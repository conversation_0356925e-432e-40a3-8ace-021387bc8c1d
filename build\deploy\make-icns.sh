#!/bin/bash

if [ -d "./build/builtin/icon-electron-macos.iconset" ]; then
    rm -rf "./build/builtin/icon-electron-macos.iconset"
fi
# 创建临时文件夹
mkdir "./build/builtin/icon-electron-macos.iconset"

# 生成不同尺寸的图片
magick ./build/builtin/icon.png -resize 16x16     "./build/builtin/icon-electron-macos.iconset/icon_16x16.png"
magick ./build/builtin/icon.png -resize 32x32     "./build/builtin/icon-electron-macos.iconset/<EMAIL>"
magick ./build/builtin/icon.png -resize 32x32     "./build/builtin/icon-electron-macos.iconset/icon_32x32.png"
magick ./build/builtin/icon.png -resize 64x64     "./build/builtin/icon-electron-macos.iconset/<EMAIL>"
magick ./build/builtin/icon.png -resize 128x128   "./build/builtin/icon-electron-macos.iconset/icon_128x128.png"
magick ./build/builtin/icon.png -resize 256x256   "./build/builtin/icon-electron-macos.iconset/<EMAIL>"
magick ./build/builtin/icon.png -resize 256x256   "./build/builtin/icon-electron-macos.iconset/icon_256x256.png"
magick ./build/builtin/icon.png -resize 512x512   "./build/builtin/icon-electron-macos.iconset/<EMAIL>"
magick ./build/builtin/icon.png -resize 512x512   "./build/builtin/icon-electron-macos.iconset/icon_512x512.png"
magick ./build/builtin/icon.png -resize 1024x1024 "./build/builtin/icon-electron-macos.iconset/<EMAIL>"

# 转换为 icns
iconutil -c icns "./build/builtin/icon-electron-macos.iconset"

# 清理临时文件
rm -rf "./build/builtin/icon-electron-macos.iconset"
