#!/bin/bash

if [ -d "./build/builtin/icon-electron-windows-ico" ]; then
    rm -rf "./build/builtin/icon-electron-windows-ico"
fi

mkdir "./build/builtin/icon-electron-windows-ico"

# 1. 生成不同尺寸的 PNG
magick ./build/builtin/icon.png -resize 16x16 ./build/builtin/icon-electron-windows-ico/icon-16.png
magick ./build/builtin/icon.png -resize 32x32 ./build/builtin/icon-electron-windows-ico/icon-32.png
magick ./build/builtin/icon.png -resize 48x48 ./build/builtin/icon-electron-windows-ico/icon-48.png
magick ./build/builtin/icon.png -resize 64x64 ./build/builtin/icon-electron-windows-ico/icon-64.png
magick ./build/builtin/icon.png -resize 128x128 ./build/builtin/icon-electron-windows-ico/icon-128.png
magick ./build/builtin/icon.png -resize 256x256 ./build/builtin/icon-electron-windows-ico/icon-256.png
# 2. 合并为 ICO
magick ./build/builtin/icon-electron-windows-ico/icon-{16,32,48,64,128,256}.png ./build/builtin/icon-electron-windows.ico

# 3. 清理临时文件
rm -rf "./build/builtin/icon-electron-windows-ico"