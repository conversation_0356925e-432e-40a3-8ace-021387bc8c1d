import {types} from '@wuk/wkp'

export const PCommonDataPack = {
  $keys: ['traceId', 'msgMaxType', 'msgMinType', 'data'],

  traceId: types.uint64,
  msgMaxType: types.uint32,
  msgMinType: types.uint32,
  data: types.bytes
}

export const ModuleMsg = {
  $keys: ['moduleId', 'msgMaxType', 'msgMinType', 'data', 'extend'],

  moduleId: types.uint32,
  msgMaxType: types.uint32,
  msgMinType: types.uint32,
  data: types.bytes,
  extend: types.mapOf(types.uint32, types.string),
}

export const PAppClientMsg = {
  $keys: ['msg', 'extend'],

  msg: ModuleMsg,
  extend: types.mapOf(types.string, types.string),
}
