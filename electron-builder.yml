productName: Constructor
artifactName: constructor-${version}-${arch}.${ext}
appId: org.builder.Constructor
asar: true
extraResources:
  - from: 'dist/cfg/'
    to: 'cfg/'
    filter:
      - '**/*'
  - from: 'dist/bin/'
    to: 'bin/'
    filter:
      - '**/*'
extraMetadata:
  main: ./dist/main/index.js
  productName: Constructor

files:
  - 'dist'
  - 'package.json'
  - 'public/icon-electron-windows.ico'
  - '!dist/**/reference.json'
  - '!dist/make-icns.sh'
  - '!dist/make-ico.sh'
  - '!dist/img-apple-*'
  - '!dist/get'
  - '!node_modules'
directories:
  output: ./output
  buildResources: ./build/builtin
nsis:
  uninstallDisplayName: Constructor
  oneClick: false
  createDesktopShortcut: true
  createStartMenuShortcut: true
  allowToChangeInstallationDirectory: true
win:
  target: nsis
  icon: build/builtin/icon-electron-windows.ico
mac:
  target:
    target: default
    arch:
      - x64
      - arm64
  entitlements: build/builtin/electron-entitlements.mac.plist
  icon: build/builtin/icon-electron-macos.icns
  notarize:
    teamId: Y54Z4K69G9
dmg:
  title: Constructor installer
  background: build/builtin/background-electron-dmg.tiff
  iconSize: 100
  contents:
    - x: 138
      y: 225
      type: file
    - x: 402
      y: 225
      type: link
      path: '/Applications'
linux:
  category: Community
  target:
    - AppImage
