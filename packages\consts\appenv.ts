import { kSdkName, PkgFile } from './configs'
import { join } from 'node:path'

export class AppEnv {
  private static readonly kRoot = 'dist'

  static get isTest() {
    return !!AppEnv.serverUrl
  }

  static get sdkname() {
    return kSdkName
  }

  static get serverUrl() {
    return process?.env?.VITE_DEV_SERVER_URL || ''
  }

  static get isRemote() {
    return AppEnv.isTest
  }

  static get icon() {
    return AppEnv.public(PkgFile.kFavicon)
  }

  static get preload() {
    return AppEnv.abs(PkgFile.kPreload)
  }

  static get root() {
    return AppEnv.abs('..')
  }

  static get main() {
    return AppEnv.dist(PkgFile.kMain)
  }

  static get login() {
    return AppEnv.dist(PkgFile.kLogin)
  }

  private static public(url: string) {
    const root = AppEnv.serverUrl || process?.env?.PUBLIC || ''
    return join(root, AppEnv.kRoot, url)
  }

  private static dist(url: string) {
    let root = AppEnv.serverUrl
    if (!root) {
      root = join(root, AppEnv.kRoot)
    }
    return join(root, url)
  }

  private static abs(url: string) {
    return join(__dirname, url)
  }
}
