/// <reference types="vite-plugin-electron/electron-env" />

declare namespace NodeJS {
  interface ProcessEnv {
    DIST: string
    /** /dist/ or /public/ */
    VITE_PUBLIC: string
    __DEV__: boolean
    __PROP__: boolean
    __PROP__: string
  }
}

// Used in Renderer process, expose in `preload.ts`
interface Window {
  ipcRenderer: import('electron').IpcRenderer
  electronAPI: {
    send: (channel: string, data: any) => void
    on: (
      channel: string,
      callback: (event: Electron.IpcRendererEvent, ...args: any[]) => void
    ) => void
  }
}
