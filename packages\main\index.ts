import {
  app,
  BrowserWindow,
  globalShortcut,
  Menu,
  MenuItem,
  ipcMain,
  protocol,
  net
} from 'electron'
import { AppEnv, kAppName, kWinWidth, kWinHeight } from '@/consts'
import { ISdk, IApp, AppItr } from '@wuk/cfg'
import { createSdk } from '@wuk/cfg/dist/node'
import { pathToFileURL } from 'node:url'
import path from 'node:path'

let mainWindow: BrowserWindow | undefined
let sdk: ISdk<IApp, AppItr> | undefined

const isDarwin = () => (process?.env?.NODE_PLAT || process.platform) === 'darwin'

const createWindow = () => {
  const preload = AppEnv.preload
  const result: BrowserWindow = new BrowserWindow({
    title: kAppName,
    icon: AppEnv.icon,
    width: kWinWidth,
    height: kWinHeight,
    minWidth: kWinWidth,
    minHeight: kWinHeight,
    // frame: false,
    titleBarStyle: 'hidden',
    titleBarOverlay: {
      color: 'rgba(0, 0, 0, 0)',
      height: 58,
      symbolColor: 'rgba(0, 0, 0, 255)'
    },
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: true,
      enableWebSQL: false,
      preload
    }
  })
  result.setMenuBarVisibility(false)

  const url = AppEnv.main
  AppEnv.isRemote ? result.loadURL(url) : result.loadFile(url)
  // if (AppEnv.isTest) {
  result.webContents.openDevTools({ mode: 'detach', activate: true })
  // }

  return result
}

const createMainWindow = () => {
  mainWindow = createWindow()
  const path = AppEnv.root
  const name = AppEnv.sdkname
  sdk = createSdk(
    {
      name,
      path
    },
    mainWindow
  )
  mainWindow.once('closed', () => {
    globalShortcut.unregisterAll()
    sdk?.stop()
    sdk?.destroy()
    sdk = undefined
    mainWindow = undefined
  })
  sdk?.start()

  if (AppEnv.isTest) {
    globalShortcut.register('F11', () => {
      mainWindow?.webContents.openDevTools({ mode: 'detach', activate: true })
    })
  }
}
protocol.registerSchemesAsPrivileged([
  {
    scheme: 'app',
    privileges: {
      standard: true,
      secure: true,
      supportFetchAPI: true
    }
  }
])

app.setName(kAppName)
app.once('ready', () => {
  createMainWindow()
  protocol.handle('app', req => {
    const app = sdk?.proxy
    const { host, pathname } = new URL(req.url)
    const customer = app?.cfg?.customer
    if (host !== customer || !customer) {
      return new Response('bad', {
        status: 404,
        headers: { 'content-type': 'text/html' }
      })
    }

    const dirname = app?.cfg?.cfgDir
    const pathToServe = path.join(dirname, customer, pathname)
    return net.fetch(pathToFileURL(pathToServe).toString())
  })
})

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0 || !mainWindow) {
    createMainWindow()
  }
})

app.on('window-all-closed', () => {
  if (!isDarwin()) {
    app.quit()
  }
})
