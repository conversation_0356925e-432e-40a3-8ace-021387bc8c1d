import { IPage, IApp, AppItr } from '@wuk/cfg'
import { createPage } from '@wuk/cfg/dist/node'
import { AppEnv } from '@/consts'
import { contextBridge, ipcRenderer } from 'electron'

const page: IPage<IApp, AppItr> = createPage({
  id: '1',
  name: AppEnv.sdkname
})

window.addEventListener('DOMContentLoaded', () => {
  const replaceText = (selector: string, text: string) => {
    const element = document.getElementById(selector)
    if (element) element.innerText = text
  }

  for (const dependency of ['chrome', 'node', 'electron']) {
    replaceText(`${dependency}-version`, process.versions[dependency] || '')
  }
})

contextBridge.exposeInMainWorld('electronAPI', {
  send: (channel: string, data: any) => ipcRenderer.send(channel, data),
  on: (channel: string, callback: (event: Electron.IpcRendererEvent, ...args: any[]) => void) => {
    ipcRenderer.on(channel, callback)
  }
})
