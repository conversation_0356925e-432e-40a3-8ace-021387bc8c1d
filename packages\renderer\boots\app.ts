import { IA<PERSON>, IWin, Platform } from '@wuk/cfg'
import 'mavon-editor/dist/css/index.css'
import * as vue from 'vue'
import { Timer } from './interface'
import { LogImpl } from './log'
import { RootLogic } from './logic'
import { QueuesImpl } from './queue'
import Render from './render'
import { TimersImpl } from './timers'

export type RootNode = vue.Component
export type RootPlugin = vue.Plugin

export default class App {
  private static _name: string
  private static _root: vue.App
  private static _render: Render
  private static _logic: RootLogic
  private static _timer: Timer
  private static _log: LogImpl
  private static _queues: QueuesImpl

  // static readonly account = new Account()

  static init(name: string): void {
    App._name = name
    App._render = new Render()
    App._logic = new RootLogic()
    App._timer = new TimersImpl()
    App._log = new LogImpl(console)
    App._queues = new QueuesImpl()

    App._render.init()
    App._logic.init()
  }

  static run(dom: HTMLElement, node: RootNode, plugins?: RootPlugin[]) {
    App._root = vue.createApp(node)
    plugins?.forEach(plugin => App._root.use(plugin))
    App._root.mount(dom)
  }

  static start() {
    App._render?.start()
    App._logic?.start()
  }

  static stop() {
    App._logic?.stop()
    App._render?.stop()
  }

  static destroy() {
    App._logic?.destroy()
    App._render?.destroy()
  }

  static get logic() {
    return App._logic
  }

  static get render() {
    return App._render
  }

  static get timer() {
    return App._timer
  }

  static get log() {
    return App._log
  }

  static get queues() {
    return App._queues
  }

  static get sdk(): IApp | undefined {
    return window.cfgsdk
  }

  static get win(): IWin | undefined {
    return App.sdk?.win
  }

  static get platform(): Platform {
    return App.win?.platform || 'linux'
  }

  static get isDarwin() {
    return App.platform === 'darwin'
  }

  static error(title: string, centent = '', ...args: any[]) {
    return App._log.log('Boots.Error', title, centent, ...args)
  }

  static info(tag: string, title: string, centent = '', ...args: any[]) {
    return App._log.log(tag, title, centent, ...args)
  }
}
