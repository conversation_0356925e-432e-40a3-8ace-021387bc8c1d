import { ExNodeClass, ExNodeTag, Excuter } from '@wuk/cfg'
import { BaseEntry, useEntry } from './Entry'
import App from '../app'

export const TargetClass =
  <T extends Excuter<T>, R extends Excuter<R>>(
    { key }: ExNodeTag,
    cls?: ExNodeClass<R>,
    up?: boolean
  ) =>
  (target: ExNodeClass<T>) => {
    target.key = key
    target.prototype.debug = (tag: string, content: string) => {
      App.info('App.Load', tag, content)
    }

    const root = (cls && App.logic.as(cls)) || undefined
    root?.add(target)

    up && root?.up({ key })
  }

export class TargetEntry<T extends BaseEntry = BaseEntry> extends BaseEntry<T> {
  constructor(key: string) {
    super(App, key)
  }

  get app() {
    return App
  }
}

@TargetClass({ key: 'app.RootCore' })
export class RootCore extends Excuter<RootCore> {
  constructor() {
    super(RootCore.key)
  }
}

@TargetClass({ key: 'app.RootApi' })
export class Root<PERSON>pi extends Excuter<RootApi> {
  constructor() {
    super(RootApi.key)
  }
}

@TargetClass({ key: 'app.RootFrame' })
export class RootFrame extends Excuter<RootFrame> {
  constructor() {
    super(RootFrame.key)
  }
}

export class RootLogic extends Excuter<RootLogic> {
  static key = 'app.RootLogic'

  private _core: RootCore
  private _api: RootApi
  private _frame: RootFrame

  constructor() {
    super(RootLogic.key)

    this._api = this.add(RootApi)
    this._core = this.add(RootCore)
    this._frame = this.add(RootFrame)
  }

  async init(...args: any[]) {
    await super.init(...args)
    await this.up(RootApi)
    await this.up(RootCore)
    await this.up(RootFrame)
  }

  get core() {
    return this._core
  }

  get api() {
    return this._api
  }

  get frame() {
    return this._frame
  }
}

export const useTarget = <T extends TargetEntry<T>>(cls: ExNodeTag): T | undefined =>
  useEntry<T>(App.logic.core, cls)
