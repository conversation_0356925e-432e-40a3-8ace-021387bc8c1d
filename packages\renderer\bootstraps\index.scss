$namespace: 'cfg';
html,
body {
  width: 100% !important;
  height: 100%;
  margin: 0;
  overflow: hidden;
  font-family: 'PingFang SC', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
}

#root {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.titlebar {
  display: flex;
  align-items: center;
  flex-shrink: 0;
  height: 35px;
  width: 100%;
  z-index: 9999;

  padding-left: 12px;
  font-size: 14px;
}

.content {
  overflow: auto;
}
@mixin cfg-button {
  .wui-button {
    width: 66px;
    height: 32px;
  }
  .wui-button--primary {
    --wui-button-bg-color: #6282c1;
    --wui-button-border-color: #3c5992;
    --wui-button-active-bg-color: #6282c1;
    --wui-button-active-border-color: #3c5992;
    --wui-button-hover-border-color: #3c5992;
    --wui-button-hover-bg-color: rgba(98, 130, 193, 0.7);
  }
  .wui-button--info.is-plain {
    width: 83px;
    height: 32px;
    --wui-button-bg-color: #f0f0f0;
    --wui-button-border-color: #b6bece;
    --wui-button-text-color: #3d3d3d;
  }
}
.#{$namespace} {
  &-submit {
    margin-top: 20px;
    text-align: right;
    @include cfg-button;
  }
  &-btn-box {
    @include cfg-button;
  }
  &-setup {
    display: flex;
    flex-direction: column;
    height: 100%;
    &_table {
      // overflow: auto;
      flex: 1;
      // 关键：初始大小为剩余空间，而不是向外扩展
      flex-basis: auto;
      height: 0;
      .wui-form-item {
        margin-bottom: 0;
      }
      .wui-input {
        width: 100%;
        height: 32px;
      }
      .wui-input-number {
        width: 100%;
      }
      .wui-select {
        width: 100%;
        height: 32px;
        margin-left: 0;
      }
      .wui-table {
        thead th {
          &.wui-table__cell {
            background: #eaf1fd;
            color: #90afe4;
            font-weight: bold;
            font-size: 18px;
          }
        }
      }
    }
  }
}

.wui-table {
  color: #3d3d3d;
  font-size: 18px;
  font-weight: bold;
  --wui-table-row-hover-bg-color: #b0cdff;
  .wui-form-item__error {
    padding-bottom: 2px;
    margin-left: 0;
  }
}

.wui-anchor {
  margin-top: 40px;
}
.wui-anchor__item {
  height: 50px;
}
.wui-anchor__link {
  font-size: 14px !important;
  font-weight: normal;
  line-height: 41px;
  padding-left: 20px;
  color: #3d3d3d;
}
.wui-anchor__link:focus {
  color: #12151a;
}
.wui-anchor__link:hover {
  color: #12151a;
  background-color: #e6efff;
}
.wui-anchor__link.is-active {
  background-color: #b0cdff;
  color: #12151a;
  font-size: 18px !important;
  font-weight: 500;
  height: 100%;
  line-height: 41px;
  padding-left: 20px;
}
.wui-anchor__marker {
  display: none;
}
.wui-anchor.wui-anchor--vertical .wui-anchor__list {
  padding-left: 0;
}

.wui-divider--horizontal {
  width: 98%;
  margin: 20px 0;
}
.wui-select {
  width: 230px;
  height: 32px;
}
.wui-select__wrapper {
  &:hover {
    box-shadow: 0 0 0 1px #b0cdff !important;
  }
}
.wui-input {
  width: 230px;
  height: 32px;
}
.wui-input__inner {
  height: 40px;
}

.wui-input__icon {
  font-size: 16px;
  color: #444444;
}
.wui-icon {
  cursor: pointer;
  &:hover {
    color: #0c65ff;
  }
}
.wui-popconfirm__action {
  display: flex;
  justify-content: center;
  .wui-button:nth-child(1) {
    display: block !important;
    background-color: #f0f0f0 !important;
    border: 1px solid #b6bece !important;
    color: #3d3d3d !important;
    &:hover {
      background-color: #909399 !important;
      color: #ffffff !important;
    }
  }
  .wui-button:nth-child(2) {
    background-color: #6282c1 !important;
    border: 1px solid #3c5992 !important;
    &:hover {
      background-color: rgba(98, 130, 193, 0.7) !important;
    }
  }
}

.wui-dialog__title {
  font-size: 14px !important;
  font-weight: bold;
}

// 单行注释
.cfg-sle {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
