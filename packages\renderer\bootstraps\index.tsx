import { createBoots } from '@/renderer/boots'
import { loadMain } from '@/renderer/logic'

import Wui from '@wuk/wui'
import '@wuk/wui/dist/style.css'
import 'mavon-editor/dist/css/index.css'
import './index.scss'

import App from '@/renderer/page'
import mainRouter from '@/renderer/page/main/router'
import mavonEditor from 'mavon-editor'
createBoots('主页', document.getElementById('root')!, App, loadMain, [Wui, mainRouter, mavonEditor])
