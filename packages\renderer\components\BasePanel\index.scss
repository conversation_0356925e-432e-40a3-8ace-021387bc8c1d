$prefix: 'scena-base-panel';
.#{$prefix} {
  background-color: #fff;
  pointer-events: all;
  margin-bottom: 10px;
  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 10px;
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
    height: 40px;
    &-title {
      width: calc(100% - 20px);
      display: flex;
      font-size: 14px;
      font-weight: bold;
      justify-content: space-between;
      align-items: center;
    }
    &-search.wui-input {
      width: auto;
      margin-left: 20px;
      height: 30px;
    }
    &-divider {
      width: 100%;
      height: 1px;
      background-color: #e0e0e0;
    }
    &-actions {
      display: flex;
      align-items: center;
      justify-content: center;
    }
    &-create {
      cursor: pointer;
      padding: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    &-close {
      cursor: pointer;
      padding: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    &-hidden {
      cursor: pointer;
      padding: 4px;

    }
  }
  &-content {
    overflow: auto;
    margin-top: 10px;

    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #c1c1c1;
      border-radius: 4px;

      &:hover {
        background-color: #a8a8a8;
      }
    }
  }
}
