import { defineComponent, ref } from 'vue'
import { PlusIcon, SvgIcon } from '../editor/icon/icons'
import { prefix } from '../editor/utils'
import './index.scss'

export type BasePanelDefaultSlot = {
  searchKey: string
}
export type BasePanelExpose = {
  handleSearch: (val?: string) => void
}

export const BasePanel = defineComponent({
  name: 'BasePanel',
  props: {
    title: {
      type: String,
      default: ''
    },
    onCreate: {
      type: Function,
      required: false,
      default: undefined
    },
    height: {
      type: String,
      default: '24vh'
    },
    isSearch: {
      type: Boolean,
      required: false,
      default: false
    }
  },
  setup(props, { slots, expose }) {
    const isCollapsed = ref(false)
    const searchKey = ref<BasePanelDefaultSlot['searchKey']>('')
    const handleCreate = () => {
      if (props.onCreate) {
        props.onCreate()
      }
    }
    const toggleCollapse = () => {
      isCollapsed.value = !isCollapsed.value
    }
    const handleSearch = (val?: string) => {
      searchKey.value = val || ''
    }
    expose<BasePanelExpose>({
      handleSearch
    })
    return () => (
      <div class={prefix('base-panel')}>
        <div class={prefix('base-panel-header')}>
          <div class={prefix('base-panel-header-title')}>
            <span>{props.title}</span>
            {props.isSearch && (
              <wui-input
                class={prefix('base-panel-header-search')}
                model-value={searchKey.value}
                onUpdate:modelValue={handleSearch}
                placeholder={`Search ${props.title.substring(0, props.title.length - 1)}`}
                clearable
              />
            )}
          </div>
          <div class={prefix('base-panel-header-actions')}>
            {props.onCreate && (
              <div class={prefix('base-panel-header-create')} onClick={handleCreate}>
                <PlusIcon style={{ width: '1.2em', height: '1.2em' }} />
              </div>
            )}
            {
              <div class={prefix('base-panel-header-hidden')} onClick={toggleCollapse}>
                {isCollapsed.value ? (
                  <SvgIcon style={{ width: '1.2em', height: '1.2em' }}>
                    <path d='M7 10l5 5 5-5' stroke='currentColor' stroke-width='2' fill='none' />
                  </SvgIcon>
                ) : (
                  <SvgIcon style={{ width: '1.2em', height: '1.2em' }}>
                    <path d='M7 15l5-5 5 5' stroke='currentColor' stroke-width='2' fill='none' />
                  </SvgIcon>
                )}
              </div>
            }
          </div>
        </div>
        <div class={prefix('base-panel-header-divider')}></div>

        <div
          class={prefix('base-panel-content')}
          style={{
            height: isCollapsed.value ? '0' : props.height,
            overflow: 'hidden',
            transition: 'height 0.3s',
            overflowY: 'auto'
          }}>
          {slots.default?.({ searchKey: searchKey.value })}
        </div>
      </div>
    )
  }
})
