import { InjectionKey } from 'vue'
import { Tree } from '../../TreeContent'
import { TableData, TableLib } from '@wuk/cfg'
import { useBiz } from './hooks'
import { BizEngine, BizMain } from '@/renderer/logic'

export interface CalcTableTreeChild extends Omit<Tree<number>, 'children'> {
  originData: {
    group_name: string
    tables: Array<TableData>
  }
}
export interface CalcTableTree extends Tree<number> {
  children?: CalcTableTreeChild[]
}

export type CurTableGroupInfo = {
  groupName: string
  calcTableId: number
  index?: number
  groupNodeIndex: number
  children?: CalcTableTreeChild[]
}

export type CurTableEqInfo = {
  tableIndex: number
  item: Partial<TableData>
  tableLib?: TableLib
}

interface CalcTableContext {
  curTableGroupInfo: CurTableGroupInfo
  curTableEqInfo: CurTableEqInfo
  changeTreeNode: (id: number) => void
  bizTable: BizMain | BizEngine | undefined
  BizTable: typeof BizMain | typeof BizEngine
}

export const TableMode = <const>{
  Special: 'Special',
  Common: 'Common'
}
export type TableModeType = (typeof TableMode)[keyof typeof TableMode]
export const calcTableContextKey: InjectionKey<CalcTableContext> = Symbol('calcTableContextKey')
