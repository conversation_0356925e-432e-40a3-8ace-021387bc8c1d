import { defineComponent, PropType, ref, computed, watchEffect, useModel } from 'vue'
import { FormModel, TableType } from '../editRow'
import CodeEditor from '@/renderer/components/CodeEditor'
import $styles from './index.module.scss'
import { useBem } from '@/renderer/hooks'
import { useParameterDialog } from '@/renderer/utils/common'
export default defineComponent({
  name: 'EditRowCode',
  props: {
    formModel: {
      type: Object as PropType<FormModel>,
      required: true
    },
    codeTxt: {
      type: String,
      default: ''
    }
  },
  emits: ['update:codeTxt'],
  setup(props) {
    const { b } = useBem('editrow', $styles)
    const codeTxt = useModel(props, 'codeTxt')
    const handleSelectParamter = (formModel: Record<string, any>, key: string) => {
      return async () => {
        const res = await useParameterDialog()
        formModel[key] = res
      }
    }
    const isThreeD = computed(() => props.formModel.tableType === TableType['3D'])
    return () => {
      const { output, xInput, yInput } = props.formModel
      return (
        <div class={b('code')}>
          <div class={b('code_header')}>
            <div>Output: {output}</div>
            {isThreeD.value && (
              <div>
                {`Y Input: `}
                <wui-button onClick={handleSelectParamter(props.formModel, 'yInput')}>
                  {yInput || 'None'}
                </wui-button>
              </div>
            )}
            <div>
              {`X Input: `}
              <wui-button onClick={handleSelectParamter(props.formModel, 'xInput')}>
                {xInput || 'None'}
              </wui-button>
            </div>
          </div>
          <CodeEditor v-model:code={codeTxt.value} height='100%' />
        </div>
      )
    }
  }
})
