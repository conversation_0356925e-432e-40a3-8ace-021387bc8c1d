import { defineComponent, PropType, toRef } from 'vue'
import { CorrData, FormModel, TableType } from '../editRow'
import { WuiButton, WuiTable, WuiTableColumn } from '@wuk/wui'
import { useBem, useTableCommonMenu } from '@/renderer/hooks'
import $styles from './index.module.scss'
import { useParameterDialog } from '@/renderer/utils/common'
import TableTool from '@/renderer/components/TableTool'
export default defineComponent({
  name: 'EditRowTable',
  props: {
    formModel: {
      type: Object as PropType<FormModel>,
      required: true
    }
  },
  setup(props) {
    const { b, e } = useBem('editrow', $styles)
    const corrData = toRef(props.formModel, 'corrData')
    const handleEdit = (row: CorrData | undefined, index = -1) => {
      const newRow = {
        coord1: null,
        coord2: null,
        coord3: null
      }
      if (index === -1) {
        corrData.value.push(newRow)
      } else {
        corrData.value.splice(index, 0, newRow)
      }
    }
    const { handleRowMenu } = useTableCommonMenu(
      corrData,
      (key, ...args) => {
        const { row, rowIndex } = args[0]
        switch (key) {
          case 'addKey':
            handleEdit(row)
            break
          case 'insertKey':
            handleEdit(row, rowIndex + 1)
            break
          case 'deleteKey':
            corrData.value.splice(rowIndex, 1)
            break
          default:
            break
        }
      },
      [1],
      [
        { key: 'addKey', label: 'add' },
        { key: 'insertKey', label: 'insert' },
        { key: 'deleteKey', label: 'delete' }
      ]
    )
    const handleSelectParamter = (formModel: any, key: string) => {
      return async () => {
        const res = await useParameterDialog()
        formModel[key] = res
      }
    }
    return () => {
      const { formModel } = props
      const isThreeD = formModel.tableType === TableType['3D']
      const xProp = isThreeD ? 'coord3' : 'coord2'
      return (
        <WuiTable
          onRow-contextmenu={handleRowMenu}
          height='100%'
          maxHeight='185px'
          data={corrData.value}
          border
          class={e('table')}>
          {{
            default: () => (
              <>
                <WuiTableColumn prop='coord1' align='center'>
                  {{
                    default: ({ row }: any) => (
                      <wui-input-number
                        v-model={row.coord1}
                        input-align='left'
                        clearable
                        controls={false}
                        placeholder='Please Input'
                      />
                    ),
                    header: () => <span>Output: {formModel.output || 'None'}</span>
                  }}
                </WuiTableColumn>
                <WuiTableColumn prop={xProp} align='center'>
                  {{
                    default: ({ row }: any) => (
                      <wui-input-number
                        v-model={row[xProp]}
                        input-align='left'
                        clearable
                        controls={false}
                        placeholder='Please Input'
                      />
                    ),
                    header: () => (
                      <div>
                        X Input:
                        <WuiButton onClick={handleSelectParamter(formModel, 'xInput')}>
                          {formModel.xInput || 'None'}
                        </WuiButton>
                      </div>
                    )
                  }}
                </WuiTableColumn>
                {isThreeD && (
                  <WuiTableColumn prop='coord2' align='center'>
                    {{
                      default: ({ row }: any) => (
                        <wui-input-number
                          v-model={row.coord2}
                          input-align='left'
                          clearable
                          controls={false}
                          placeholder='Please Input'
                        />
                      ),
                      header: () => (
                        <div>
                          Y Input:
                          <WuiButton onClick={handleSelectParamter(formModel, 'yInput')}>
                            {formModel.yInput || 'None'}
                          </WuiButton>
                        </div>
                      )
                    }}
                  </WuiTableColumn>
                )}
              </>
            ),
            empty: () => <TableTool.Empty />
          }}
        </WuiTable>
      )
    }
  }
})
