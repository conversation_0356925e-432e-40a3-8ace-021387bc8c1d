import { computed } from 'vue'
import { useBizEngine, useBizMain } from '@/renderer/hooks'
import { TableMode, TableModeType } from '../constants'
import { BizEngine, BizMain } from '@/renderer/logic'

/**
 * @description 决定接口调用的模块 main | engine
 */
export const useBiz = (mode: TableModeType) => {
  const bizEngine = useBizEngine()
  const bizMain = useBizMain()
  const bizTable = computed(() => (mode === TableMode.Common ? bizMain.value : bizEngine.value))
  const BizTable = computed(() => (mode === TableMode.Common ? BizMain : BizEngine))
  return {
    bizTable,
    BizTable
  }
}
