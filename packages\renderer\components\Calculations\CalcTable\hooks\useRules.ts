import { FormRules } from '@wuk/wui'
import { inject, Ref } from 'vue'
import { calcTableContextKey } from '../constants'

export const useGroupRules = (curEditIndex: Ref<number>) => {
  const calcTableContext = inject(calcTableContextKey)
  const groupRules: FormRules = {
    group_name: [
      { required: true, message: 'Please input group name', trigger: 'change' },
      {
        trigger: 'change',
        validator(_, value, callback) {
          const { children = [] } = calcTableContext?.curTableGroupInfo || {}
          const newList =
            curEditIndex.value === -1
              ? children
              : children.filter((_, index) => index !== curEditIndex.value)
          const isUnique = newList.some(item => item.label === value)
          if (isUnique) {
            callback('The groupName must be unique')
            return
          }
          callback()
        }
      }
    ]
  }
  return {
    groupRules
  }
}
export const useEquationRules = () => {
  const equationRules: FormRules = {
    name: [{ required: true, message: 'Please input equation name', trigger: 'change' }],
    equation: [{ required: true, message: 'Please input equation', trigger: 'change' }]
  }
  return {
    equationRules
  }
}
