import { defineComponent, FunctionalComponent, inject, reactive, toRef, watchEffect } from 'vue'
import { EquationHeader } from '../../CalcTool'
import { useHandler, usePermanentFocus, useTableCommonMenu } from '@/renderer/hooks'
import TableTool, { BaseTableRow, isAddOrInsertType, OpType } from '@/renderer/components/TableTool'
import { ref } from 'vue'
import { calcTableContextKey } from '../constants'
import { WuiForm, WuiMessage } from '@wuk/wui'
import { useEquationRules } from '../hooks'
import { cloneFnJSON } from '@vueuse/core'
import { TableCfg } from '@wuk/cfg'
import EditRow from '../editRow'

type EquationTitleProps = {
  label?: string
}
type EquationTableGroupRow = BaseTableRow<{
  name: string
  equation: string
}>
const { handleInputClick, cancelPermanentFocus, handleSelect, handleSelectParam } =
  usePermanentFocus<any>()

const Table = defineComponent({
  name: 'SetupEquation',
  setup() {
    const calcTableContext = inject(calcTableContextKey)
    if (!calcTableContext) return
    const equationFormRef = ref<InstanceType<typeof WuiForm>>()
    const model = reactive({
      list: [] as EquationTableGroupRow[]
    })
    const showEditRow = ref(false)
    const tableCfgList = ref<TableCfg>()
    const { equationRules } = useEquationRules()
    function createRow(
      row_type: EquationTableGroupRow['row_type'],
      flag = true
    ): EquationTableGroupRow {
      return {
        flag,
        name: '',
        equation: '',
        row_type
      }
    }
    const tipsMessage = () => {
      WuiMessage({
        message: 'success',
        type: 'success',
        offset: 90,
        grouping: true
      })
    }
    const { handleRowMenu } = useTableCommonMenu(
      toRef(model, 'list'),
      async (key, ...args) => {
        if (!calcTableContext) return
        const { row, rowIndex } = args[0]
        switch (key) {
          case 'addKey':
            model.list.push(createRow('add'))
            break
          case 'insertKey':
            model.list.splice(rowIndex + 1, 0, createRow('insert'))
            break
          case 'modifyKey':
            handleOp('edit', row, rowIndex)
            break
          case 'deleteKey':
            handleOp('delete', row, rowIndex)
            break
          case 'tableValuesKey':
            if (!row) return
            showEditRow.value = true
            getTableLib(row.name)
            break
        }
        const { curTableGroupInfo, curTableEqInfo } = calcTableContext || {}
        const { groupNodeIndex = -1, children = [] } = curTableGroupInfo
        if (groupNodeIndex === -1) return
        curTableEqInfo.item = children[groupNodeIndex].originData.tables[rowIndex]
        curTableEqInfo.tableIndex = rowIndex
      },
      [1],
      [
        { key: 'addKey', label: 'add' },
        { key: 'insertKey', label: 'insert' },
        { key: 'modifyKey', label: 'modify' },
        { key: 'deleteKey', label: 'delete' },
        { key: 'tableValuesKey', label: 'table values' }
      ]
    )
    const handleOp = (op: OpType, row: EquationTableGroupRow | undefined, index: number) => {
      if (!row) return
      switch (op) {
        case 'edit':
          row.flag = true
          break
        case 'delete':
          handleDelete(index)
          break
        case 'cancel':
          handleOpCancel(row, index)
          break
        case 'select':
          handleSelect(row, index)
          break
      }
    }
    async function handleSelect(row: EquationTableGroupRow, index: number) {
      const valid = await equationFormRef.value?.validateField([
        `list.${index}.name`,
        `list.${index}.equation`
      ])
      if (!valid) return
      if (!calcTableContext) return
      const { curTableGroupInfo, bizTable } = calcTableContext
      const { groupNodeIndex, groupName, children = [] } = curTableGroupInfo || {}
      const { flag, row_type, ...reset } = row
      let res: boolean | undefined
      const params = cloneFnJSON({
        ...children[groupNodeIndex].originData.tables[index],
        name: reset.name,
        exp: reset.equation
      })
      if (row_type === 'add') {
        res = await bizTable?.addTable(groupName, params, index)
      } else {
        res = await bizTable?.modifyTable(groupName, index, params)
      }
      if (!res) return
      row.flag = false
      row.row_type = '*'
      tipsMessage()
    }

    async function handleDelete(index: number) {
      if (!calcTableContext) return
      const { curTableGroupInfo, bizTable } = calcTableContext
      const { groupName } = curTableGroupInfo || {}
      const res = await bizTable?.removeTable(groupName, index)
      if (!res) return
      tipsMessage()
    }

    const handleOpCancel = (row: EquationTableGroupRow, index: number) => {
      if (isAddOrInsertType(row.row_type)) {
        model.list.splice(index!, 1)
        return
      }
      const children = calcTableContext?.curTableGroupInfo.children || []
      const { name, exp } = children[index].originData.tables[index]
      row.name = name
      row.equation = exp
      row.flag = false
    }

    /**
     * @description 查询对应tables的corr数据
     */
    const getTableLib = (tableName: string) => {
      const { tables = [] } = tableCfgList.value || {}
      calcTableContext.curTableEqInfo.tableLib = tables.find(item => item.name === tableName)
    }

    const columns = [
      {
        label: 'Name',
        prop: 'name',
        align: 'center',
        isPermanentFocus: false
      },
      {
        label: 'Equation',
        prop: 'equation',
        align: 'center',
        'show-overflow-tooltip': true,
        isPermanentFocus: true
      }
    ]
    const getTableCfg = async (groupName?: string) => {
      if (!groupName) return
      tableCfgList.value = await calcTableContext.bizTable?.readTableCfg(groupName)
    }

    /**
     * @description reset form
     */
    const handleReset = () => {
      const { groupNodeIndex, groupName, children } = calcTableContext?.curTableGroupInfo || {}
      if (groupNodeIndex === undefined || !children) return
      model.list = children[groupNodeIndex].originData.tables.map(item => ({
        name: item.name,
        equation: item.exp,
        flag: false,
        row_type: '*'
      }))
      getTableCfg(groupName)
    }

    useHandler(
      toRef(calcTableContext, 'bizTable'),
      calcTableContext?.BizTable.onTableCfgChanged,
      () => {
        const { groupName } = calcTableContext?.curTableGroupInfo || {}
        getTableCfg(groupName)
      }
    )

    const RenderFormTableColumn = () => {
      return (
        <>
          {columns.map(({ isPermanentFocus, ...column }) => (
            <wui-table-column {...column}>
              {{
                default: ({ row, $index }: any) => {
                  const clickHandler = isPermanentFocus
                    ? (ev: MouseEvent) => handleInputClick(row, column.prop, ev)
                    : (ev: MouseEvent) => cancelPermanentFocus(ev)
                  return (
                    <>
                      {row.flag ? (
                        <wui-form-item
                          prop={`list.${$index}.${column.prop}`}
                          rules={equationRules[column.prop]}>
                          <wui-input
                            v-model={row[column.prop]}
                            type='text'
                            onClick={clickHandler}
                            placeholder='Please input'
                          />
                        </wui-form-item>
                      ) : (
                        row[column.prop]
                      )}
                    </>
                  )
                }
              }}
            </wui-table-column>
          ))}
        </>
      )
    }
    watchEffect(handleReset)
    return () => (
      <>
        <wui-form
          ref={equationFormRef}
          label-width='0'
          label-position='left'
          hide-required-asterisk
          inline-message
          validate-box-gap='3'
          validate-placement='bottom'
          model={model}
          rules={equationRules}
          class='cfg-setup'>
          <div class='cfg-setup_table'>
            <wui-table border height='100%' data={model.list} onRow-contextmenu={handleRowMenu}>
              {{
                default: () => (
                  <>
                    <wui-table-column label='No.' type='index' width='80px' align='center' />
                    <RenderFormTableColumn />
                    <wui-table-column label='Op' width='100px' align='center'>
                      {{
                        default: ({ row, $index }: any) => (
                          <TableTool.Op flag={row.flag} onOp={op => handleOp(op, row, $index)} />
                        )
                      }}
                    </wui-table-column>
                  </>
                ),
                empty: () => <TableTool.Empty />
              }}
            </wui-table>
          </div>
        </wui-form>
        <EditRow v-model:showEditRow={showEditRow.value} />
      </>
    )
  }
})

const Header: FunctionalComponent<EquationTitleProps> = props => {
  return (
    <EquationHeader
      style={{ '--calc-tool-gap': '120px' }}
      onSelect={handleSelect}
      onSelectParam={handleSelectParam}>
      <div>Calculation Group: {props.label}</div>
    </EquationHeader>
  )
}
Header.props = {
  label: {
    type: String,
    default: ''
  }
}

const SetupEquation = {
  Table,
  Header
}
export default SetupEquation
