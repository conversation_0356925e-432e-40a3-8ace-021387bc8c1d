import TableTool, { BaseTableRow, isAddOrInsertType, OpType } from '@/renderer/components/TableTool'
import {
  defineComponent,
  FunctionalComponent,
  inject,
  reactive,
  ref,
  toRef,
  watchEffect
} from 'vue'
import { calcTableContextKey } from '../constants'
import { useTableCommonMenu } from '@/renderer/hooks'
import { cloneFnJSON } from '@vueuse/core'
import { uuid } from '@wuk/cfg'
import { useGroupRules } from '../hooks'
import { WuiForm, WuiMessage } from '@wuk/wui'
export type TableGroupRow = BaseTableRow<{
  group_name: string
  id: number
}>
type TableGroupDefaultScope = {
  row: TableGroupRow
  $index: number
}
const Table = defineComponent({
  name: 'SetupGroupTable',
  setup() {
    const calcTableContext = inject(calcTableContextKey)
    const model = reactive({
      list: [] as TableGroupRow[]
    })
    const curEditIndex = ref(-1)
    const groupFormRef = ref<InstanceType<typeof WuiForm>>()
    const { groupRules } = useGroupRules(curEditIndex)
    function createRow(row_type: TableGroupRow['row_type'], flag = true): TableGroupRow {
      return {
        flag,
        group_name: '',
        id: uuid(),
        row_type
      }
    }
    const tipsMessage = () => {
      WuiMessage({
        message: 'success',
        type: 'success',
        offset: 90,
        grouping: true
      })
    }
    const { handleRowMenu } = useTableCommonMenu(
      toRef(model, 'list'),
      async (key, ...args) => {
        const { rowIndex, row } = args[0]
        switch (key) {
          case 'addKey':
            model.list.push(createRow('add'))
            break
          case 'insertKey':
            model.list.splice(rowIndex + 1, 0, createRow('insert'))
            break
          case 'modifyKey':
            handleOp('edit', row, rowIndex)
            break
          case 'deleteKey':
            handleOp('delete', row, rowIndex)
            break
          case 'equationKey':
            row && calcTableContext?.changeTreeNode(row.id)
            break
        }
      },
      [1],
      [
        {
          key: 'addKey',
          label: 'add'
        },
        {
          key: 'insertKey',
          label: 'insert'
        },
        {
          key: 'modifyKey',
          label: 'modify'
        },
        {
          key: 'deleteKey',
          label: 'delete'
        },
        {
          key: 'equationKey',
          label: 'equation Editor'
        }
      ]
    )

    const handleOp = (op: OpType, row: TableGroupRow | undefined, index: number) => {
      if (!row) return
      switch (op) {
        case 'edit':
          curEditIndex.value = index
          row.flag = true
          break
        case 'delete':
          handleDelete(index)
          break
        case 'cancel':
          handleOpCancel(row, index)
          break
        case 'select':
          handleSelect(row, index)
          break
      }
    }

    async function handleSelect(row: TableGroupRow, index: number) {
      const valid = await groupFormRef.value?.validateField(`list.${index}.group_name`)
      if (!valid) return
      if (!calcTableContext) return
      const { row_type, group_name } = row
      let res: boolean | undefined
      const { bizTable, curTableGroupInfo } = calcTableContext
      if (row_type === 'add') {
        res = await bizTable?.addTableGroup(
          cloneFnJSON({
            name: group_name,
            tables: []
          }),
          index
        )
      } else {
        const children = curTableGroupInfo.children || []
        const { tables } = children[index].originData
        res = await bizTable?.modifyTableGroup(
          index,
          cloneFnJSON({
            name: group_name,
            tables
          })
        )
      }
      if (!res) return
      row.flag = false
      curEditIndex.value = -1
      row.row_type = '*'
      tipsMessage()
    }

    async function handleDelete(index: number) {
      if (!calcTableContext) return
      const res = await calcTableContext.bizTable?.removeTableGroup(index)
      if (!res) return
      tipsMessage()
    }

    const handleOpCancel = (row: TableGroupRow, index: number) => {
      if (isAddOrInsertType(row.row_type)) {
        model.list.splice(index!, 1)
        return
      }
      const children = calcTableContext?.curTableGroupInfo.children || []
      const { group_name } = children[index].originData
      row.group_name = group_name
      row.flag = false
    }

    watchEffect(() => {
      const { children = [], groupName } = calcTableContext?.curTableGroupInfo || {}
      model.list = children.map(item => ({
        group_name: item.label,
        flag: false,
        row_type: '*',
        id: item.id
      }))
    })
    return () => (
      <>
        <wui-form
          ref={groupFormRef}
          label-width='0'
          label-position='left'
          hide-required-asterisk
          inline-message
          validate-box-gap='3'
          validate-placement='bottom'
          class='cfg-setup'
          rules={groupRules}
          model={model}>
          <div class='cfg-setup_table'>
            <wui-table border height='100%' data={model.list} onRow-contextmenu={handleRowMenu}>
              {{
                default: () => (
                  <>
                    <wui-table-column label='No.' type='index' width='80px' align='center' />
                    <wui-table-column prop='group_name' label='Group Name' align='center'>
                      {{
                        default: ({ row, $index }: TableGroupDefaultScope) => (
                          <>
                            {row.flag ? (
                              <wui-form-item
                                prop={`list.${$index}.group_name`}
                                rules={groupRules.group_name}>
                                <wui-input
                                  v-model={row.group_name}
                                  type='text'
                                  placeholder='Please input'
                                />
                              </wui-form-item>
                            ) : (
                              row.group_name
                            )}
                          </>
                        )
                      }}
                    </wui-table-column>
                    <wui-table-column label='Op' width='100px' align='center'>
                      {{
                        default: ({ row, $index }: any) => (
                          <TableTool.Op flag={row.flag} onOp={op => handleOp(op, row, $index)} />
                        )
                      }}
                    </wui-table-column>
                  </>
                ),
                empty: () => <TableTool.Empty />
              }}
            </wui-table>
          </div>
        </wui-form>
      </>
    )
  }
})

const Header: FunctionalComponent = () => <div>Table Groups</div>

const SetupGroup = {
  Header,
  Table
}
export default SetupGroup
