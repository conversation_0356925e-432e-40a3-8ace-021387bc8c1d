import { useParameterDialog } from '@/renderer/utils/common'
import { type FunctionalComponent } from 'vue'
import OperatorsButton from '@/renderer/components/OperatorsButton/index.vue'
import { useBem } from '@/renderer/hooks'
import $styles from './index.module.scss'
type EquationHeaderEmits = {
  select(val: string): void
  selectParam(): void
}
export const EquationHeader: FunctionalComponent<any, EquationHeaderEmits> = (
  _,
  { emit, slots }
) => {
  const { e } = useBem('calc-tool', $styles)
  const handleSelectParam = async () => {
    emit('selectParam')
  }
  const handleSelect = (val: string) => {
    emit('select', val)
  }
  return (
    <div class={e('header')}>
      <div class={e('header', 'lf')}>
        <OperatorsButton onSelect={handleSelect} position='absolute' />
        <wui-button onClick={handleSelectParam}>Parameters</wui-button>
        {slots.lt_content?.()}
      </div>
      <div>{slots.default?.()}</div>
      <div class={e('header', 'rt')}>{slots.rt_content?.()}</div>
    </div>
  )
}
