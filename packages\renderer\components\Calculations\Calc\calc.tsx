import { NodeDropEvent, TreeContent, TreeNode, TreeProps } from '../../TreeContent'
import { computed, defineComponent, onMounted, PropType, ref, provide, reactive, toRef } from 'vue'
import { SetupEquation, SetupGroup } from './index'
import { CfgFileType, uuid } from '@wuk/cfg'
import {
  CalcMode,
  CalcModeType,
  CalcTree,
  CalcTreeChild,
  CurEditCalcInfo,
  DragGpInfo,
  calcContextKey
} from './constants'
import { useBiz } from './hooks'
import { WuiMessage } from '@wuk/wui'
import { useDebounceFn } from '@vueuse/core'
export default defineComponent({
  name: 'Calc',
  props: {
    calcMode: {
      type: String as PropType<CalcModeType>,
      readonly: true,
      required: true
    }
  },
  setup(props) {
    const bizCalcs = useBiz(props.calcMode)
    // const calcData = ref<CalsSignalItem[]>([])
    const treeData = ref<CalcTree[]>([])
    const treeContentRef = ref<InstanceType<typeof TreeContent>>()
    const defaultExpandedKeys = ref<number[]>([])
    // group length
    const groupLen = ref(0)
    // dragging groupName
    const dragGpInfo = reactive<DragGpInfo>({
      groupName: '',
      oldGroupName: '',
      groupId: -1,
      oldGroupId: -1,
      isFinish: true
    })
    /**
     * @type {*}
     * @param {CalcsSignalType | CalcsExcuteType} calcId 一级节点id
     * @param {number} groupNodeIndex 二级节点index
     * */
    const curEditCalcInfo = ref<CurEditCalcInfo>({
      label: '',
      calcId: -1,
      index: 0,
      groupNodeIndex: -1,
      children: []
    })
    /**
     * @description 二级节点 label
     */
    const groupNodeLabel = ref('')
    const mode = ref<'setup-group' | 'setup-equation'>('setup-group')

    const isDragging = computed(() =>
      [CalcMode.Initial, CalcMode.Final].includes(props.calcMode as never)
    )
    const tipsMessage = () => {
      WuiMessage({
        message: 'success',
        type: 'success',
        offset: 90
      })
    }
    const handleNodeChange = (data: CalcTree, node: TreeNode) => {
      const { level, parent } = node
      if (level === 1) {
        mode.value = 'setup-group'
        curEditCalcInfo.value = {
          ...data,
          calcId: data.id,
          groupNodeIndex: -1
        }
      } else if (level === 2) {
        const { data: parentData } = parent
        mode.value = 'setup-equation'
        curEditCalcInfo.value = {
          ...(parentData as CalcTree),
          calcId: data.id,
          groupNodeIndex: dragGpInfo.isFinish
            ? data.index ?? -1
            : curEditCalcInfo.value.groupNodeIndex
        }
      }
      groupNodeLabel.value = data.label
    }
    const isGroupMode = computed(() => mode.value === 'setup-group')
    /**
     * @description 获取计算信号
     */
    const getCalcsSignalOptions = async () => {
      if (!dragGpInfo.isFinish) return
      if (props.calcMode === CalcMode.Signal) {
        treeData.value = [
          {
            label: 'Calibrate',
            id: CfgFileType.Calibrate,
            index: 0,
            children: []
          }
        ]
      } else if (props.calcMode === CalcMode.Sys_common) {
        treeData.value = [
          {
            label: 'Common',
            id: CfgFileType.Common,
            index: 0,
            children: []
          }
        ]
      } else {
        treeData.value = [
          {
            label: 'Common',
            id: CfgFileType.Common,
            index: 0,
            children: []
          },
          {
            label: 'Specific',
            id: CfgFileType.EngineSpecific,
            index: 1,
            children: []
          }
        ]
      }
      const groupList = (await bizCalcs.readCalcs?.()) || []
      groupList.forEach((item, originIndex) => {
        const idx = item.type === CfgFileType.EngineSpecific ? 1 : 0
        const treeItem = treeData.value[idx]
        const id = uuid()
        const len = treeItem.children?.length ?? 0
        if (item.group_name === dragGpInfo.groupName) {
          dragGpInfo.groupId = id
          curEditCalcInfo.value.calcId = id
          curEditCalcInfo.value.groupNodeIndex = originIndex
        }
        treeItem.children?.push({
          label: item.group_name,
          id,
          index: len,
          originData: {
            index: originIndex,
            ...item
          }
        })
      })
      const { id, ...args } = treeData.value[curEditCalcInfo.value.index ?? 0]
      curEditCalcInfo.value = {
        ...curEditCalcInfo.value,
        ...args
      }
      const groupId = dragGpInfo.groupId
      if (groupId !== -1) {
        changeTreeNode(groupId)
      }
      groupLen.value = groupList.length
      console.log(treeData.value, 'treeData.value ---- groupListgroupListgroupList')
    }
    /**
     * @description modify calc group
     */
    const changeTreeNode = async (id: number) => {
      treeContentRef.value?.treeRef?.setCurrentKey(id)
    }
    const handleAllowDrag: TreeProps<CalcTree>['allowDrag'] = node => {
      const { level } = node
      if (level === 1) return false
      if (level === 2) return true
      return false
    }
    const handleAllowDrop: TreeProps<CalcTreeChild>['allowDrop'] = (
      draggingNode,
      dropNode,
      type
    ) => {
      const { level } = dropNode
      if (level === 1) {
        if (type === 'inner') return true
        return false
      }
      if (level === 2) {
        if (type === 'inner') return false
        const { type: dragType } = draggingNode.data.originData
        const { type: dropType } = dropNode.data.originData
        if (dragType === dropType) return false
        return true
      }
      return false
    }
    const handleNodeDrop: NodeDropEvent<CalcTreeChild> = async (draggingNode, dropNode, type) => {
      dragGpInfo.isFinish = false
      const {
        id,
        index: dropIndex = -1,
        originData: dropOgData = { type: id, index: groupLen.value - 1 }
      } = dropNode.data
      const { id: dragId, originData: draggingOgData } = draggingNode.data
      const newIdx =
        type === 'before' || dropNode.level === 1 ? dropOgData.index : dropOgData.index + 1
      let res: boolean | undefined
      dragGpInfo.oldGroupName = dragGpInfo.groupName
      dragGpInfo.groupName = draggingOgData.group_name
      if (dragGpInfo.groupId !== dragId) {
        dragGpInfo.groupId = dragId
        curEditCalcInfo.value.groupNodeIndex = type === 'before' ? dropIndex - 1 : dropIndex + 1
        changeTreeNode(dragGpInfo.groupId)
      }
      const { index: dragIdx, ...groupItem } = draggingOgData
      if (dropOgData.type === CfgFileType.Common) {
        res = await bizCalcs.pasteSpecificToCommon(groupItem, newIdx, dragIdx)
      } else {
        res = await bizCalcs.copyCommonToSpecific(groupItem, newIdx, dragIdx)
      }
      if (!res) {
        changeTreeNode(dragGpInfo.oldGroupId)
        return
      }
      dragGpInfo.isFinish = true
      getCalcsSignalOptions()
      tipsMessage()
    }
    bizCalcs.bindCalcsOptionsHandler(useDebounceFn(getCalcsSignalOptions, 100))
    onMounted(async () => {
      await getCalcsSignalOptions()
      const defaultId = treeData.value[0].id
      defaultExpandedKeys.value = [defaultId]
      changeTreeNode(defaultId)
    })
    provide(
      calcContextKey,
      reactive({
        bizCalcs,
        calcMode: toRef(props.calcMode),
        curEditCalcInfo,
        changeTreeNode,
        dragGpInfo,
        groupLen
      })
    )
    return () => {
      return (
        <>
          <TreeContent
            ref={treeContentRef}
            onTree-node-change={handleNodeChange}
            treeData={treeData.value}
            treeAreaMenu={[]}
            draggable={isDragging.value}
            showRightMenu={false}
            rightContentPadding={0}
            allowDrag={handleAllowDrag}
            allowDrop={handleAllowDrop}
            onNode-drop={handleNodeDrop}
            v-model:default-expanded-keys={defaultExpandedKeys.value}>
            {{
              header: () =>
                isGroupMode.value ? (
                  <SetupGroup.Header />
                ) : (
                  <SetupEquation.Header label={groupNodeLabel.value} />
                ),
              default: () => (isGroupMode.value ? <SetupGroup.Table /> : <SetupEquation.Table />)
            }}
          </TreeContent>
        </>
      )
    }
  }
})
