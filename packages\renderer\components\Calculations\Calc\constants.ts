import { InjectionKey } from 'vue'
import { Tree } from '../../TreeContent'
import { CfgFileType } from '@wuk/cfg'
import { useBiz } from './hooks'
export interface CalcTreeChild extends Omit<Tree<number>, 'children'> {
  originData: {
    index: number
    group_name: string
    type: CfgFileType
    file: string
  }
}
export interface CalcTree extends Tree<number> {
  children?: CalcTreeChild[]
}
export type ReadFnName = 'readCalcsInitOptions' | 'readCalcsFinalOptions' | 'readCalcsSignalOptions'
export type RemoveFnName = 'removeCalcsInit' | 'removeCalcsFinal' | 'removeCalcsSignal'
export type AddFnName = 'addCalcsInit' | 'addCalcsFinal' | 'addCalcsSignal'
export type ModifyFnName = 'modifyCalcsInit' | 'modifyCalcsFinal' | 'modifyCalcsSignal'
export type HandleEventName =
  | 'onCalcsInitialOChanged'
  | 'onCalcsFinalChanged'
  | 'onCalcsSignalChanged'
type CalcModeItem = {
  readFnName: ReadFnName
  removeFnName: RemoveFnName
  addFnName: AddFnName
  modifyFnName: ModifyFnName
  handleEventName: HandleEventName
}
export type CurEditCalcInfo = {
  label: string
  calcId: number
  index?: number
  groupNodeIndex?: number
  children?: CalcTreeChild[]
}

export type DragGpInfo = {
  groupName: string
  oldGroupName: string
  groupId: number
  oldGroupId: number
  isFinish: boolean
}
interface calcContext {
  bizCalcs: ReturnType<typeof useBiz>
  calcMode: CalcModeType
  curEditCalcInfo: CurEditCalcInfo
  dragGpInfo: DragGpInfo
  changeTreeNode: (id: number) => void
  groupLen: number
}

export const CalcMode = <const>{
  Initial: 'initial',
  Final: 'final',
  Signal: 'signal',
  Sys_common: 'sys_common'
}
export type CalcModeType = (typeof CalcMode)[keyof typeof CalcMode]
export const calcModefnMap: Record<string, CalcModeItem> = <const>{
  [CalcMode.Initial]: {
    readFnName: 'readCalcsInitOptions',
    removeFnName: 'removeCalcsInit',
    addFnName: 'addCalcsInit',
    modifyFnName: 'modifyCalcsInit',
    handleEventName: 'onCalcsInitialOChanged'
  },
  [CalcMode.Final]: {
    readFnName: 'readCalcsFinalOptions',
    removeFnName: 'removeCalcsFinal',
    addFnName: 'addCalcsFinal',
    modifyFnName: 'modifyCalcsFinal',
    handleEventName: 'onCalcsFinalChanged'
  },
  [CalcMode.Signal]: {
    readFnName: 'readCalcsSignalOptions',
    removeFnName: 'removeCalcsSignal',
    addFnName: 'addCalcsSignal',
    modifyFnName: 'modifyCalcsSignal',
    handleEventName: 'onCalcsSignalChanged'
  }
}
export const calcContextKey: InjectionKey<calcContext> = Symbol('calcContextKey')
