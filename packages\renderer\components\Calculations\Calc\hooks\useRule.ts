import { FormRules } from '@wuk/wui'
import { inject } from 'vue'
import { calcContextKey } from '../constants'
import { Ref } from 'vue'
export const useCalcGpRules = (curEditIndex: Ref<number>) => {
  const calcContext = inject(calcContextKey)
  const calcGpRules: FormRules = {
    group_name: [
      { required: true, message: 'Please input group name', trigger: 'change' },
      {
        trigger: 'change',
        validator(_, value, callback) {
          const { children = [] } = calcContext?.curEditCalcInfo || {}
          const newList =
            curEditIndex.value === -1
              ? children
              : children.filter((_, index) => index !== curEditIndex.value)
          const isUnique = newList.some(item => item.label === value)
          if (isUnique) {
            callback('The groupName must be unique')
            return
          }
          callback()
        }
      }
    ]
  }
  return {
    calcGpRules
  }
}

export const calcEqRules: FormRules = {
  name: [{ required: true, message: 'Please input name', trigger: 'change' }]
}
