import { useBem, useTableCommonMenu } from '@/renderer/hooks'
import { defineComponent, inject, PropType, toRef, useModel, watch } from 'vue'
import $styles from './index.module.scss'
import {
  EquationTableRow,
  EquationTableRowKey,
  EquationTableRowScope,
  ModelType,
  EditRowTableExpose
} from './type'
import { Arrayable } from '@vueuse/core'
import { FormItemRule, WuiForm, WuiMessage, WuiMessageBox } from '@wuk/wui'
import TableTool, { isAddOrInsertType, OpType, RowType } from '@/renderer/components/TableTool'
import { CalcGroupable, CalcLineType, CalcWhenToExcuteType } from '@wuk/cfg'
import { calcContextKey } from '../constants'
import { watchEffect } from 'vue'
import { calcEqRules } from '../hooks'

export default defineComponent({
  name: 'EditRowTable',
  props: {
    model: {
      type: Object as PropType<ModelType>,
      default: () => ({})
    },
    eqFormRef: {
      type: Object as PropType<InstanceType<typeof WuiForm>>,
      default: undefined
    },
    handleModifyCalc: {
      type: Function as PropType<(removeIndex: number) => Promise<boolean | undefined>>,
      required: true
    }
  },
  emits: ['input-click', 'update:model', 'modify-calc', 'cancel-permanent-focus'],
  setup(props, { emit, expose }) {
    const calcContext = inject(calcContextKey)
    if (!calcContext) return
    const { b, e } = useBem('setup-equation', $styles)
    const model = useModel(props, 'model')
    const tipsMessage = () => {
      WuiMessage({
        message: 'success',
        type: 'success',
        offset: 90,
        grouping: true
      })
    }
    const createRow = (row_type: RowType, index: number, flag = true) => ({
      flag,
      row_type,
      comments: '',
      name: '',
      type: CalcLineType.kField,
      value: '',
      unit: '',
      index
    })
    const list = toRef(model.value, 'list')
    const { handleRowMenu } = useTableCommonMenu(list, async (key, ...args) => {
      const { row, rowIndex } = args[0]
      switch (key) {
        case 'addKey':
          const lsLen = model.value.list.length
          const addIndex = lsLen ? model.value.list[lsLen - 1].index + 1 : 0
          model.value.list.push(createRow('add', addIndex))
          break
        case 'insertKey':
          model.value.list.splice(rowIndex + 1, 0, createRow('insert', rowIndex + 1))
          break
        case 'modifyKey':
          handleOp('edit', row, rowIndex)
          break
        case 'deleteKey':
          handleOp('delete', row, rowIndex)
          break
      }
    })

    const handleOp = (op: OpType, row: EquationTableRow | undefined, index: number) => {
      if (!row) return
      switch (op) {
        case 'edit':
          row.flag = true
          break
        case 'delete':
          handleDelete(index)
          break
        case 'select':
          handleOpSelect(row, index)
          break
        case 'cancel':
          handleOpCancel(row, index)
          break
      }
    }

    const handleOpSelect = async (row: EquationTableRow, index: number) => {
      const valid = await props.eqFormRef?.validateField(`list.${index}.name`)
      if (!valid) return
      const res = await props.handleModifyCalc(-1)
      if (!res) return
      row.row_type = '*'
      row.flag = false
      emit('modify-calc')
      tipsMessage()
    }

    /**
     * @description handle op delete
     */
    async function handleDelete(index: number) {
      // model.value.list.splice(index, 1)
      const res = await props.handleModifyCalc(index)
      if (!res) return
      tipsMessage()
    }
    /**
     * @description handle op cancel
     */
    const handleOpCancel = (row: EquationTableRow, index: number) => {
      if (isAddOrInsertType(row.row_type)) {
        model.value.list.splice(index, 1)
        return
      }
      if (!model.value.originCalcGroupable) return
      const { name, type, comments, value, unit } = model.value.originCalcGroupable.lines[index]
      row.name = name
      row.value = value
      row.comments = comments.join('\n')
      row.unit = unit
      row.type = type
      row.flag = false
    }

    /**
     * @description reset form
     */
    const handleLoadCalc = async (force = false) => {
      const { groupNodeIndex = -1, children = [] } = calcContext.curEditCalcInfo
      const { originData } = children[groupNodeIndex] || {}
      if (!originData) return
      model.value.originCalcGroupable = await calcContext.bizCalcs.loadCalc?.(
        originData.file,
        originData.type,
        force
      )
      const { excute = CalcWhenToExcuteType.kNone, lines = [] } =
        model.value.originCalcGroupable || {}
      model.value.when_to_excute = excute
      model.value.list = lines
        .map((l, index) => ({ ...l, index }))
        .filter(({ type }) => type === CalcLineType.kField)
        .map(line => ({
          ...line,
          comments: line.comments.join('\n'),
          units: '',
          flag: false,
          row_type: '*'
        }))
    }

    watchEffect(() => {
      handleLoadCalc()
    })

    /**
     * @description save option
     */
    const handleSave = async () => {
      const onSave = async () => {
        const { curEditCalcInfo, bizCalcs, changeTreeNode } = calcContext
        const { groupNodeIndex = -1, children = [] } = curEditCalcInfo
        const originData = children[groupNodeIndex].originData
        const res = await bizCalcs.saveCalc?.(originData.file, originData.type)
        if (!res) return
        changeTreeNode(curEditCalcInfo.calcId)
      }
      const isNoSave = model.value.list.some(item => item.flag)
      if (isNoSave) {
        WuiMessageBox.confirm(
          'Data has not been fully saved. Do you want to continue submitting the saved data?',
          'Warning',
          {
            confirmButtonText: 'OK',
            cancelButtonText: 'Close',
            type: 'warning',
            draggable: true,
            showClose: false
          }
        )
          .then(async () => {
            await onSave()
          })
          .catch(() => {})
      } else {
        await onSave()
      }
    }

    calcContext.bizCalcs.bindCalcFileHander(handleLoadCalc)

    expose<EditRowTableExpose>({
      handleSave,
      handleLoadCalc
    })

    /**
     * @description column render
     */
    const RenderTableColumn = ({
      prop,
      label,
      rules,
      type = 'text',
      row: inputRow,
      isPermanentFocus = false
    }: {
      prop: EquationTableRowKey
      label: string
      rules?: Arrayable<FormItemRule>
      type?: string
      row?: number
      isPermanentFocus?: boolean
    }) => {
      return (
        <>
          <wui-table-column show-overflow-tooltip prop={prop} label={label} align='center'>
            {{
              default: ({ row, $index }: EquationTableRowScope) => {
                const clickHandler = isPermanentFocus
                  ? (ev: MouseEvent) => emit('input-click', row, prop, ev)
                  : (ev: MouseEvent) => emit('cancel-permanent-focus', ev)
                return (
                  <>
                    {row.flag ? (
                      rules ? (
                        <wui-form-item prop={`list.${$index}.${prop}`} rules={rules}>
                          <wui-input
                            type={type}
                            row={inputRow}
                            onClick={clickHandler}
                            placeholder={`Please input ${prop}`}
                            v-model={row[prop]}
                            clearable
                          />
                        </wui-form-item>
                      ) : (
                        <wui-input
                          onClick={clickHandler}
                          placeholder={`Please input ${prop}`}
                          v-model={row[prop]}
                          type={type}
                          row={inputRow}
                          clearable
                        />
                      )
                    ) : Array.isArray(row[prop]) ? (
                      row[prop].join(';')
                    ) : (
                      row[prop]
                    )}
                  </>
                )
              }
            }}
          </wui-table-column>
        </>
      )
    }
    return () => (
      <wui-table
        data={model.value.list}
        height='100%'
        border
        class={e('table')}
        onRow-contextmenu={handleRowMenu}>
        {{
          default: () => (
            <>
              <wui-table-column label='No.' type='index' width='80px' align='center' />
              <RenderTableColumn prop='name' label='Name' rules={calcEqRules.name} />
              <RenderTableColumn prop='value' label='Equation' isPermanentFocus={true} />
              <RenderTableColumn prop='unit' label='Units' />
              <RenderTableColumn type='textarea' row={2} prop='comments' label='Comments' />
              <wui-table-column label='Op' width='100px' align='center'>
                {{
                  default: ({ row, $index }: any) => (
                    <TableTool.Op flag={row.flag} onOp={op => handleOp(op, row, $index)} />
                  )
                }}
              </wui-table-column>
            </>
          ),
          empty: () => <TableTool.Empty />
        }}
      </wui-table>
    )
  }
})
