import {
  defineComponent,
  inject,
  reactive,
  type FunctionalComponent,
  ref,
  watch,
  computed
} from 'vue'
import { usePermanentFocus } from '@/renderer/hooks'
import { calcContextKey } from '../constants'
import { useBem } from '@/renderer/hooks/bem'
import $styles from './index.module.scss'
import { WuiForm, WuiMessage } from '@wuk/wui'
import { EquationHeader } from '../../CalcTool'
import { CalcGroupable, CalcWhenToExcuteType } from '@wuk/cfg'
import { cloneFnJSON } from '@vueuse/core'
import {
  EquationHeaderProps,
  EquationTableRow,
  ModelType,
  EditRowTableExpose,
  EditRowCodeExpose
} from './type'
import { calcEqRules } from '../hooks'
import EditRowTable from './editRowTable'
import EditRowCode from './editRowCode'
import { EditModeTool, EditMode } from '../../CalcTool'
import FunctionsButton from '@/renderer/components/FunctionsButton/index.vue'
const { b, e } = useBem('setup-equation', $styles)
const { handleInputClick, cancelPermanentFocus, handleSelect, handleSelectParam } =
  usePermanentFocus<EquationTableRow>()
const insertValue = ref('')
const whenToExcuteOptions = [
  {
    label: 'None',
    value: CalcWhenToExcuteType.kNone
  },
  {
    label: 'Select Engine',
    value: CalcWhenToExcuteType.kSelectEngine
  },
  {
    label: 'Init Displays',
    value: CalcWhenToExcuteType.kInitDisplays
  },
  {
    label: 'Open Test',
    value: CalcWhenToExcuteType.kOpenTest
  },
  {
    label: 'Load Tables',
    value: CalcWhenToExcuteType.kLoadTables
  },
  {
    label: 'All Times',
    value: CalcWhenToExcuteType.kAllTimes
  }
]
const Table = defineComponent({
  props: {},
  setup() {
    const calcContext = inject(calcContextKey)
    if (!calcContext) return
    const permanentFocus = ref(true)
    const eqFormRef = ref<InstanceType<typeof WuiForm>>()
    const editRowTableRef = ref<EditRowTableExpose>()
    const editRowCodeRef = ref<EditRowCodeExpose>()
    const tipsMessage = () => {
      WuiMessage({
        message: 'success',
        type: 'success',
        offset: 90,
        grouping: true
      })
    }
    const model = reactive<ModelType>({
      when_to_excute: CalcWhenToExcuteType.kSelectEngine,
      editMode: EditMode.Table,
      list: [],
      originCalcGroupable: undefined
    })

    const handleModifyCalc = async (removeIndex = -1) => {
      const { bizCalcs, curEditCalcInfo } = calcContext
      const { children = [], groupNodeIndex = -1 } = curEditCalcInfo
      if (groupNodeIndex === -1) return
      const { label, originData } = children[groupNodeIndex]
      const { name, comments = [], lines: newLines = [] } = model.originCalcGroupable || {}
      if (removeIndex !== -1) {
        newLines.splice(removeIndex, 1)
      } else {
        model.list.forEach(item => {
          const { row_type, flag, index, ...lines } = item
          newLines[index] = {
            ...lines,
            comments: lines.comments ? lines.comments.split('\n') : []
          }
        })
      }
      console.log(newLines, '---------------')
      return await bizCalcs.modifyCalc?.(
        originData.file,
        cloneFnJSON({
          name: name || label,
          comments,
          excute: model.when_to_excute,
          lines: newLines
        }),
        originData.type
      )
    }

    const handleChange = async () => {
      const res = await handleModifyCalc()
      if (!res) return
      tipsMessage()
    }

    /**
     * @description save option
     */
    const handleSave = async () => {
      if (model.editMode === EditMode.Table) {
        await editRowTableRef.value?.handleSave()
      } else {
        const res = await editRowCodeRef.value?.handleSave()
        if (!res) return
        const { curEditCalcInfo, changeTreeNode } = calcContext
        await editRowTableRef.value?.handleLoadCalc(true)
        changeTreeNode(curEditCalcInfo.calcId)
      }
    }

    watch(insertValue, val => {
      if (!val) return
      editRowCodeRef.value?.code.insert(val)
      insertValue.value = ''
    })

    watch(
      () => model.editMode,
      _ => {
        permanentFocus.value = true
      }
    )
    const isEditTable = computed(() => model.editMode === EditMode.Table)

    return () => (
      <wui-form
        ref={eqFormRef}
        label-width='0'
        label-position='left'
        hide-required-asterisk
        inline-message
        validate-box-gap='3'
        validate-placement='bottom'
        model={model}
        rules={calcEqRules}
        class={[b(), 'cfg-setup']}>
        <wui-form-item label-width='180' align='center'>
          {{
            label: () => <div class={e('label')}>When to Execute</div>,
            default: () => (
              <wui-select
                v-model={model.when_to_excute}
                onChange={handleChange}
                onVisible-change={(visible: boolean) => {
                  permanentFocus.value = !visible
                }}
                placeholder='Please input when to excute'>
                {whenToExcuteOptions.map(option => (
                  <wui-option key={option.value} label={option.label} value={option.value} />
                ))}
              </wui-select>
            )
          }}
        </wui-form-item>
        <wui-form-item label-width='180'>
          {{
            label: () => <div class={e('label')}>Edit Mode</div>,
            default: () => <EditModeTool v-model:editMode={model.editMode} />
          }}
        </wui-form-item>
        <div class='cfg-setup_table'>
          <EditRowTable
            ref={editRowTableRef}
            v-model:model={model}
            eqFormRef={eqFormRef.value}
            onInput-click={handleInputClick}
            onCancel-permanent-focus={cancelPermanentFocus}
            handleModifyCalc={handleModifyCalc}
            onModify-calc={() => {
              editRowCodeRef.value?.loadCode(true)
            }}
            style={{
              display: isEditTable.value ? 'block' : 'none'
            }}
          />
          <EditRowCode
            style={{
              display: !isEditTable.value ? 'block' : 'none'
            }}
            v-model:permanentFocus={permanentFocus.value}
            ref={editRowCodeRef}
          />
        </div>
        <div class={['cfg-submit', e('footer')]}>
          <wui-button type='primary' onClick={handleSave}>
            Ok
          </wui-button>
        </div>
      </wui-form>
    )
  }
})

const Header: FunctionalComponent<EquationHeaderProps> = props => {
  const onSelect = (val: string) => {
    insertValue.value = val
    handleSelect(val)
  }
  return (
    <EquationHeader onSelect={onSelect} onSelectParam={handleSelectParam}>
      {{
        default: () => <div>Calculation Group: {props.label}</div>,
        lt_content: () => <FunctionsButton onSelect={onSelect} />
      }}
    </EquationHeader>
  )
}
Header.props = {
  label: {
    type: String,
    default: ''
  }
}

const SetupEquation = {
  Table,
  Header
}
export default SetupEquation
