import { CodeEditorExpose } from '@/renderer/components/CodeEditor'
import type { BaseTableDefaultScope, BaseTableRow } from '@/renderer/components/TableTool'
import type { CalcGroupable, CalcLineable, CalcWhenToExcuteType } from '@wuk/cfg'
import { EditMode } from '../../CalcTool'
export type EquationTableRow = BaseTableRow<
  Omit<CalcLineable, 'comments'> & {
    unit: string
    index: number
    comments: string
  }
>
export type EquationTableRowKey = keyof EquationTableRow
export type EquationTableRowScope = BaseTableDefaultScope<EquationTableRow>
export type EquationHeaderProps = {
  label?: string
}
export type ModelType = {
  when_to_excute: CalcWhenToExcuteType
  editMode: EditMode
  list: EquationTableRow[]
  originCalcGroupable: CalcGroupable | undefined
}

export type EditRowTableExpose = {
  handleSave: () => Promise<void>
  handleLoadCalc: (force?: boolean) => Promise<void>
}
export type EditRowCodeExpose = {
  handleSave: () => Promise<boolean | undefined>
  loadCode: (force?: boolean) => Promise<void>
  code: CodeEditorExpose
}
