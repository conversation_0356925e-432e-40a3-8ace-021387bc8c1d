$namespace: 'editor-tabs';
.#{$namespace} {
  $bg: #f5f6f8;
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: $bg;
  :global {
    .wui-scrollbar__bar{
      &.is-horizontal {
        height: 2px;
      }
    }
  }
  &_tabs {
    padding: 0 5px;
    &_content {
      display: flex;
      background: transparent;
      border-bottom: 1px solid #e9ecef;
      height: 36px;
      align-items: center;
      gap: 2px;
    }
  }

  &_tab {
    display: inline-flex;
    align-items: center;
    padding: 0 12px;
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-bottom: none;
    cursor: pointer;
    -webkit-user-select: none;
    user-select: none;
    min-width: 100px;
    max-width: 200px;
    height: 28px;
    position: relative;
    transition: all 0.2s ease;
    border-radius: 4px 4px 0 0;
    margin-top: 4px;
    color: #495057;

    &:hover {
      background: #f8f9fa;
      color: #212529;

      .editor-tabs_tab-close {
        opacity: 1;
      }
    }

    &--active {
      background: #ffffff;
      border-bottom: 2px solid #228be6;
      color: #228be6;
      margin-bottom: -1px;
      z-index: 1;

      &::after {
        content: '';
        position: absolute;
        bottom: -1px;
        left: 0;
        right: 0;
        height: 1px;
        background: #ffffff;
      }

      .editor-tabs_tab-close {
        opacity: 1;
      }
    }

    &--modified {
      .editor-tabs_tab-title::after {
        content: '•';
        margin-left: 4px;
        color: #228be6;
      }
    }
  }

  &_tab-title {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 13px;
    line-height: 1;
  }

  &_tab-close {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 16px;
    margin-left: 8px;
    padding: 0;
    border: none;
    background: transparent;
    color: #868e96;
    cursor: pointer;
    border-radius: 3px;
    font-size: 14px;
    opacity: 0;
    transition: all 0.2s ease;

    &:hover {
      background: rgba(0, 0, 0, 0.05);
      color: #212529;
    }
  }

  &_editor {
    flex: 1;
    overflow: hidden;
    position: relative;
    background: #ffffff;
  }
}
