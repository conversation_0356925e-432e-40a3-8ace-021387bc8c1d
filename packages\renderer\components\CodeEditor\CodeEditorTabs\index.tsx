import { defineComponent, ref, computed, watch, useModel, nextTick } from 'vue'
import CodeEditor, { type CodeEditorExpose } from '../Editor'
import { useBem } from '@/renderer/hooks'
import $styles from './index.module.scss'

interface Tab {
  id: string
  title: string
  content: string
  headerKeywords: string
  ext: string
  isModified: boolean
}
type AddTab = {
  title: string
  content: string
  headerKeywords?: string
  ext?: string
}

export type CodeEditorTabsExpose = {
  addTab: (tab: AddTab) => void
  code: CodeEditorExpose
}

export const CodeEditorTabs = defineComponent({
  name: 'CodeEditorTabs',
  components: {
    CodeEditor
  },
  props: {
    tabs: {
      type: Array as () => Tab[],
      default: () => []
    }
  },
  emits: ['update:tabs'],
  setup(props, { expose }) {
    const { b, e, m } = useBem('editor-tabs', $styles)
    const tabs = useModel(props, 'tabs')
    const codeEditorRef = ref<CodeEditorExpose>()
    const activeTabId = ref<string>(tabs.value[0]?.id || '')
    const editorContent = ref('')

    const activeTab = computed(() => tabs.value.find(tab => tab.id === activeTabId.value))

    watch(editorContent, newContent => {
      const tab = tabs.value.find(t => t.id === activeTabId.value)
      if (tab && tab.content !== newContent) {
        tab.content = newContent
        tab.isModified = true
      }
    })

    watch(
      activeTabId,
      newTabId => {
        const tab = tabs.value.find(t => t.id === newTabId)
        if (tab) {
          editorContent.value = tab.content
        }
      },
      { immediate: true }
    )

    const addTab = (tab: AddTab) => {
      const newTab: Tab = {
        ...tab,
        headerKeywords: tab.headerKeywords || '#include',
        ext: tab.ext || '*',
        id: `tab-${Date.now()}`,
        isModified: false
      }
      tabs.value.push(newTab)
      activeTabId.value = newTab.id
    }

    const closeTab = (tabId: string, e?: MouseEvent) => {
      if (e) {
        e.stopPropagation()
      }

      const index = tabs.value.findIndex(tab => tab.id === tabId)
      if (index === -1) return

      // 如果关闭的是当前tab，先保存当前内容
      if (tabId === activeTabId.value) {
        const tab = tabs.value[index]
        if (tab) {
          tab.content = editorContent.value
        }
      }

      tabs.value.splice(index, 1)

      // 如果关闭了当前tab，激活下一个可用的tab
      if (tabId === activeTabId.value) {
        activeTabId.value = tabs.value[index]?.id || tabs.value[index - 1]?.id || ''
      }
    }

    const switchTab = (tabId: string) => {
      if (tabId === activeTabId.value) return

      // 保存当前tab的内容
      const currentTab = tabs.value.find(t => t.id === activeTabId.value)
      if (currentTab) {
        currentTab.content = editorContent.value
      }

      activeTabId.value = tabId

      // 切换标签页后，需要重新检查当前行的include语句
      // 这里通过nextTick确保编辑器内容已经更新
      nextTick(() => {
        if (codeEditorRef.value) {
          // 触发编辑器的内容检查
          const currentTab = tabs.value.find(t => t.id === tabId)
          if (currentTab) {
            // 重新设置编辑器内容以触发检查
            editorContent.value = currentTab.content
          }
        }
      })
    }

    expose({
      addTab,
      code: codeEditorRef
    })
    return () => (
      <div class={b()}>
        <div class={e('tabs')}>
          <wui-scrollbar>
            <div class={e('tabs', 'content')}>
              {tabs.value.map(tab => (
                <div
                  key={tab.id}
                  class={[
                    e('tab'),
                    tab.id === activeTabId.value ? m('active', 'tab') : '',
                    tab.isModified ? m('modified', 'tab') : ''
                  ]}
                  onClick={() => switchTab(tab.id)}>
                  <span class={e('tab-title')}>{tab.title}</span>
                  <button class={e('tab-close')} onClick={e => closeTab(tab.id, e)} title='Close'>
                    ×
                  </button>
                </div>
              ))}
            </div>
          </wui-scrollbar>
        </div>
        <div class={e('editor')}>
          {activeTab.value && (
            <CodeEditor
              ref={codeEditorRef}
              height='100%'
              v-model:code={editorContent.value}
              headerKeywords={activeTab.value.headerKeywords}
              ext={activeTab.value.ext}
            />
          )}
        </div>
      </div>
    )
  }
})
