$namespace: 'code-editor';
:root {
  --code-editor-primary: #409eff;
  --code-editor-info: #909399;
}
.#{$namespace} {
  margin: 0 auto;
  height: 100%;
  &_container {
    height: 100%;
    box-sizing: border-box;
    border: 1px solid #dcdfe6;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 25px;
    position: relative;
    background: white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;

    &--focused {
      border-color: var(--code-editor-primary);
      box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.2);
    }
  }
}

:global {
  // Codemirror 组件样式 (保留原始类名)
  .cm-editor {
    font-size: 14px;
    line-height: 1.6;
    border-radius: 4px;
  }

  .cm-lineNumbers .cm-gutterElement {
    background-color: #f5f7fa;
    border-right: 1px solid #eaeaea;
    padding: 0 7px;
    color: var(--code-editor-info);
    font-size: 13px;
  }

  .cm-content .cm-line {
    line-height: 1.7;
  }

  .cm-cursor {
    border-left: 2px solid var(--code-editor-primary) !important;
    opacity: 1;
  }

  .cm-focused .cm-cursor {
    animation: blink 1.2s infinite;

    @keyframes blink {
      0%,
      100% {
        opacity: 1;
      }
      50% {
        opacity: 0;
      }
    }
  }

  .cm-activeLine {
    background-color: rgba(102, 177, 255, 0.08) !important;
  }
}
