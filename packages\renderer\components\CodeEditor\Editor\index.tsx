import {
  defineComponent,
  ref,
  reactive,
  onBeforeUnmount,
  shallowRef,
  computed,
  useModel,
  PropType,
  watch
} from 'vue'
import { Codemirror } from 'vue-codemirror'
import { EditorState } from '@codemirror/state'
import { EditorView, highlightActiveLine } from '@codemirror/view'
import { keymap } from '@codemirror/view'
import $styles from './index.module.scss'
import { useBem } from '@/renderer/hooks'
import { customSyntaxHighlight } from './syntax'
import { useDebounceFn, useThrottleFn } from '@vueuse/core'
import { isString } from '@/renderer/utils'

// Types
interface CursorPosition {
  line: number
  ch: number
}

interface EditorUpdatePayload {
  state: EditorState
}

interface EditorReadyPayload {
  view: EditorView
}

export type CodeEditorExpose = {
  togglePermanentFocus: () => void
  insert: (val: string) => void
  curFileName: string
  resetFileName: () => void
  codeFocus: () => void
}

export default defineComponent({
  name: 'CodeEditor',
  components: {
    Codemirror
  },
  props: {
    placeholder: {
      type: String,
      default: 'Please input code'
    },
    tabSize: {
      type: Number,
      default: 2
    },
    headerKeywords: {
      type: String,
      default: '#include'
    },
    ext: {
      type: String as PropType<'*' | string>,
      default: '*'
    },
    height: {
      type: [Number, String],
      default: 'auto'
    },
    code: {
      type: String,
      default: ''
    },
    permanentFocus: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:code', 'update:permanentFocus'],
  setup(props, { emit, expose }) {
    const { b, e, m } = useBem('code-editor', $styles)
    const code = useModel(props, 'code')
    const permanentFocus = useModel(props, 'permanentFocus')
    const cursorPosition = reactive<CursorPosition>({ line: 0, ch: 0 })
    const editorView = ref<EditorView | null>(null)
    const extensions = ref<any[]>([])
    const editorStyle = computed(() => {
      const { height: ht } = props
      const height = isString(ht) && ht.endsWith('%') ? ht : `${ht}px`
      return {
        height
      }
    })

    const curFileName = ref('')

    const onEditorReady = (payload: EditorReadyPayload) => {
      editorView.value = payload.view

      // 配置编辑器扩展
      extensions.value = [
        EditorState.readOnly.of(false),
        EditorView.editable.of(true),
        highlightActiveLine(),
        keymap.of([{ key: 'Ctrl-Space', run: autocomplete }]),
        customSyntaxHighlight
      ]
      // 启用永久焦点事件监听
      permanentFocus.value && enablePermanentFocus()
    }

    const onEditorUpdate = (payload: EditorUpdatePayload) => {
      const state = payload.state
      const selection = state.selection.main
      const doc = state.doc
      const line = doc.lineAt(selection.from)
      const ch = selection.from - line.from

      const newLine = line.number - 1
      const newCh = ch

      // 只在位置真正变化时更新状态
      if (newLine !== cursorPosition.line || newCh !== cursorPosition.ch) {
        cursorPosition.line = newLine
        cursorPosition.ch = newCh
      }

      // 使用防抖检查include行
      debouncedCheckIncludeLine(newLine)
    }

    const debouncedCheckIncludeLine = useDebounceFn((lineIndex: number) => {
      // 变化检测
      checkIncludeLine(lineIndex)
    }, 50)

    const checkIncludeLine = (lineIndex: number) => {
      if (!editorView.value) return

      const doc = editorView.value.state.doc
      const lineContent = doc.line(lineIndex + 1)?.text || ''
      const trimmed = lineContent.trim()

      if (trimmed.startsWith(props.headerKeywords)) {
        // 当ext为*时，支持任何文件后缀
        const extensionPattern =
          props.ext === '*'
            ? '[\\w.-]+' // 匹配任何文件后缀
            : `${props.ext.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}` // 转义特殊字符

        const rge = new RegExp(
          `(${props.headerKeywords.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})` + // 转义特殊字符
            `\\s+` + // 匹配空白
            `([\\w.-]+\\.` + // 文件名部分
            `${extensionPattern})`, // 扩展名部分
          'i' // 不区分大小写
        )
        const match = trimmed.match(rge)
        if (match) {
          const fileName = match[2]
          curFileName.value = fileName
        } else {
          curFileName.value = ''
        }
      } else {
        // 如果当前行不是include语句，清空文件名
        curFileName.value = ''
      }
    }

    // 页面全局点击处理
    const ensureFocus = useThrottleFn(() => {
      if (editorView.value && permanentFocus.value) {
        editorView.value?.focus()
      }
    }, 50)

    // 开启永久焦点模式
    const enablePermanentFocus = () => {
      permanentFocus.value = true
      document.addEventListener('click', ensureFocus)
      document.addEventListener('focus', ensureFocus, true)
      document.addEventListener('blur', ensureFocus, true)
      document.addEventListener('mousedown', ensureFocus)
      document.addEventListener('touchstart', ensureFocus)
      setTimeout(() => {
        editorView.value?.focus()
      }, 100)
    }

    // 关闭永久焦点模式
    const disablePermanentFocus = () => {
      permanentFocus.value = false
      document.removeEventListener('click', ensureFocus)
      document.removeEventListener('focus', ensureFocus, true)
      document.removeEventListener('blur', ensureFocus, true)
      document.removeEventListener('mousedown', ensureFocus)
      document.removeEventListener('touchstart', ensureFocus)
    }

    // 切换永久焦点模式
    const togglePermanentFocus = () => {
      if (permanentFocus.value) {
        disablePermanentFocus()
        permanentFocus.value = false
      } else {
        enablePermanentFocus()
        permanentFocus.value = true
      }
    }

    const resetFileName = () => {
      curFileName.value = ''
    }

    const insert = (str: string) => {
      if (!editorView.value) return

      const state = editorView.value.state
      const doc = state.doc

      // 使用当前光标位置而不是保存的位置
      const currentPos = state.selection.main.head
      // 获取当前行信息
      const lineInfo = doc.lineAt(currentPos)
      // 新光标位置
      const newPos = currentPos + str.length

      editorView.value.dispatch({
        changes: {
          from: currentPos,
          insert: str
        },
        selection: {
          anchor: newPos,
          head: newPos
        },
        scrollIntoView: true
      })
      cursorPosition.ch = newPos
      // 确保编辑器获得焦点
      editorView.value.focus()
    }

    // 简单的自动补全函数
    const autocomplete = () => {
      return true
    }

    onBeforeUnmount(() => {
      if (permanentFocus.value) {
        disablePermanentFocus()
      }
    })

    expose({
      togglePermanentFocus,
      insert,
      curFileName,
      resetFileName,
      codeFocus: () => {
        editorView.value?.focus()
      }
    })

    return () => (
      <div class={b()}>
        <div class={[e('container'), permanentFocus.value ? m('focused', 'container') : '']}>
          <Codemirror
            v-model={code.value}
            style={editorStyle.value}
            autofocus={true}
            indentWithTab={true}
            tabSize={props.tabSize}
            placeholder={props.placeholder}
            extensions={extensions.value}
            onUpdate={onEditorUpdate}
            onReady={onEditorReady}
          />
        </div>
      </div>
    )
  }
})
