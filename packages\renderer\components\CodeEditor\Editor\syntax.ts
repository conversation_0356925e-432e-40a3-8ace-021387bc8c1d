import { Extension } from '@codemirror/state'
import { syntaxHighlighting, HighlightStyle, StreamLanguage } from '@codemirror/language'
import { tags } from '@lezer/highlight'

// 定义自定义语法标记
const customTags = {
  keyword: tags.keyword,
  operator: tags.operator,
  number: tags.number,
  string: tags.string,
  comment: tags.comment,
  variable: tags.variableName,
  function: tags.function(tags.variableName),
  random: tags.special(tags.variableName),
  atom: tags.atom
}

// 定义语法高亮样式
const customHighlightStyle = HighlightStyle.define([
  { tag: customTags.keyword, color: '#0000FF' },
  { tag: customTags.operator, color: '#51a6b1' },
  { tag: customTags.number, color: '#098658' },
  { tag: customTags.string, color: '#A31515' },
  { tag: customTags.comment, color: '#808080', fontStyle: 'italic' },
  { tag: customTags.variable, color: '#001080' },
  { tag: customTags.function, color: '#795E26' },
  { tag: customTags.random, color: '#098658' },
  { tag: customTags.atom, color: '#8B0000' }
])

// 定义词法分析器
const customLanguage = StreamLanguage.define({
  token(stream) {
    // 跳过空白
    if (stream.eatSpace()) return null

    // 以 #C 开头的行为注释
    if (stream.sol() && stream.match('#C')) {
      stream.skipToEnd()
      return 'comment'
    }

    // 字符串
    if (stream.match('"')) {
      let escaped = false
      while (!stream.eol()) {
        const char = stream.next()
        if (char === undefined) break
        if (!escaped && char === '"') break
        escaped = char === '\\' && !escaped
      }
      return 'string'
    }

    // 数字
    if (/\d/.test(stream.peek() || '')) {
      stream.eatWhile(/\d/)
      return 'number'
    }

    // 关键字
    if (stream.match(/\b(if|else|while|for|return|break|continue)\b/)) {
      return 'keyword'
    }

    // 运算符
    if (stream.match(/[+\-*/=!<>]=?/)) {
      return 'operator'
    }

    // 随机数标记
    if (stream.match(/RND_\d+/)) {
      return 'random'
    }

    // 函数调用
    if (stream.match(/[a-zA-Z_][a-zA-Z0-9_]*(?=\()/)) {
      return 'function'
    }

    // 变量
    if (stream.match(/[a-zA-Z_][a-zA-Z0-9_]*/)) {
      return 'variable'
    }

    // 其他字符
    stream.next()
    return 'atom'
  }
})

// 导出语法高亮扩展
export const customSyntaxHighlight: Extension = [
  customLanguage,
  syntaxHighlighting(customHighlightStyle)
]
