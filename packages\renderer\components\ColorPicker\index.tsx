import { BizMain } from '@/renderer/logic'
import { WuiColorPicker } from '@wuk/wui'
import { defineComponent, onMounted, ref, watch } from 'vue'
import './index.scss'
export const ColorPicker = defineComponent({
  name: 'ColorPicker',
  props: {
    modelValue: {
      type: String,
      default: '0'
    }
  },

  setup(props, { emit }) {
    const color = ref()
    const predefineColors = ref<string[]>([])

    onMounted(() => {
      const colors = BizMain.impl?.colors
      color.value = colors?.forValue(props.modelValue)
      predefineColors.value = colors?.list() || []
    })

    watch(color, newValue => {
      const colors = BizMain.impl?.colors
      const key = colors?.forKey(newValue) || ''
      if (key === props.modelValue) return
      emit('update:modelValue', key)
    })

    watch(
      () => props.modelValue,
      newValue => {
        const colors = BizMain.impl?.colors
        color.value = colors?.forValue(newValue)
      }
    )
    return () => (
      <div class={'color-picker'}>
        <WuiColorPicker
          v-model={color.value}
          showAlpha={true}
          colorFormat='hex'
          readOnly={true}
          predefine={predefineColors.value}
        />
      </div>
    )
  }
})
