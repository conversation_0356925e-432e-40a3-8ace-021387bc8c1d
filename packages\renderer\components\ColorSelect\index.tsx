import { BizMain } from '@/renderer/logic'
import { defineComponent, onMounted, ref, watch } from 'vue'

export default defineComponent({
  name: 'ColorSelect',
  props: {
    modelValue: {
      type: String,
      default: 'red'
    },
    placeholder: {
      type: String,
      default: 'Select color'
    }
  },
  setup(props, { slots, emit }) {
    const color = ref()
    const colors = BizMain.impl?.colors
    const colorOptions = ref<Record<string, string>>({})

    onMounted(() => {
      color.value = props.modelValue
      colors?.list()?.forEach(item => {
        const key = colors?.forKey(item)
        key && (colorOptions.value[key] = key)
      })
    })

    watch(color, newValue => {
      emit('update:modelValue', newValue)
    })

    watch(
      () => props.modelValue,
      newValue => {
        color.value = newValue
      }
    )
    return () => (
      <>
        <wui-select v-model={color.value} placeholder={props.placeholder}>
          {Object.entries(colorOptions.value).map(([key, value]) => (
            <wui-option label={value} value={key} />
          ))}
        </wui-select>
      </>
    )
  }
})
