import { useOpenFileDialog } from '@/renderer/hooks/assert'
import { BizMain } from '@/renderer/logic'
import { useParameterDialog } from '@/renderer/utils/common'
import {
  WuiButton,
  WuiImage,
  WuiInput,
  WuiMessage,
  WuiOption,
  WuiRadio,
  WuiRadioGroup,
  WuiSelect,
  WuiSlider,
  WuiSwitch,
  WuiTag,
  WuiTooltip
} from '@wuk/wui'
import { CSSProperties, defineComponent, h, PropType, ref } from 'vue'
import { ColorPicker } from '../ColorPicker'
import { MultiColorPicker } from '../MultiColorPicker'
import { QuestionIcon } from '../editor/icon/icons'
import './index.scss'

interface TableRow {
  [key: string]: any
}

export const CustomFormItem = defineComponent({
  name: 'CustomFormItem',
  props: {
    label: { type: String, default: '' }, // 标签名称
    type: {
      type: String as PropType<
        | 'Text'
        | 'Number'
        | 'Image'
        | 'TextArea'
        | 'Font'
        | 'Select'
        | 'Radio'
        | 'Switch'
        | 'Table'
        | 'Color'
        | 'Slider'
        | 'Param'
        | 'File'
        | 'ParamList'
        | 'MultiColor'
      >,
      required: true
    }, // 组件类型：文本、数字、图片、多行文本、字体、选择器、单选框、开关、表格、颜色选择器、滑块、参数、文件、列表
    modelValue: {
      type: [String, Number, Boolean, Array as PropType<TableRow[] | string[]>, Object] as PropType<
        string | number | boolean | TableRow[] | string[] | Record<string, any>
      >,
      default: ''
    }, // 绑定值
    unit: { type: String, default: '' }, // 单位（仅对文本和数字类型生效）
    range: {
      type: Array as PropType<Array<any>>,
      default: () => []
    }, // 选项（用于选择器和单选框）
    cast: { type: String, default: '' }, // 类型转换
    comment: { type: String, default: '' }, // 提示说明
    column: {
      type: Array as PropType<
        Array<{
          key: string
          name: string
          type: string
          range?: Array<{ key: string; text: string }>
        }>
      >,
      default: () => []
    }, // 列配置
    row: { type: Object, default: () => ({}) }, // 行配置
    labelWidth: { type: String, default: '' }, // 标签宽度
    className: {
      type: String,
      default: () => ''
    },
    style: {
      type: Object as PropType<CSSProperties>,
      default: () => ({})
    }
  },
  emits: ['update:modelValue'],
  setup(props, { emit }) {
    const isFirstLoad = ref(true)
    const inputValue = ref('')
    const inputVisible = ref(false)

    const handleChange = (value: any) => {
      if (props.type === 'Slider') {
        if (props.cast === 'string') {
          value = String(value)
        }
        if (isFirstLoad.value) {
          isFirstLoad.value = false
          return
        }
      }
      if (props.type === 'Number') {
        value = Number(value)
      }
      emit('update:modelValue', value)
    }

    const showDialog = ref(false)
    const handleOpenDialog = () => {
      showDialog.value = true
    }

    // List 类型相关方法
    const handleTagClose = (tag: string) => {
      if (Array.isArray(props.modelValue)) {
        const newTags = [...props.modelValue]
        newTags.splice(newTags.indexOf(tag), 1)
        handleChange(newTags)
      }
    }

    const showTagInput = () => {
      inputVisible.value = true
      setTimeout(() => {
        // 聚焦到输入框
        const inputEl = document.querySelector('.tag-input input')
        if (inputEl) {
          ;(inputEl as HTMLInputElement).focus()
        }
      }, 10)
    }

    const handleTagInputConfirm = () => {
      if (inputValue.value) {
        const newTags = Array.isArray(props.modelValue) ? [...props.modelValue] : []
        newTags.push(inputValue.value)
        handleChange(newTags)
      }
      inputVisible.value = false
      inputValue.value = ''
    }

    const renderContent = () => {
      switch (props.type) {
        case 'Text':
          return h(WuiInput, {
            size: 'small',
            modelValue: String(props.modelValue),
            'onUpdate:modelValue': (val: string) => {
              handleChange(val)
            }
          })
        case 'Number':
          return h(
            'div',
            {
              style: {
                position: 'relative',
                display: 'flex',
                alignItems: 'center'
              }
            },
            [
              h(WuiInput, {
                modelValue: Number(props.modelValue),
                'onUpdate:modelValue': (val: string) => {
                  handleChange(val)
                },
                type: 'number',
                min: props.range?.length >= 2 ? props.range[0] : undefined,
                max: props.range?.length >= 2 ? props.range[1] : undefined,
                style: { flex: 1 }
              }),
              props.unit
                ? h(
                    'span',
                    {
                      style: {
                        position: 'absolute',
                        right: '25px',
                        color: '#999',
                        pointerEvents: 'none',
                        paddingRight: '5px',
                        backgroundColor: `var(--wui-input-bg-color, var(--wui-fill-color-blank))`
                      }
                    },
                    props.unit
                  )
                : null
            ]
          )
        case 'Image':
          return h(WuiImage, {
            action: 'https://jsonplaceholder.typicode.com/posts/',
            'list-type': 'picture-card',
            onSuccess: (file: any) => handleChange(file.url)
          })
        case 'TextArea':
          return h(WuiInput, {
            type: 'textarea',
            modelValue: String(props.modelValue),
            'onUpdate:modelValue': handleChange
          })
        case 'Font':
        case 'Select':
          return h(
            WuiSelect,
            {
              modelValue: props.modelValue,
              'onUpdate:modelValue': handleChange
            },
            () =>
              props.range.map((opt: { key: string; text: string }, index: number) =>
                h(WuiOption, { key: index, label: opt.text, value: opt.key })
              )
          )
        case 'Radio':
          return h(
            WuiRadioGroup,
            {
              modelValue: String(props.modelValue),
              'onUpdate:modelValue': handleChange
            },
            () =>
              props.range.map(opt =>
                h(WuiRadio, { key: opt.value, label: opt.value }, () => opt.label)
              )
          )
        case 'Switch':
          return h(WuiSwitch, {
            activeValue: props.range[1],
            inactiveValue: props.range[0],
            modelValue: props.modelValue as string | number | boolean,
            'onUpdate:modelValue': handleChange
          })
        case 'Slider':
          return h(
            'div',
            {
              class: 'slider-with-value',
              style: {
                display: 'flex',
                alignItems: 'center',
                position: 'relative',
                width: '100%'
              }
            },
            [
              h(WuiSlider, {
                style: { marginLeft: '4px', flex: '1' },
                modelValue: Number(props.modelValue),
                'onUpdate:modelValue': handleChange,
                color: '#c0c4cc',
                trackColor: '#F1F6F8',
                tooltip: true,
                tooltipTextColor: '#333',
                sticky: true,
                cast: props.cast || '',
                tooltipFormatter: (value: number) => value.toString(),
                min: Number(props.range[0]),
                max: Number(props.range[1])
              }),
              h(
                'div',
                {
                  style: {
                    marginLeft: '6px',
                    textAlign: 'center',
                    fontSize: '12px'
                  }
                },
                String(props.modelValue)
              )
            ]
          )
        case 'Color':
          return h(ColorPicker, {
            modelValue: String(props.modelValue),
            'onUpdate:modelValue': (val: string) => {
              handleChange(val)
            }
          })
        case 'Param':
          return h(
            'div',
            {
              style: {
                display: 'flex',
                alignItems: 'center',
                width: '156px'
              }
            },
            [
              h(WuiInput, {
                size: 'small',
                modelValue: String(props.modelValue),
                'onUpdate:modelValue': handleChange
              }),
              h(
                WuiButton,
                {
                  size: 'small',
                  type: 'text',
                  onClick: () => {
                    useParameterDialog().then((value: string) => {
                      handleChange(value)
                    })
                  }
                },
                () => 'Choose'
              )
            ]
          )
        case 'File':
          return h(
            WuiButton,
            {
              size: 'small',
              type: 'text',
              onClick: async () => {
                const result = await useOpenFileDialog().show()
                console.log('result........', result)
                if (result) {
                  const url = await BizMain.impl?.uploadImage(result)
                  console.log('url........', url)

                  if (url) {
                    handleChange(url)
                    WuiMessage({
                      message: 'Upload Success',
                      type: 'success',
                      offset: 80
                    })
                  } else {
                    WuiMessage({
                      message: 'Upload Failed',
                      type: 'error',
                      offset: 80
                    })
                  }
                }
              }
            },
            () =>
              props.modelValue ? (
                <WuiTooltip content={String(props.modelValue)}>
                  <div
                    style={{
                      width: '100px',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap',
                      marginLeft: '-10px',
                      textAlign: 'left'
                    }}>
                    {props.modelValue}
                  </div>
                </WuiTooltip>
              ) : (
                'Select File'
              )
          )
        case 'ParamList':
          return h(
            'div',
            { class: 'tag-list', style: { display: 'flex', flexWrap: 'wrap', gap: '8px' } },
            [
              ...(Array.isArray(props.modelValue) ? props.modelValue : []).map((tag: string) =>
                h(
                  WuiTag,
                  {
                    key: tag,
                    closable: true,
                    onClose: () => handleTagClose(tag),
                    style: { marginRight: '4px', marginBottom: '4px', height: '32px' }
                  },
                  () => tag
                )
              ),
              inputVisible.value
                ? [
                    h(WuiInput, {
                      class: 'tag-input',
                      modelValue: inputValue.value,
                      'onUpdate:modelValue': (val: string) => {
                        inputValue.value = val
                      },
                      size: 'small',
                      style: { width: '90px' },
                      onKeyup: (event: KeyboardEvent) => {
                        if (event.key === 'Enter') {
                          handleTagInputConfirm()
                        }
                      }
                    }),
                    h(
                      WuiButton,
                      {
                        size: 'small',
                        type: 'text',
                        onClick: () => {
                          useParameterDialog().then((value: string) => {
                            //把value设置到input中去
                            inputValue.value = value
                            handleTagInputConfirm()
                          })
                        }
                      },
                      () => 'Choose'
                    )
                  ]
                : h(
                    WuiButton,
                    {
                      size: 'small',
                      onClick: showTagInput
                    },
                    () => '+ add'
                  )
            ]
          )
        case 'MultiColor':
          return h(MultiColorPicker, {
            modelValue: Array.isArray(props.modelValue)
              ? props.modelValue
              : props.modelValue
              ? [String(props.modelValue)]
              : [],
            'onUpdate:modelValue': handleChange
          })
        default:
          return h('div', 'Unsupported Type')
      }
    }

    return () =>
      h(
        'div',
        {
          class: ['form-item', props.className],
          style: {
            ...props.style,
            display: 'flex',
            marginBottom: '2px'
          }
        },
        [
          h(
            'label',
            {
              class: 'form-item__label',
              style: {
                width: '100px'
              }
            },
            [
              props.label,
              props.comment
                ? h(
                    WuiTooltip,
                    {
                      content: props.comment,
                      placement: 'top',
                      theme: 'light',
                      offset: 10
                    },
                    () => [
                      h(QuestionIcon, {
                        class: 'form-item__label-icon'
                      })
                    ]
                  )
                : null
            ]
          ),
          h(
            'div',
            {
              class: 'form-item__content',
              style: { flex: 1 }
            },
            renderContent()
          )
        ]
      )
  }
})
