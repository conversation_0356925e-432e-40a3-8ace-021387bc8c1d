<template>
  <div :class="styles.slider_container" ref="containerRef">
    <wui-slider
      :model-value="modelValue"
      :min="min"
      :max="max"
      :show-tooltip="false"
      @update:model-value="$emit('update:modelValue', $event)"
      @change="val => $emit('change', val)"
    />
    <div :class="styles.slider_value" :style="bubbleStyle">
      {{ modelValue }}
      <div :class="styles.slider_value_arrow"></div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, defineProps, defineEmits } from 'vue'
import styles from './index.module.scss'
import { WuiSlider } from '@wuk/wui'

const props = defineProps({
  modelValue: {
    type: Number,
    required: true
  },
  min: {
    type: Number,
    default: 0
  },
  max: {
    type: Number,
    default: 100
  }
})

const emit = defineEmits(['update:modelValue', 'change'])
const containerRef = ref<HTMLElement | null>(null)

const bubbleStyle = computed(() => {
  if (!containerRef.value) return {}
  const containerWidth = containerRef.value.offsetWidth
  const percentage = (props.modelValue - props.min) / (props.max - props.min)
  return {
    left: `${containerWidth * percentage}px`,
    transform: 'translate(-50%, -130%)'
  }
})
</script>
