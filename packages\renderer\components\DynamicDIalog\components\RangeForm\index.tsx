import { defineComponent, useModel } from 'vue'
import { useBem } from '@/renderer/hooks'
import $styles from './index.module.scss'

export interface RangeFormData {
  minimum: string | number
  maximum: string | number
  units: string
}

export interface RangeFormProps {
  modelValue: RangeFormData
  disabled?: boolean
  placeholder?: {
    minimum?: string
    maximum?: string
    units?: string
  }
}

export default defineComponent({
  name: 'RangeForm',
  props: {
    modelValue: {
      type: Object as () => RangeFormData,
      default: () => ({
        minimum: '',
        maximum: '',
        units: ''
      })
    },
    disabled: {
      type: Boolean,
      default: false
    },
    placeholder: {
      type: Object as () => RangeFormProps['placeholder'],
      default: () => ({
        minimum: 'Minimum',
        maximum: 'Maximum',
        units: 'Units'
      })
    },
    showLabel: {
      type: Boolean,
      default: () => true
    }
  },
  emits: ['update:modelValue'],
  setup(props) {
    const { b, e } = useBem('range-form', $styles)
    const modelValue = useModel(props, 'modelValue')

    return () => (
      <div class={b()}>
        <div class={e('content')}>
          <div class={e('field')}>
            {props.showLabel && <label class={e('label')}>Minimum</label>}
            <wui-input
              v-model={modelValue.value.minimum}
              placeholder={props.placeholder?.minimum}
              disabled={props.disabled}
              class={e('input')}
            />
          </div>
          <div class={e('field')}>
            {props.showLabel && <label class={e('label')}>Maximum</label>}
            <wui-input
              v-model={modelValue.value.maximum}
              placeholder={props.placeholder?.maximum}
              disabled={props.disabled}
              class={e('input')}
            />
          </div>
          <div class={e('field')}>
            {props.showLabel && <label class={e('label')}>Units</label>}
            <wui-input
              v-model={modelValue.value.units}
              placeholder={props.placeholder?.units}
              disabled={props.disabled}
              class={e('input')}
            />
          </div>
        </div>
      </div>
    )
  }
})
