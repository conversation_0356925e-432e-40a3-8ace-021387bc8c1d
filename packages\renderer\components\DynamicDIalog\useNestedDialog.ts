import { ref, reactive } from 'vue'
import type { DialogConfig, NestedDialogManager } from './types'

export interface NestedDialogState {
  visible: boolean
  config: DialogConfig
}

export interface ExtendedNestedDialogManager extends NestedDialogManager {
  dialogs: Record<string, NestedDialogState>
  getVisibleDialogs: () => [string, NestedDialogState][]
  updateDialogConfig: (key: string, config: Partial<DialogConfig>) => void
  closeAllDialogs: () => void
}

export function useNestedDialog(): ExtendedNestedDialogManager {
  const dialogs = reactive<Record<string, NestedDialogState>>({})

  const openDialog = (key: string, config?: DialogConfig) => {
    if (!config) {
      console.warn(`Dialog config for key "${key}" is required`)
      return
    }

    dialogs[key] = {
      visible: true,
      config
    }
  }

  const closeDialog = (key: string) => {
    if (dialogs[key]) {
      dialogs[key].visible = false
    }
  }

  const isDialogOpen = (key: string): boolean => {
    return dialogs[key]?.visible || false
  }

  const getDialogConfig = (key: string): DialogConfig | undefined => {
    return dialogs[key]?.config
  }

  const updateDialogConfig = (key: string, config: Partial<DialogConfig>) => {
    if (dialogs[key]) {
      dialogs[key].config = { ...dialogs[key].config, ...config }
    }
  }

  const closeAllDialogs = () => {
    Object.keys(dialogs).forEach(key => {
      dialogs[key].visible = false
    })
  }

  const getVisibleDialogs = () => {
    return Object.entries(dialogs).filter(([_, dialog]) => dialog.visible)
  }

  return {
    dialogs,
    openDialog,
    closeDialog,
    isDialogOpen,
    getDialogConfig,
    updateDialogConfig,
    closeAllDialogs,
    getVisibleDialogs
  }
}
