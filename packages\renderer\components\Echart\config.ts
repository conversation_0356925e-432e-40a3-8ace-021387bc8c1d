export function getChartConfigs() {
  const title = {
    left: 10,
    top: 0,
    textStyle: {
      fontSize: 15,
      fontWeight: '500',
      fontFamily: 'Roboto, Inter, Arial, sans-serif'
    }
  }

  const tooltip = {
    axisPointer: {
      type: 'cross',
      label: {
        show: false
      }
    },
    enterable: false,
    hideDelay: 0,
    confine: false,
    textStyle: {}
  }

  const verticalLegend = {
    type: 'scroll',
    orient: 'vertical',
    right: 4,
    top: 10,
    icon: 'circle',
    padding: [0, 0, 10, 0],
    itemHeight: 9,
    itemWidth: 14,
    itemGap: 14,
    textStyle: {},
    tooltip: {
      textStyle: {}
    }
  }

  const textStyle = {
    rich: {
      name: {
        width: 100,
        align: 'left'
      }
    }
  }

  const legend = {
    type: 'scroll',
    orient: 'horizontal',
    top: 4,
    padding: [0, 8, 30, 8],
    itemHeight: 9,
    itemGap: 14,
    itemWidth: 14,
    textStyle: {
      overflow: 'truncate',
      ellipsis: '...',
      rich: {}
    },
    tooltip: {
      padding: [4, 8],
      enterable: true,
      hideDelay: 800,
      fontSize: 12
    }
  }

  const grid = {
    left: '10%',
    right: '10%',
    bottom: '15%',
    top: '15%'
  }

  const xAxis = {
    type: 'value',
    nameLocation: 'middle',
    nameGap: 30,
    axisLabel: {
      formatter: '{value}'
    }
  }

  const xAxis3D = {
    type: 'value',
    nameLocation: 'middle',
    nameGap: 30
  }

  const yAxis = {
    type: 'value',
    nameLocation: 'middle',
    nameGap: 40,
    axisLabel: {
      formatter: '{value}'
    }
  }

  const yAxis3D = {
    type: 'value',
    nameLocation: 'middle',
    nameGap: 40
  }

  const zAxis3D = {
    type: 'value',
    nameLocation: 'middle',
    nameGap: 50
  }

  const grid3D = {
    viewControl: {
      projection: 'orthographic'
    }
  }

  const dataZoom = [
    {
      type: 'inside'
    },
    {
      start: 0,
      end: 10,
      textStyle: {
        overflow: 'break'
      }
    }
  ]

  const visualMap = {
    show: false,
    dimension: 2,
    min: 0,
    max: 30,
    inRange: {
      color: [
        '#313695',
        '#4575b4',
        '#74add1',
        '#abd9e9',
        '#e0f3f8',
        '#ffffbf',
        '#fee090',
        '#fdae61',
        '#f46d43',
        '#d73027',
        '#a50026'
      ]
    }
  }

  return {
    title,
    tooltip,
    textStyle,
    legend,
    grid,
    xAxis,
    yAxis,
    dataZoom,
    verticalLegend,
    visualMap,
    xAxis3D,
    yAxis3D,
    zAxis3D,
    grid3D
  }
}
