import { defineComponent, watch, onBeforeUnmount, onMounted, nextTick, useModel } from 'vue'
import _ from 'lodash'

import * as echarts from 'echarts/core'
import { <PERSON><PERSON><PERSON>, LineChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  DatasetComponent,
  TransformComponent,
  LegendComponent,
  DataZoomComponent,
  VisualMapComponent
} from 'echarts/components'
import { LabelLayout, UniversalTransition } from 'echarts/features'
import { CanvasRenderer } from 'echarts/renderers'

import 'echarts-gl'

export type { ECOption } from './types'
import { ChartType, type ECOption } from './types'

echarts.use([
  TitleComponent,
  TooltipComponent,
  GridComponent,
  DatasetComponent,
  TransformComponent,
  BarChart,
  LineChart,
  LabelLayout,
  UniversalTransition,
  CanvasRenderer,
  LegendComponent,
  DataZoomComponent,
  VisualMapComponent
])

import { uuid } from '@wuk/cfg'

import { defaultBarConfig, defaultLineConfig, defaultLine3DConfig } from './process'

export default defineComponent({
  name: 'Echart',
  props: {
    option: {
      type: Object as () => ECOption,
      default: () => {}
    },
    width: {
      type: [String, Number],
      default: '100%'
    },
    height: {
      type: [String, Number],
      default: '100%'
    }
  },
  setup(props) {
    //------------ref----------
    const id = `chart_${uuid()}`
    const opt = useModel(props, 'option')
    let myChart: echarts.ECharts | null = null
    const typeMapFun: Record<string, Function> = {
      line: defaultLineConfig,
      bar: defaultBarConfig,
      line3D: defaultLine3DConfig
    }

    //------------methods----------
    const updateChart = () => {
      if (!myChart) return

      const is3D = (type: string) => type?.endsWith('3D')

      const getBaseOption = (seriesTypes: string[]) =>
        seriesTypes.some(type => is3D(type))
          ? typeMapFun[ChartType.LINE_3D]()
          : typeMapFun[ChartType.LINE]()

      if (!opt.value?.series) {
        myChart.setOption(_.merge({}, typeMapFun[ChartType.LINE](), opt.value))
        myChart.resize()
        return
      }

      const seriesArray = Array.isArray(opt.value.series) ? opt.value.series : [opt.value.series]

      const mergedSeries = seriesArray.map(customSeries => {
        const seriesType = customSeries.type || ChartType.LINE
        const defaultSeries = typeMapFun[seriesType]?.().series?.[0] || {}
        return _.merge({}, defaultSeries, customSeries)
      })

      const seriesTypes = seriesArray.map(s => s.type || ChartType.LINE)
      const finalOption = _.merge({}, getBaseOption(seriesTypes), {
        ...opt.value,
        series: mergedSeries
      })

      myChart.setOption(finalOption)
      myChart.resize()
    }

    const init = () => {
      myChart = echarts.init(document.getElementById(id))
      updateChart()
    }

    watch(
      () => opt.value,
      () => {
        updateChart()
      },
      { deep: true }
    )

    //------------lifecycle----------
    onMounted(async () => {
      await nextTick()
      init()
    })

    onBeforeUnmount(() => {
      if (myChart) {
        myChart.dispose()
        myChart = null
      }
    })

    //------------render----------
    return () => {
      return (
        <>
          <div id={id} style={{ width: props.width, height: props.height }}></div>
        </>
      )
    }
  }
})
