import { cloneFnJSON } from '@vueuse/core'

import { getChartConfigs } from './config'

/**折线图默认配置 */
export function defaultLineConfig() {
  const { dataZoom, grid, legend, title, tooltip, xAxis, yAxis } = getChartConfigs()
  return {
    title,
    tooltip: {
      trigger: 'axis',
      extraCssText: 'max-height:calc(100vh - 2px);overflow: hidden;',
      ...tooltip
    },
    legend: {
      ...cloneFnJSON(legend),
      itemStyle: {
        opacity: 0
      }
    },
    grid,
    xAxis,
    yAxis,
    dataZoom,
    series: [
      {
        type: 'line',
        smooth: 0.5,
        symbol: 'circle',
        lineStyle: {
          width: 1
        },
        itemStyle: {
          opacity: 0
        },
        emphasis: {
          scale: 2,
          itemStyle: {
            opacity: 1
          },
          disabled: true // 是否关闭高亮状态。
        }
      }
    ]
  }
}

/**柱状图默认配置 */
export function defaultBarConfig() {
  const { dataZoom, grid, legend, title, tooltip, xAxis, yAxis } = getChartConfigs()
  return {
    title,
    tooltip: {
      trigger: 'axis',
      extraCssText: 'max-height:calc(100vh - 2px);overflow: hidden;',
      ...tooltip
    },
    legend: {
      ...cloneFnJSON(legend),
      icon: 'rect',
      itemStyle: {
        opacity: 'inherit'
      }
    },
    grid,
    xAxis,
    yAxis,
    dataZoom,
    series: [
      {
        type: 'bar',
        barWidth: '30%',
        itemStyle: {
          opacity: 1
        },
        emphasis: {
          disabled: true,
          scale: 1,
          itemStyle: {
            opacity: 1
          }
        }
      }
    ]
  }
}

/**3D折现默认配置 */
export function defaultLine3DConfig() {
  const { title, xAxis3D, yAxis3D, zAxis3D, grid3D, visualMap } = getChartConfigs()
  return {
    tooltip: {},
    title,
    grid3D,
    xAxis3D,
    yAxis3D,
    zAxis3D,
    visualMap,
    series: [
      {
        type: 'line3D',
        lineStyle: {
          width: 4
        }
      }
    ]
  }
}
