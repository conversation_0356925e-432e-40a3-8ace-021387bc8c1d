import type { BarSeriesOption, LineSeriesOption } from 'echarts/charts'
import type {
  TitleComponentOption,
  TooltipComponentOption,
  GridComponentOption,
  DatasetComponentOption,
  LegendComponentOption,
  DataZoomComponentOption,
  VisualMapComponentOption
} from 'echarts/components'
import type { XAXisComponentOption, YAXisComponentOption } from 'echarts'

// ECharts GL 3D 系列类型定义
export interface Line3DSeriesOption {
  type: 'line3D'
  name?: string
  data?: Array<[number, number, number]> | Array<Array<number>>
  lineStyle?: {
    width?: number
    color?: string
    opacity?: number
  }
  itemStyle?: {
    color?: string
    opacity?: number
  }
  emphasis?: {
    lineStyle?: {
      width?: number
      color?: string
    }
    itemStyle?: {
      color?: string
      opacity?: number
    }
  }
  animation?: boolean
  animationDuration?: number
  animationEasing?: string
}

// 3D 坐标轴类型定义
export interface XAxis3DComponentOption {
  type?: 'value' | 'category' | 'time' | 'log'
  name?: string
  nameLocation?: 'start' | 'middle' | 'end'
  nameGap?: number
  nameTextStyle?: {
    color?: string
    fontSize?: number
    fontWeight?: string | number
  }
  min?: number | string | ((value: { min: number; max: number }) => number)
  max?: number | string | ((value: { min: number; max: number }) => number)
  data?: Array<string | number>
}

export type YAxis3DComponentOption = XAxis3DComponentOption
export type ZAxis3DComponentOption = XAxis3DComponentOption

// 3D 网格类型定义
export interface Grid3DComponentOption {
  show?: boolean
  boxWidth?: number
  boxHeight?: number
  boxDepth?: number
  axisLine?: {
    show?: boolean
    lineStyle?: {
      color?: string
      width?: number
    }
  }
  axisLabel?: {
    show?: boolean
    textStyle?: {
      color?: string
      fontSize?: number
    }
  }
  axisTick?: {
    show?: boolean
    length?: number
    lineStyle?: {
      color?: string
      width?: number
    }
  }
  splitLine?: {
    show?: boolean
    lineStyle?: {
      color?: string
      width?: number
      type?: 'solid' | 'dashed' | 'dotted'
    }
  }
  splitArea?: {
    show?: boolean
    areaStyle?: {
      color?: string[]
    }
  }
  viewControl?: {
    projection?: 'perspective' | 'orthographic'
    autoRotate?: boolean
    autoRotateDirection?: 'cw' | 'ccw'
    autoRotateSpeed?: number
    damping?: number
    rotateSensitivity?: number
    zoomSensitivity?: number
    panSensitivity?: number
    panMouseButton?: 'left' | 'middle' | 'right'
    rotateMouseButton?: 'left' | 'middle' | 'right'
    distance?: number
    minDistance?: number
    maxDistance?: number
    orthographicSize?: number
    maxOrthographicSize?: number
    minOrthographicSize?: number
    alpha?: number
    beta?: number
    minAlpha?: number
    maxAlpha?: number
    minBeta?: number
    maxBeta?: number
    center?: [number, number, number]
    animation?: boolean
    animationDurationUpdate?: number
    animationEasingUpdate?: string
  }
}

// 扩展的 ECharts 选项类型，包含 3D 支持
export interface ECOption {
  // 基础配置
  backgroundColor?: string
  animation?: boolean
  animationDuration?: number
  animationEasing?: string

  // 系列数据
  series?: Array<BarSeriesOption | LineSeriesOption | Line3DSeriesOption>

  // 组件配置
  title?: TitleComponentOption
  tooltip?: TooltipComponentOption
  grid?: GridComponentOption
  dataset?: DatasetComponentOption
  legend?: LegendComponentOption
  dataZoom?: DataZoomComponentOption
  visualMap?: VisualMapComponentOption

  // 2D 坐标轴选项
  xAxis?: XAXisComponentOption
  yAxis?: YAXisComponentOption

  // 3D 组件选项
  grid3D?: Grid3DComponentOption
  xAxis3D?: XAxis3DComponentOption
  yAxis3D?: YAxis3DComponentOption
  zAxis3D?: ZAxis3DComponentOption
}

// 图表类型枚举
export enum ChartType {
  LINE = 'line',
  BAR = 'bar',
  LINE_3D = 'line3D'
}
