<template>
  <wui-popover
    v-model="state.visible"
    trigger="click"
    :width="450"
    :show-arrow="false"
    placement="bottom"
  >
    <wui-scrollbar :wrap-class="e('wrap')" :view-class="e('view')" @click="handleClickItem">
      <div v-for="(func, index) in funcs" :key="index" :data-name="$name" :class="e('item')">
        {{ func }}
      </div>
    </wui-scrollbar>
    <template #reference>
      <wui-button @click="handleClickBtn">{{ name }}</wui-button>
    </template>
  </wui-popover>
</template>
<script setup lang="ts">
import { useBem } from '@/renderer/hooks'
import { WuiPopover, WuiScrollbar } from '@wuk/wui'
import $styles from './index.module.scss'
import { reactive, shallowRef } from 'vue'
interface Props {
  name?: string
}
const emits = defineEmits(['select'])
// func：标识
const $name = 'func-item'
const { e } = useBem('funcs-btn', $styles)
withDefaults(defineProps<Props>(), {
  name: 'Functions'
})
/**
 * @description: 状态
 * @param {boolean} visible 是否显示
 */
const state = reactive({
  visible: false
})
const funcs = shallowRef([
  'Compare',
  'fueldensity',
  'fluidmassflow',
  'lookup',
  'pct2value',
  'startautotest',
  'abs',
  'value2pct',
  'changedisplay',
  'psi2inhg',
  'smoothsignal',
  'startcontrolloop',
  'psi2mbar',
  'stable',
  'stopcontrolloop',
  'inhg2psi',
  'evalratio',
  'modifycontrolloop',
  'temp_f2c',
  'max',
  'debugcontrolloop',
  'temp_c2f',
  'min',
  'calcpga',
  'temp_f2r',
  'inrange',
  'calcfueflow',
  'temp_r2f',
  'curveeval',
  'grns2lbs',
  'temp_c2k',
  'concat',
  'lbs2grns',
  'temp_k2c',
  'bittest',
  'retwfinterm',
  'pints2gals',
  'bitset',
  'averagen',
  'pints2liters',
  'bitclear',
  'calcfueflow2',
  'pints2qts',
  'setbits',
  'setarincsdi',
  'gals2pints',
  'setecutime',
  'setarincssm',
  'gals2liters',
  'setecudate',
  'fueldensitycorr',
  'gals2qts',
  'decodebcd',
  'average',
  'liters2pints',
  'log',
  'set_valid',
  'liters2gals',
  'fuelused',
  'set_invalid',
  'liters2qts',
  'wave',
  'lookupat',
  'qts2pints',
  'decodebits',
  'roundup',
  'qts2gals',
  'maxlookup',
  'temp_c2r',
  'qts2liters',
  'minlookup',
  'temp_r2c',
  'calchumidity',
  'airmassflow',
  'sqrt',
  'shum_gpp',
  'StoreEvent',
  'SQRT',
  'calwf',
  'setvxivalue'
])

/**
 * @description: 点击事件(委托)
 */
const handleClickItem = (e: MouseEvent) => {
  const target = e.target as HTMLElement
  if (target.dataset.name !== $name) return
  emits('select', target.innerText)
  state.visible = false
}
const handleClickBtn = () => {
  state.visible = !state.visible
}
</script>
