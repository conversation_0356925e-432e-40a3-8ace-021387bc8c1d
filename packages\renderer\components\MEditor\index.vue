<template>
  <div class="md-editor-container">
    <mavon-editor
      v-model="content"
      :toolbars="toolbars"
      :ishljs="true"
      :subfield="false"
      :box-shadow="false"
      :editable="true"
      :transition="false"
      code-style="monokai"
      @change="handleChange"
      @save="handleSave"
    />
  </div>
</template>

<script setup lang="ts">
/**
 * @description: 富文本编辑器
 * @author:
 * @date: 2025-03-11 15:00:00
 * @last-modified: 2025-03-11 15:00:00
 * @last-modified-by:
 * @example:
 * <MEditor v-model="content" @change="handleChange" @save="handleSave" />
 */
import { defineEmits, defineExpose, defineProps, ref, watch } from 'vue'

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  height: {
    type: String,
    default: '400px'
  },
  language: {
    type: String,
    default: 'zh-CN'
  }
})

const emit = defineEmits(['update:modelValue', 'change', 'save'])

const content = ref(props.modelValue)
const MEditor = ref()

watch(
  () => props.modelValue,
  newVal => {
    content.value = newVal
  }
)

// 默认工具栏配置
const toolbars = {
  bold: true,
  italic: true,
  header: true,
  underline: true,
  strikethrough: true,
  mark: true,
  superscript: true,
  subscript: true,
  quote: true,
  ol: true,
  ul: true,
  link: true,
  imagelink: true,
  code: true,
  table: true,
  fullscreen: true,
  readmodel: true,
  htmlcode: true,
  help: true,
  undo: true,
  redo: true,
  trash: true,
  save: true,
  navigation: true,
  alignleft: true,
  aligncenter: true,
  alignright: true,
  subfield: false,
  preview: false
}

// 内容变化事件
const handleChange = (value: string, render: HTMLElement) => {
  emit('update:modelValue', value)
  emit('change', { value, render })
}

// 保存事件
const handleSave = (value: string, render: HTMLElement) => {
  emit('save', { value, render })
}

// 对外暴露编辑器实例方法
defineExpose({
  MEditor
})
</script>

<style scoped>
.md-editor-container {
  width: 100%;
}
</style>
