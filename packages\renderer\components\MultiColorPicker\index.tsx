import { useBizMain } from '@/renderer/hooks'
import { WuiButton, WuiColorPicker, WuiTag } from '@wuk/wui'
import { defineComponent, onMounted, ref, watch } from 'vue'
import './index.scss'

export const MultiColorPicker = defineComponent({
  name: 'MultiColorPicker',
  props: {
    modelValue: {
      type: [String, Array],
      default: () => []
    }
  },
  emits: ['update:modelValue'],
  setup(props, { emit }) {
    const mainPtr = useBizMain()
    const predefineColors = ref<string[]>([])
    const selectedColors = ref<string[]>(
      Array.isArray(props.modelValue)
        ? (props.modelValue as string[])
        : props.modelValue
        ? [props.modelValue as string]
        : []
    )
    const showColorPicker = ref(false)
    const currentColor = ref<string>('')

    onMounted(() => {
      const colors = mainPtr.value?.colors
      predefineColors.value = colors?.list() || []
    })

    watch(
      () => props.modelValue,
      newValue => {
        selectedColors.value = Array.isArray(newValue)
          ? (newValue as string[])
          : newValue
          ? [newValue as string]
          : []
      }
    )

    // 当颜色变化时添加颜色
    watch(currentColor, newColor => {
      if (newColor && showColorPicker.value) {
        const colorKey = mainPtr.value?.colors?.forKey(newColor) || newColor
        addColor(colorKey)
      }
    })

    const addColor = (color: string) => {
      if (!selectedColors.value.includes(color)) {
        const newColors = [...selectedColors.value, color]
        selectedColors.value = newColors
        emit('update:modelValue', newColors)
      }
      showColorPicker.value = false
    }

    const removeColor = (colorToRemove: string) => {
      const newColors = selectedColors.value.filter(color => color !== colorToRemove)
      selectedColors.value = newColors
      emit('update:modelValue', newColors)
    }

    return () => (
      <div class='multi-color-picker'>
        <div class='selected-colors' style={{ alignItems: 'center' }}>
          {selectedColors.value.map(color => {
            const displayColor = mainPtr.value?.colors?.forValue(color) || color
            return (
              <WuiTag
                key={color}
                closable
                onClose={() => removeColor(color)}
                style={{
                  backgroundColor: '#f0f2f5',
                  color: '#333',
                  marginRight: '8px',
                  marginBottom: '8px',
                  height: '28px',
                  lineHeight: '28px',
                  display: 'flex',
                  alignItems: 'center'
                }}>
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '5px'
                  }}>
                  <div
                    style={{
                      width: '12px',
                      height: '12px',
                      borderRadius: '50%',
                      backgroundColor: displayColor,
                      border: '1px solid rgba(0,0,0,0.1)'
                    }}></div>
                  <span>{color}</span>
                </div>
              </WuiTag>
            )
          })}
          <WuiButton
            size='small'
            onClick={() => (showColorPicker.value = true)}
            style={{
              height: '28px',
              lineHeight: '28px',
              padding: '0 12px',
              marginBottom: '8px'
            }}>
            + Add
          </WuiButton>
        </div>

        {showColorPicker.value && (
          <div class='color-picker-container'>
            <WuiColorPicker v-model={currentColor.value} predefine={predefineColors.value} />
            <div
              style={{
                display: 'flex',
                justifyContent: 'flex-end',
                marginTop: '8px',
                gap: '8px'
              }}></div>
          </div>
        )}
      </div>
    )
  }
})

// 根据背景色计算对比色文本颜色
function getContrastTextColor(hexColor: string): string {
  // 如果不是十六进制颜色，返回黑色
  if (!hexColor || !hexColor.startsWith('#')) {
    return '#000000'
  }

  // 移除#前缀并处理缩写形式
  let hex = hexColor.substring(1)
  if (hex.length === 3) {
    hex = hex
      .split('')
      .map(h => h + h)
      .join('')
  }

  // 将十六进制颜色转换为RGB
  const r = parseInt(hex.substring(0, 2), 16)
  const g = parseInt(hex.substring(2, 4), 16)
  const b = parseInt(hex.substring(4, 6), 16)

  // 计算亮度 (YIQ公式)
  const yiq = (r * 299 + g * 587 + b * 114) / 1000

  // 根据亮度返回黑色或白色
  return yiq >= 128 ? '#000000' : '#ffffff'
}
