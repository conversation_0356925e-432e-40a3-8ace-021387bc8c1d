<template>
  <div :class="styles.collapseBox">
    <wui-collapse :style="{ position }" v-model="activeNames" @change="handleChange">
      <wui-collapse-item :title="name" name="1">
        <template #icon>
          <span></span>
        </template>
        <div
          :class="styles.collapseBox_item"
          v-for="(item, index) in options"
          :key="index"
          :style="{ backgroundColor: activeIndex === index ? '#b0cdff' : '' }"
          @click="onCollapseItem(item, index)"
        >
          {{ item }}
        </div>
      </wui-collapse-item>
    </wui-collapse>
  </div>
</template>

<script lang="ts" setup>
import { ref, defineEmits, PropType } from 'vue'
import styles from './index.module.scss'
import { WuiCollapse, WuiCollapseItem } from '@wuk/wui'

const emit = defineEmits(['select'])
defineProps({
  name: {
    type: String,
    default: 'Operators'
  },
  position: {
    type: String as PropType<'absolute' | 'static'>,
    default: 'static'
  }
})
const options = ref([
  '+',
  '-',
  '/',
  '*',
  '^',
  '(',
  ')',
  '&',
  '|',
  '=',
  '<',
  '>',
  '<=',
  '>=',
  '~',
  '%'
])
const activeNames = ref([])
const activeIndex = ref(-1)
const onCollapseItem = (row: string, rowIndex: number) => {
  activeIndex.value = rowIndex
  emit('select', row)
  activeNames.value = []
  activeIndex.value = -1
}
const handleChange = (e: any) => {
  if (e.length === 0) {
    activeIndex.value = -1
  }
}
</script>
