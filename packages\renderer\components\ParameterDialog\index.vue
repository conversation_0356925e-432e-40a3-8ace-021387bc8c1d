<template>
  <div>
    <MyDialog
      :model-value="visible"
      title="Parameter Selection"
      @ok="handleSubmit"
      @close="onClose()"
    >
      <div :class="styles.modelBox">
        <div :class="styles.modelBox_modelSearch">
          <span>Select </span>
          <wui-input v-model="filterText" placeholder="Please input" clearable />
        </div>
        <div :class="styles.modelBox_btnAssembly">
          <wui-button @click="onSearch">Search</wui-button>
          <wui-button>Signal Info</wui-button>
        </div>
      </div>
      <wui-table-v2
        class="ParameterTableV2"
        :columns="tableColumn"
        :data="showList"
        :width="459"
        :height="190"
        fixed
        :row-event-handlers="rowEventHandlers"
        :row-class="hightlightRow"
      >
        <template #empty>
          <div class="tableEmpty">
            <p>No Data</p>
          </div>
        </template>
      </wui-table-v2>
    </MyDialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import MyDialog from '@/renderer/components/dialog/index.vue'
import { useBizEngine, useCore } from '@/renderer/hooks'
import styles from './index.module.scss'
import { WuiMessage, WuiInput, WuiButton, WuiTableV2 } from '@wuk/wui'
import { deepCopy } from '@/renderer/utils/common'
import { BizEngine } from '@/renderer/logic'

interface TableDataType {
  name: string
}
const props = defineProps({
  visible: {
    type: Boolean,
    required: true
  },
  onSubmit: {
    type: Function,
    required: true
  },
  onClose: {
    type: Function,
    required: true
  }
})
const emit = defineEmits(['submit'])

const bizEngine = useCore<BizEngine>(BizEngine)
const selectedName = ref('')
const filterText = ref('')
const tableColumn = ref([
  {
    key: 'name',
    dataKey: 'name',
    width: 400
  }
])
const metaList = ref<TableDataType[]>([])
const showList = ref<TableDataType[]>([])
const selectRowIndex = ref()
const rowEventHandlers: any = {
  onClick: (params: { rowData: any; rowIndex: number; rowKey: KeyType; event: Event }) => {
    selectedName.value = params.rowData.name
    selectRowIndex.value = params.rowIndex
  },
  ondblclick: () => handleSubmit()
}
const hightlightRow = ({ columns, rowData, rowIndex }: any) => {
  return rowIndex === selectRowIndex.value ? 'selectedRow' : ''
}
const onSearch = () => {
  if (filterText.value) {
    showList.value = metaList.value.filter(item =>
      item.name.toLowerCase().includes(filterText.value.toLowerCase())
    )
  } else {
    showList.value = metaList.value
  }
}
const handleSubmit = () => {
  if (selectedName.value) {
    props.onSubmit(selectedName.value)
  } else {
    WuiMessage({
      message: 'Please select data',
      type: 'warning',
      offset: 80
    })
    return
  }
}
onMounted(async () => {
  filterText.value = ''
  const { list = [] } = (await bizEngine.value?.readAttributes()) || {}
  showList.value = metaList.value = list.map(item => ({ name: item.name }))
})
</script>

<style lang="scss" scoped>
.ParameterTableV2 {
  .tableEmpty {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  :deep(.wui-table-v2__main) {
    border: 1px solid #e2e2e2;

    .wui-vl__wrapper {
      height: 190px;
    }
    .wui-vl__wrapper > div:first-of-type {
      height: 190px !important;
    }
    .wui-table-v2__header-wrapper {
      height: 0 !important;
    }
    .wui-table-v2__header {
      height: 0 !important;
    }
    .wui-table-v2__header-row {
      height: 0 !important;
    }
    .wui-table-v2__row {
      cursor: pointer;
    }
    .wui-table-v2__row-cell {
      width: 100% !important;
      justify-content: center;

      &:hover {
        background-color: #e6efff;
      }
    }
  }
}
</style>

<style>
.selectedRow {
  background-color: #b0cdff !important;
}
</style>
