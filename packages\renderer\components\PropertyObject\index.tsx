import { useStoreStateValue } from '@/renderer/store'
import { defineComponent, onMounted } from 'vue'
import { CustomFormItem } from '../CustomFormItem'
import { $editor } from '../editor/stores'
import { prefix } from '../editor/utils'
import './style.scss'
export const PropertyObject = defineComponent({
  name: 'PropertyObject',
  props: {
    data: {
      type: Object,
      default: () => ({
        key: '',
        childs: []
      })
    },
    selectedInfo: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:modelValue'],
  setup(props, { emit }) {
    const editorRef = useStoreStateValue($editor)
    onMounted(() => {
      console.log('props.selectedInfo', props.selectedInfo?.meta)
      console.log('props.data', props.data.childs)
    })
    return () => (
      <div class={prefix('property-object')}>
        <div class={prefix('property-object-title')}>{props.data.name}</div>
        <div class={prefix('property-object-content')}>
          {props.data.childs.map((item: any) => {
            return (
              <>
                <div class={prefix('property-item')} key={item.key}>
                  <CustomFormItem
                    labelWidth={'100px'}
                    label={item.name}
                    type={item.type}
                    modelValue={item.value}
                    unit={item.unit}
                    range={item.range}
                    column={item.column}
                    row={item.row}
                    comment={item.comments}
                    onUpdate:modelValue={(e: any) => {
                      item.value = e
                      const result: any = {}
                      props.data.childs.forEach((child: any) => {
                        result[child.key] = child.value
                      })
                      emit('update:modelValue', result)
                    }}
                  />
                </div>
                {item.childs &&
                  item.childs[item.value] &&
                  item.childs[item.value].map((child: any) => {
                    return (
                      <div class={prefix('property-item')} key={item.key}>
                        <CustomFormItem
                          labelWidth={'100px'}
                          label={child.name}
                          type={child.type}
                          modelValue={child.value}
                          unit={child.unit}
                          range={child.range}
                          column={child.column}
                          row={child.row}
                          comment={child.comments}
                          onUpdate:modelValue={(e: any) => {
                            child.value = e
                            const result: any = {}
                            result[child.key] = child.value
                            emit('update:modelValue', result)
                          }}
                        />
                      </div>
                    )
                  })}
              </>
            )
          })}
        </div>
      </div>
    )
  }
})
