import MyDialog from '@/renderer/components/dialog/index.vue'
import { useTableCommonMenu } from '@/renderer/hooks'
import { WuiTable, WuiTableColumn } from '@wuk/wui'
import { defineComponent, h, PropType, ref, watchEffect } from 'vue'
import { CustomFormItem } from '../CustomFormItem'
import { prefix } from '../editor/utils'
import './index.scss'
interface TableItem {
  [key: string]: any
}

export const PropertyTable = defineComponent({
  name: 'PropertyTable',
  components: {
    MyDialog
  },
  props: {
    data: { type: Array as PropType<TableItem[]>, default: () => [] },
    columns: { type: Array as PropType<string[]>, default: () => [] },
    row: { type: Array as PropType<any[]>, default: () => [] },
    modelValue: { type: Array as PropType<any[]>, default: () => [] }
  },
  emits: ['update:modelValue'],
  setup(props, { emit }) {
    const tableData = ref<TableItem[]>([])
    const tableRow = ref<TableItem>({})
    const currentIndex = ref(0)
    const actionType = ref('add')
    const dialogVisible = ref(false)
    const title = ref('Add')

    const { handleRowMenu } = useTableCommonMenu(
      tableData,
      async (key, ...args) => {
        const { row, rowIndex } = args[0]
        switch (key) {
          case 'addKey': {
            dialogVisible.value = true
            actionType.value = 'add'
            title.value = 'Add'
            // 重置表单数据
            tableRow.value = {}
            console.log('tableRow.value----------', tableData.value)
            break
          }
          case 'modifyKey': {
            if (row) {
              dialogVisible.value = true
              title.value = 'Modify'
              actionType.value = 'modify'
              // 设置表单数据
              tableRow.value = row
            }
            break
          }
          case 'deleteKey': {
            if (row) {
              //删除选中行
              tableData.value = tableData.value.filter((item: TableItem) => item.index !== rowIndex)
              tableData.value.forEach((item: TableItem, index: number) => {
                item.index = index
              })
            }
            break
          }
        }
      },
      [1],
      [
        { key: 'addKey', label: 'add' },
        { key: 'modifyKey', label: 'modify' },
        { key: 'deleteKey', label: 'delete' }
      ]
    )

    const handleFormItemChange = (key: string, value: any) => {
      tableRow.value[key] = value
    }

    watchEffect(() => {
      tableData.value = props.data.map((item, index) => ({ ...item, index }))
    })

    return () => (
      <div class={prefix('property-table')}>
        <WuiTable
          data={tableData.value}
          onRow-contextmenu={handleRowMenu}
          empty-text='Right Click to Add'>
          {props.columns?.map((col, index) => (
            <WuiTableColumn prop={props.row && props.row[index].key} label={col} />
          ))}
        </WuiTable>

        {h(
          MyDialog,
          {
            title: title.value,
            width: '800px',
            modelValue: dialogVisible.value,
            'onUpdate:modelValue': (val: boolean) => (dialogVisible.value = val),
            onOk: () => {
              if (actionType.value === 'add') {
                const newRow = { ...tableRow.value, index: tableData.value.length }
                tableData.value = [...tableData.value, newRow]
              }
              dialogVisible.value = false
              emit('update:modelValue', JSON.parse(JSON.stringify(tableData.value)))
            }
          },
          () => (
            <div class='property-table-dialog-content'>
              <div class='property-table-dialog-grid'>
                {props.row.map(item =>
                  h(CustomFormItem, {
                    key: item.key,
                    label: item.name || item.key,
                    type: item.type,
                    labelWidth: '80px',
                    modelValue: tableRow.value[item.key],
                    'onUpdate:modelValue': (value: any) => handleFormItemChange(item.key, value),
                    range: item.range
                  })
                )}
              </div>
            </div>
          )
        )}
      </div>
    )
  }
})
