<template>
  <transition name="success-fade">
    <div v-if="show" class="successIndicator">
      <wui-icon class="successIndicator-icon"><Check /></wui-icon>
      <span class="successIndicator-text">Saved</span>
    </div>
  </transition>
</template>

<script lang="ts" setup>
import { Check } from '@element-plus/icons-vue'

interface Props {
  show?: boolean
}

withDefaults(defineProps<Props>(), {
  show: false
})
</script>

<style lang="scss">
.successIndicator {
  position: absolute;
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  gap: 4px;
  color: #52c41a;
  font-size: 12px;
  font-weight: 500;
  background: transparent;
  padding: 2px 6px;
  border-radius: 4px;
  z-index: 10;
  pointer-events: none;
  margin-left: 8px;

  &-icon {
    font-size: 14px;
  }

  &-text {
    white-space: nowrap;
  }
}

.success-fade-enter-active,
.success-fade-leave-active {
  transition: all 0.3s ease;
}

.success-fade-enter-from,
.success-fade-leave-to {
  opacity: 0;
  transform: translateY(-50%) translateX(10px);
}
</style>
