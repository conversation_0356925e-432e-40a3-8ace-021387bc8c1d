<template>
  <div :class="e('tip')" @contextmenu.prevent="$emit('custom-contextmenu', $event)">
    {{ content }}
  </div>
</template>

<script setup lang="ts">
import { useBem } from '@/renderer/hooks/bem'
import style from './index.module.scss'
defineEmits(['custom-contextmenu'])
defineProps({
  content: {
    type: String,
    default: 'No Data'
  }
})
const { e } = useBem('table-tool', style)
</script>
