import Op from './op.vue'
import Empty from './empty.vue'
export type OpType = 'edit' | 'select' | 'cancel' | 'delete'
export type RowType = '*' | 'add' | 'insert'
export type BaseTableRow<T, K = RowType> = T & {
  flag: boolean
  row_type: K
}
export type BaseTableDefaultScope<R, C = any> = {
  row: R
  cloumn: C
  $index: number
}
export const ROW_ACTION_TYPES = ['add', 'insert']
export const isAddOrInsertType = (type: RowType) => ROW_ACTION_TYPES.includes(type)
const TableTool = {
  Op,
  Empty
}

export default TableTool
