<template>
  <!-- 操作按钮容器 -->
  <div :class="e('op')">
    <!-- 非标志状态的操作按钮 -->
    <template v-if="!flag">
      <!-- 编辑按钮 -->
      <wui-icon :style="opStyle" @click="handleOp('edit')">
        <edit-pen />
      </wui-icon>
      <slot :op-style name="pre-op" />
    </template>

    <!-- 标志状态的操作按钮 -->
    <template v-else>
      <!-- 选择按钮 -->
      <wui-icon :style="opStyle" @click="handleOp('select')">
        <Select />
      </wui-icon>
      <slot :op-style name="after-op" />
      <!-- 取消确认弹窗 -->
      <wui-popconfirm
        title="Are you sure you want to cancel this?"
        width="295px"
        @confirm="handleOp('cancel')"
      >
        <template #reference>
          <!-- 取消按钮 -->
          <wui-icon :style="opStyle">
            <close-bold />
          </wui-icon>
        </template>
      </wui-popconfirm>
    </template>
  </div>
</template>

<script setup lang="ts">
import { EditPen, Select, CloseBold } from '@element-plus/icons-vue'
import { useBem } from '@/renderer/hooks/bem'
import style from './index.module.scss'
import type { OpType } from './index'
const emits = defineEmits<{
  (e: 'op', op: OpType): void
}>()
withDefaults(
  defineProps<{
    flag?: boolean
  }>(),
  {
    flag: false
  }
)
const { e } = useBem('table-tool', style)
const handleOp = (op: OpType) => {
  emits('op', op)
}
// 预留：后续修改样式，好统一
const opStyle = {
  cursor: 'pointer'
}
</script>
