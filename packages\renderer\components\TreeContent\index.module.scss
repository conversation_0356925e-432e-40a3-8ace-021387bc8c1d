$namespace: 'treecontent';
.#{$namespace} {
  width: 100%;
  height: 100%;
  overflow: auto;
  &_left {
    width: 100%;
    height: 100%;
    background-color: #fff;
    &_tree {
      width: 100%;
      height: 100%;
      font-size: 16px;
      --wui-tree-node-hover-bg-color: #eaf1fd;
      :global {
        .wui-tree-node {
          &__content {
            display: flex;
          }
          &__label {
            flex: 1 1 auto;
            width: 0;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }
  }
  &_resize_handle {
    position: relative;
    z-index: 2;
    transform: translateZ(1px);
    pointer-events: all;
    &:hover {
      &::before {
        background: #ccc;
        opacity: 0.5;
      }
    }
    &::after,&::before {
      position: absolute;
      content: "";
      width: 8px;
      height: 100%;
      top: 0;
      left: 50%;
      transform: translateX(-50%);
      z-index: 1;
    }
    &::after {
      width: 1px;
      background: transparent;
    }
  }
  &_right {
    display: flex;
    flex-direction: column;
    height: 100%;
    &_header {
      font-size: 18px;
      font-weight: bold;
      min-height: 50px;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 0 20px;
    }
    &_content {
      flex: 1;
      height: 100%;
      box-sizing: border-box;
      border-radius: 5px;
      margin: 20px;
      border: 1px solid #ccc;
      overflow: hidden;
      &--header {
        margin-top: 0px;
      }
    }
  }
}
