import { AppMenuItem } from '@wuk/cfg'
import { RenderContentContext } from '@wuk/wui'
export interface Tree<T = string | number> {
  label: string
  id: T
  index?: number
  children?: Tree[]
}
export type TreeNode<T = any> = Omit<RenderContentContext['node'], 'data'> & {
  data: T
}
export interface TreeProps<T = any> {
  treeData?: Tree[]
  treeAreaMenu?: AppMenuItem[]
  showRightBorder?: boolean
  showRightMenu?: boolean
  rightContentPadding?: number
  draggable?: boolean
  allowDrag?: (node: TreeNode<T>) => boolean
  allowDrop?: (
    draggingNode: TreeNode<T>,
    dropNode: TreeNode<T>,
    type: 'prev' | 'inner' | 'next'
  ) => boolean
}

export type NodeDropEvent<T> = (
  draggingNode: TreeNode<T>,
  dropNode: TreeNode<T>,
  type: 'before' | 'after' | 'inner',
  ev: MouseEvent
) => void
export type CtxType = 'Node' | 'Area'
