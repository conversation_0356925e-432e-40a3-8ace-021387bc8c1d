import { defineComponent, onMounted, PropType, ref, Ref, RenderFunction, SetupContext } from 'vue'

export function ComponentRef<T, P extends Record<string, any> = Record<string, any>, K = any>(
  name: string,
  render: (props: P, ref: Ref<T | undefined>, ctx: SetupContext, key?: K) => RenderFunction,
  key?: K
) {
  return defineComponent({
    name,
    props: {
      // eslint-disable-next-line vue/require-default-prop
      forProps: {
        type: Object as PropType<P>
      },
      // eslint-disable-next-line vue/require-default-prop
      forRef: {
        type: Object as PropType<Ref<T | undefined>>
      }
    },
    setup(props, ctx) {
      const innerRef = ref<T>()
      onMounted(() => {
        const { forRef } = props
        forRef && (forRef.value = innerRef.value)
      })

      return render(props.forProps as P, innerRef, ctx, key)
    }
  })
}
