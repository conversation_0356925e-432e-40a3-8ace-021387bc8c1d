.popup {
  min-height: 250px;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  border: 1px solid #c1c1c2;
  border-radius: 10px 10px 0 0;
  background-color: #f5f6f8;
  z-index: 999;

  &_header {
    background-color: #e0e3e8;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 13px;
    border-radius: 10px 10px 0 0;
    border-bottom: 1px solid #c1c1c2;
  }
  &_close {
    cursor: pointer;
    font-size: 18px;
  }

  &_title {
    margin: 0;
    height: 18px;
    line-height: 16px;
  }

  &_content {
    min-height: 100px;
    background-color: #f5f6f8;
  }

  &_footer {
    width: 165px;
    height: 50px;
    display: flex;
    justify-content: space-between;
    margin: auto;
  }
}
