<template>
  <div class="customClass">
    <wui-dialog
      :model-value="modelValue"
      :title="title"
      :width="width"
      draggable
      :close-on-click-modal="false"
      :destroy-on-close="true"
      @close="close"
    >
      <template #header>
        <span>{{ title }}</span>
        <slot name="header-right" />
      </template>
      <div :class="styles.popup_content" :style="contentStyle">
        <slot />
      </div>
      <template #footer>
        <div v-if="showFooter" :class="styles.popup_footer">
          <wui-button :class="styles.popup_footer_button" type="info" plain @click="close()">
            {{ cancelText }}
          </wui-button>
          <wui-button :class="styles.popup_footer_button" type="primary" @click="ok()">
            {{ okText }}
          </wui-button>
        </div>
      </template>
    </wui-dialog>
  </div>
</template>

<script lang="ts" setup>
import { <PERSON><PERSON><PERSON>utton, WuiDialog } from '@wuk/wui'
import { defineEmits, defineProps, PropType, CSSProperties } from 'vue'
import styles from './index.module.scss'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '标题'
  },
  width: {
    type: String,
    default: '500px'
  },
  okText: {
    type: String,
    default: 'save'
  },
  cancelText: {
    type: String,
    default: 'cancel'
  },
  contentStyle: {
    type: Object as PropType<CSSProperties>,
    default: () => ({
      padding: '20px 20px 0'
    })
  },
  showFooter: {
    type: Boolean,
    default: true
  }
})

const emits = defineEmits(['update:modelValue', 'close', 'ok'])

const close = async () => {
  emits('update:modelValue', false)
  emits('close')
}

const ok = () => {
  emits('ok')
}
</script>

<style lang="scss" stylemodule>
.customClass {
  .wui-dialog {
    padding: 0;
    border-radius: 10px 10px 0 0;
    background-color: #f5f6f8;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    margin: 0;

    &__header {
      height: 45px;
      display: flex;
      align-items: center;
      padding: 0 12px;
      background-color: #e0e3e8;
      border-radius: 10px 10px 0 0;
    }

    &__headerbtn {
      font-size: 18px;
      height: 44px;
      line-height: 52px;
    }

    &__title {
      font-size: 14px !important;
      font-weight: bold;
    }

    &__close {
      color: #101010 !important;
    }

    &__footer {
      text-align: center;
      .wui-button {
        width: 66px;
        height: 32px;
      }
      .wui-button--primary {
        --wui-button-bg-color: #6282c1;
        --wui-button-border-color: #3c5992;
        --wui-button-active-bg-color: #6282c1;
        --wui-button-active-border-color: #3c5992;
        --wui-button-hover-border-color: #3c5992;
        --wui-button-hover-bg-color: rgba(98, 130, 193, 0.7);
      }
      .wui-button--info.is-plain {
        width: 83px;
        height: 32px;
        --wui-button-bg-color: #f0f0f0;
        --wui-button-border-color: #b6bece;
        --wui-button-text-color: #3d3d3d;
      }
    }
  }
}
</style>
