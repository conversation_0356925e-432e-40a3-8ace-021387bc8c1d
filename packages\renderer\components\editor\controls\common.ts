import {
  FontConfigType,
  NumberConfigType,
  ParamConfigType,
  ParamListConfigType,
  SliderConfigType,
  SwitchConfigType,
  TextConfigType
} from './types'

export type BaseConfigType =
  | NumberConfigType
  | FontConfigType
  | TextConfigType
  | SliderConfigType
  | SwitchConfigType<string>
  | ParamConfigType
  | ParamListConfigType
export const ItemTypes = {
  CONTROL: 'control'
}

export const baseConfig: Array<BaseConfigType> = [
  {
    key: 'layer_name',
    name: 'Layer name',
    type: 'Text'
  },
  {
    key: 'flag_id',
    name: 'Display Flag',
    type: 'Param',
    belong: 'base'
  },
  {
    key: 'left',
    name: 'Left',
    type: 'Number',
    unit: 'px',
    belong: 'base'
  },
  {
    key: 'top',
    name: 'Top',
    type: 'Number',
    unit: 'px',
    belong: 'base'
  },
  {
    key: 'width',
    name: 'Width',
    type: 'Number',
    unit: 'px',
    belong: 'base'
  },
  {
    key: 'height',
    name: 'Height',
    type: 'Number',
    unit: 'px',
    belong: 'base'
  }
  // {
  //   key: 'zIndex',
  //   name: 'zIndex',
  //   type: 'Number',
  //   belong: 'base'
  // },
  // {
  //   key: 'font',
  //   name: 'Font',
  //   type: 'Font'
  // }
]

export interface BaseDefaultType {
  flag_id: string
}

export const baseDefault: BaseDefaultType = {
  flag_id: 'TRUE'
}
