import { BizMain } from '@/renderer/logic'
import { DisplayObjectType } from '@wuk/cfg'
import { isArray, isFunction, isObject } from '@wuk/wui/dist/utils'
import {
  BarGroupIcon,
  BoxIcon,
  BtnIcon,
  DigitalIcon,
  EcmBtnIcon,
  FuncBtnIcon,
  GaugeIcon,
  ImageComponentIcon,
  InputIcon,
  LineIcon,
  PlotIcon,
  SwitchIcon,
  TextIcon
} from '../icon/icons'
import {
  ComponentType,
  dspBarDefault,
  dspBarGroupConfig,
  dspBoxConfig,
  dspBoxDefault,
  dspButtonConfig,
  dspButtonDefault,
  dspDigitalConfig,
  dspDigitalDefault,
  dspEcmButtonConfig,
  dspEcmButtonDefault,
  dspFuncButtonConfig,
  dspFuncButtonDefault,
  dspGaugeConfig,
  dspGaugeDefault,
  dspImageConfig,
  dspImageDefault,
  dspIndicatorConfig,
  dspIndicatorDefault,
  dspInputConfig,
  dspInputDefault,
  dspLineConfig,
  dspLineDefault,
  dspPlotConfig,
  dspPlotDefault,
  dspStringConfig,
  dspStringDefault,
  dspSwitchConfig,
  dspSwitchDefault,
  dspTextConfig,
  dspTextDefault
} from './schema'
import { FieldType, TargetBoxItem } from './targetbox'
import { BaseDefineType } from './types'

export * from './common'

function mergeFields<T extends BaseDefineType>(config: Array<T>) {
  const result: Record<string, FieldType> = {}
  const make = (fields: Record<string, FieldType>, childs: Array<T>) => {
    childs.forEach(item => {
      fields[item.key] = [
        (item.belong !== 'base' && item.field) || item.key,
        item.type,
        item.belong,
        !!item.required,
        item.parent
      ]

      const subs = item.childs || item.row
      if (subs && (isObject(subs) || isArray(subs))) {
        const list = isArray(subs) ? subs : Object.values(subs).flat()
        list.length && make(fields, list)
      }
    })
  }
  make(result, config)

  return result
}

export const checkAttrs = async (
  fields: Record<string, FieldType>,
  attrs: Record<string, any>,
  filters: Record<string, boolean> = {}
) => {
  const result: Record<string, any> = {}
  const colors = BizMain.impl?.colors
  for (const key in attrs) {
    if (!(key in fields)) {
      delete attrs[key]
      continue
    }
    if (filters[key]) continue
    if (isFunction(attrs[key])) continue

    const [field, type, belong, , parent] = fields[key]
    if (belong === 'base') continue

    let val = attrs[key]
    if (val) {
      if (isArray(val)) {
        const list = []
        for (let i = 0; i < val.length; i++) {
          const item = val[i]
          if (!isObject(item)) continue
          list[i] = await checkAttrs(fields, item, filters)
        }
        val = list
      } else if (isObject(val)) {
        val = await checkAttrs(fields, val, filters)
      } else if (type === 'Color' && colors) {
        val = colors.forValue(val) || ''
      } else if (type === 'File') {
        val = await BizMain.impl?.customerURL(val)
      }
    }
    if (parent) {
      result[parent] = result[parent] || {}
      result[parent][field] = val
      continue
    }
    result[field] = val
  }
  return result
}

export const checkValid = (fields: Record<string, FieldType>, attrs: Record<string, any>) => {
  let result = true
  for (const key in fields) {
    const [, type, , required = false] = fields[key]
    if (required && !attrs[key]) {
      result = false
      break
    }
  }

  return result
}

export type DragBoxItem = TargetBoxItem<ComponentType>
export const components: Record<DisplayObjectType, DragBoxItem> = {
  Box: {
    type: 'WuiDspBox',
    displayName: 'Box',
    name: 'Box',
    icon: BoxIcon,
    config: dspBoxConfig,
    default: dspBoxDefault,
    fields: mergeFields(dspBoxConfig),
    category: 'component'
  },
  Button: {
    type: 'WuiDspButton',
    displayName: 'Button',
    name: 'Button',
    icon: BtnIcon,
    config: dspButtonConfig,
    default: dspButtonDefault,
    fields: mergeFields(dspButtonConfig),
    category: 'component'
  },
  Digital: {
    type: 'WuiDspDigitalChart',
    displayName: 'Digital',
    name: 'Digital',
    icon: DigitalIcon,
    config: dspDigitalConfig,
    default: dspDigitalDefault,
    fields: mergeFields(dspDigitalConfig),
    category: 'component'
  },
  Buttonobj: {
    type: 'WuiDspEcmButton',
    displayName: 'ECMButton',
    name: 'Buttonobj',
    icon: EcmBtnIcon,
    config: dspEcmButtonConfig,
    default: dspEcmButtonDefault,
    fields: mergeFields(dspEcmButtonConfig),
    category: 'component'
  },
  FuncButton: {
    type: 'WuiDspFuncButton',
    displayName: 'FuncButton',
    name: 'FuncButton',
    icon: FuncBtnIcon,
    config: dspFuncButtonConfig,
    default: dspFuncButtonDefault,
    fields: mergeFields(dspFuncButtonConfig),
    category: 'component'
  },
  Gauge: {
    type: 'WuiDspGauge',
    displayName: 'Gauge',
    name: 'Gauge',
    icon: GaugeIcon,
    config: dspGaugeConfig,
    default: dspGaugeDefault,
    fields: mergeFields(dspGaugeConfig),
    category: 'component'
  },
  Image: {
    type: 'WuiDspImage',
    displayName: 'Image',
    name: 'Image',
    icon: ImageComponentIcon,
    config: dspImageConfig,
    default: dspImageDefault,
    fields: mergeFields(dspImageConfig),
    category: 'component'
  },
  StatusIndicator: {
    type: 'WuiDspIndicator',
    displayName: 'Indicator',
    name: 'StatusIndicator',
    icon: DigitalIcon,
    config: dspIndicatorConfig,
    default: dspIndicatorDefault,
    fields: mergeFields(dspIndicatorConfig),
    category: 'component'
  },
  Input: {
    type: 'WuiDspInput',
    displayName: 'Input',
    name: 'Input',
    icon: InputIcon,
    config: dspInputConfig,
    default: dspInputDefault,
    fields: mergeFields(dspInputConfig),
    category: 'component'
  },
  Line: {
    type: 'WuiDspLine',
    displayName: 'Line',
    name: 'Line',
    icon: LineIcon,
    config: dspLineConfig,
    default: dspLineDefault,
    fields: mergeFields(dspLineConfig),
    category: 'component'
  },
  Plot: {
    type: 'WuiDspPlot',
    displayName: 'Plot',
    name: 'Plot',
    icon: PlotIcon,
    config: dspPlotConfig,
    default: dspPlotDefault,
    fields: mergeFields(dspPlotConfig),
    category: 'component'
  },
  String: {
    type: 'WuiDspString',
    displayName: 'String',
    name: 'String',
    icon: GaugeIcon,
    config: dspStringConfig,
    default: dspStringDefault,
    fields: mergeFields(dspStringConfig),
    category: 'component'
  },
  Switch: {
    type: 'WuiDspSwitch',
    displayName: 'Switch',
    name: 'Switch',
    icon: SwitchIcon,
    config: dspSwitchConfig,
    default: dspSwitchDefault,
    fields: mergeFields(dspSwitchConfig),
    category: 'component'
  },
  Bar: {
    type: 'WuiDspBarChart',
    displayName: 'Bar',
    name: 'Bar',
    icon: BarGroupIcon,
    config: dspBarGroupConfig,
    default: dspBarDefault,
    fields: mergeFields(dspBarGroupConfig),
    category: 'component'
  },
  Text: {
    type: 'WuiDspText',
    displayName: 'Text',
    name: 'Text',
    icon: TextIcon,
    config: dspTextConfig,
    default: dspTextDefault,
    fields: mergeFields(dspTextConfig),
    category: 'component'
  }
}

export * from './schema'
export * from './targetbox'
