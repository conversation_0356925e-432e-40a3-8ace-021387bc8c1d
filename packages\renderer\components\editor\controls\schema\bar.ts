import { DspBarDir, DspBarParamBox, DspBarPosition } from '@wuk/wui'
import { baseConfig, BaseConfigType, baseDefault } from '../common'
import {
  ColorConfigType,
  ParamConfigType,
  SelectConfigType,
  SwitchConfigType,
  TableConfigType
} from '../types'

export type DspBarConfigType = BaseConfigType | ParamConfigType

// param_id: string
// target: string
// corr: string
// label: string
// units: string
// min: number
// max: number
// tic_inter: number
// label_inter: number
export const dspBarConfig: Array<DspBarConfigType> = [
  //  ...baseConfig,
  {
    key: 'param_id',
    type: 'Param',
    field: 'paramId',
    comments: '绑定的id值。用于去后端websocket返回的数据中查找对应的数据。绑定的值是bar的进度。'
  },
  {
    key: 'label',
    name: 'Label',
    field: 'label',
    type: 'Text',
    comments: '最左侧标签值'
  },
  {
    key: 'min',
    name: 'Min Value',
    field: 'minValue',
    type: 'Number',
    comments: '进度的最小值'
  },
  {
    key: 'max',
    name: 'Max Value',
    field: 'maxValue',
    type: 'Number',
    comments: '进度的最大值'
  },
  {
    key: 'tic_inter',
    name: 'Tic Interval',
    type: 'Number',
    field: 'barTicInterval',
    comments: '每个刻度值间的间隔值。为0表示不设置刻度条'
  },
  {
    key: 'label_inter',
    name: 'Label Interval',
    type: 'Number',
    field: 'barLabelInterval',
    comments: '标记间隔几个刻度显示一个刻度值'
  },
  {
    key: 'units',
    name: 'Units',
    type: 'Text',
    field: 'unit',
    comments: '最右侧单位值'
  },
  {
    key: 'target',
    name: 'Target',
    field: 'target',
    type: 'Param',
    comments: 'target Id'
  },
  {
    key: 'corr',
    name: 'Corrected（corr）',
    type: 'Param',
    field: 'corr',
    comments: 'corr id'
  }
]

export type DspBarGroupConfigType =
  | BaseConfigType
  | ColorConfigType
  | SelectConfigType<number | string>
  | SwitchConfigType
  | TableConfigType<DspBarConfigType>

// label_space: number
// unit_space: number
// label_color: string
// length: number
// height: number
// format: string
// tic_pos: string
// dir: number
// shading: number
// spacing: number
// tic_font: number // 废弃
// param_box: number
// param_box_color: string
// limit_width: number
// unit_font_size: number
// tic_font_size: number
// digit_font_size: number
// digit_radius: string
// digit_font_weight: number
// label_font_size: number
// label_font_weight: number
// unit_font_weight: number
// item_vec: Array<BarItem>
// bar_arc // todo: cfg-reader待接入
// bar_color // todo: cfg-reader待接入
export const dspBarGroupConfig: Array<DspBarGroupConfigType> = [
  ...baseConfig,
  {
    key: 'digit_radius',
    name: 'Digit Radius',
    type: 'Slider',
    field: 'digitRadius',
    cast: 'string',
    range: [0, 10],
    comments: 'Digit box的圆角弧度'
  },
  {
    key: 'bar_arc',
    name: 'Bar Arc',
    type: 'Switch',
    field: 'barArc',
    range: [0, 1],
    comments: 'bar 的圆角弧度'
  },
  {
    key: 'bar_color',
    name: 'Bar Color',
    type: 'Color',
    field: 'barColor',
    comments: 'bar 背景条颜色'
  },
  {
    key: 'digit_font_weight',
    name: 'Digit Font Weight',
    type: 'Slider',
    field: 'digitFontWeight',
    range: [100, 900],
    step: 100,
    comments: 'digit字体的粗细程度'
  },
  {
    key: 'label_space',
    name: 'Label Space',
    type: 'Number',
    field: 'labelSpace',
    range: [0, 33],
    comments: '进度label与bar间的间距的最大值'
  },
  {
    key: 'unit_space',
    name: 'Unit Space',
    type: 'Number',
    field: 'unitSpace',
    range: [0, 33],
    comments: '单位与左侧 digital数字值之间的间距' // 无效
  },
  {
    key: 'label_color',
    name: 'Label Color',
    type: 'Color',
    field: 'labelColor',
    comments: 'Label标签的颜色'
  },
  {
    key: 'length',
    name: 'Bar Length',
    type: 'Number',
    field: 'barLength',
    comments: '条形图进度条的总长度'
  },
  {
    key: 'bar_height',
    name: 'Bar Height',
    type: 'Number',
    field: 'barProgressHeight',
    comments: '条形图的高度'
  },
  {
    key: 'format',
    name: 'Format',
    type: 'Text',
    field: 'format',
    comments: ''
  },
  {
    key: 'tic_pos',
    name: 'Tic Position',
    type: 'Select',
    field: 'barTicPosition',
    range: [
      { key: DspBarPosition.BELOW, text: 'Below' },
      { key: DspBarPosition.ABOVE, text: 'Above' },
      { key: DspBarPosition.LEFT, text: 'Left' },
      { key: DspBarPosition.RIGHT, text: 'Right' }
    ],
    comments:
      'Below | Above | Left | Right （刻度条在下面 | 刻度条在上面 | 刻度条在左边 | 刻度条在右边）'
  },
  {
    key: 'dir',
    name: 'Direction',
    type: 'Select',
    field: 'barDir',
    range: [
      { key: DspBarDir.HORIZONTAL, text: 'Horizontal' },
      { key: DspBarDir.VERTICAL, text: 'Vertical' }
    ],
    comments: 'Horizontal | Vertical 支持 水平和垂直两种类型的bar'
  },
  {
    key: 'shading',
    name: 'Box Shading',
    type: 'Number',
    field: 'shading',
    comments: '边框宽度值'
  },
  {
    key: 'spacing',
    name: 'Bar Spacing',
    type: 'Slider',
    range: [0, 20],
    field: 'barSpacing',
    comments: 'Bar间的间距'
  },
  {
    key: 'param_box',
    name: 'Param Box',
    type: 'Select',
    range: [
      { key: DspBarParamBox.NONE, text: 'none' },
      { key: DspBarParamBox.OUTLINE, text: 'outline' },
      { key: DspBarParamBox.FILLED, text: 'filled' }
    ],
    field: 'paramBox',
    comments:
      'none | outline | filled（当为outline时显示为条形图和数字对象的边框，为filled时显示为条形图的边框和数值对象的背景）'
  },
  {
    key: 'param_box_color',
    name: 'Param Box Color',
    type: 'Color',
    field: 'paramBoxColor',
    comments: '条形图边框和数字对象的边框颜色或背景颜色' // 不生效
  },
  {
    key: 'limit_width',
    name: 'Limit Width',
    type: 'Number',
    field: 'barLimitWidth',
    range: [0, 20],
    comments: '刻度条的宽度'
  },
  {
    key: 'label_font_size',
    name: 'Label Font Size',
    type: 'Slider',
    range: [5, 100],
    field: 'labelFontSize',
    comments: 'label字体大小'
  },
  {
    key: 'label_font_weight',
    name: 'Label Font Weight',
    type: 'Slider',
    field: 'labelFontWeight',
    range: [100, 900],
    step: 100,
    comments: 'label字体粗细'
  },
  {
    key: 'unit_font_weight',
    name: 'Unit Font Weight',
    type: 'Slider',
    field: 'unitFontWeight',
    range: [100, 900],
    step: 100,
    comments: 'unit字体粗细'
  },
  {
    key: 'unit_font_size',
    name: 'Unit Font Size',
    type: 'Slider',
    field: 'unitFontSize',
    comments: '单位字体大小',
    range: [5, 100]
  },
  {
    key: 'tic_font_size',
    name: 'Tic Font Size',
    type: 'Slider',
    range: [5, 100],
    field: 'barTicFontSize',
    comments: '刻度字体大小'
  },
  {
    key: 'digit_font_size',
    name: 'Digit Font Size',
    type: 'Slider',
    range: [5, 100],
    field: 'digitFontSize',
    comments: '数字字体大小'
  },
  {
    key: 'item_vec',
    name: 'BarList',
    type: 'Table',
    field: 'barList',
    column: [
      'Param Id',
      'Label',
      'Min',
      'Max',
      'Tic Interval',
      'Label Interval',
      'Units',
      'Target',
      'Corr'
    ],
    row: dspBarConfig,
    comments: ''
  }
]

export const dspBarDefault: Record<string, any> = {
  ...baseDefault,
  width: 425,
  height: 58,
  digit_radius: '5',
  digit_font_weight: 400,
  label_space: 5,
  bar_arc: 0,
  bar_color: 'Black',
  unit_space: 3,
  label_color: 'Black',
  length: 200,
  bar_height: 20,
  format: '8.2',
  tic_pos: DspBarPosition.BELOW,
  dir: DspBarDir.HORIZONTAL,
  shading: 0,
  spacing: 5,
  param_box: DspBarParamBox.NONE,
  param_box_color: 'Black',
  limit_width: 5,
  unit_font_size: 23,
  label_font_size: 23,
  label_font_weight: 400,
  unit_font_weight: 400,
  tic_font_size: 20,
  digit_font_size: 23,
  item_vec: [
    {
      param_id: 'None',
      label: 'default',
      min: 0,
      max: 1000,
      tic_inter: 100,
      label_inter: 2,
      units: 'default',
      target: 'None',
      corr: 'None'
    }
  ]
}
