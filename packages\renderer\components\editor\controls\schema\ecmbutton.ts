import { DspEcmButtonStyle, DspEcmButtonType, dspEcmButtonTypes } from '@wuk/wui'
import { baseConfig, BaseConfigType, baseDefault } from '../common'
import {
  ColorConfigType,
  ObjectConfigType,
  ParamListConfigType,
  SelectConfigType,
  SliderConfigType,
  SwitchConfigType,
  TableConfigType
} from '../types'

export type DspEcmButtonConfigType =
  | BaseConfigType
  | ColorConfigType
  | SliderConfigType
  | SelectConfigType<number | string | (typeof dspEcmButtonTypes)[number]>
  | SwitchConfigType<number>
  | ObjectConfigType<DspEcmButtonConfigType>
  | TableConfigType<BaseConfigType | ColorConfigType | ParamListConfigType>
// param_id: string
// wait_param_id: string
// type: string
// label_color: string
// warn_color: string
// off_color: string
// radius: string
// font_size: number
// font_weight: number
// default_state: number
// has_panel: boolean      //TODO: 组件未实现
// panel: ECMButtonPanel
// state_vec: Array<ECMButtonState>   //TODO: 未对接

// ECMButtonPanel {
//   style: number
//   line_width: number
//   line_color: string
//   box_color: string
//   shading: number
//   label: string
// }

// ECMButtonState {
//   state: number
//   label: string
//   on_color: string
//   has_wait: boolean
//   wait_msg: CalMsg
//   wait_vec: Array<string>
//   has_lock: boolean
//   lock_msg: CalMsg
//   lock_vec: Array<string>
//   has_init: boolean
//   init_msg: CalMsg
//   init_cal_vec: Array<string>
//   has_main: boolean
//   main_msg: CalMsg
//   main_cal_vec: Array<string>
//   has_final: boolean
//   final_msg: CalMsg
//   final_cal_vec: Array<string>
// }
export const dspEcmButtonConfig: Array<
  DspEcmButtonConfigType | ObjectConfigType<DspEcmButtonConfigType>
> = [
  ...baseConfig,
  {
    key: 'param_id',
    name: 'Param ID',
    type: 'Text',
    comments: '参数ID'
  },
  {
    key: 'type',
    name: 'Type',
    type: 'Select',
    range: [
      { key: DspEcmButtonType.MOMENTARY, text: 'MOMENTARY' },
      { key: DspEcmButtonType.RADIO, text: 'RADIO' },
      { key: DspEcmButtonType.TOGGLE, text: 'TOGGLE' },
      { key: DspEcmButtonType.LIGHT_SWITCH, text: 'LIGHTSWITCH' }
    ],
    dataType: DspEcmButtonType.MOMENTARY
  },
  {
    key: 'label_color',
    name: 'Label Color',
    type: 'Color',
    field: 'labelColor',
    comments: 'Label 颜色'
  },
  {
    key: 'warn_color',
    name: 'Warn Color',
    type: 'Color',
    field: 'warnColor',
    comments: 'Warn 颜色'
  },
  {
    key: 'off_color',
    name: 'Off Color',
    type: 'Color',
    field: 'offColor',
    comments: 'Off 颜色'
  },
  {
    key: 'radius',
    name: 'Radius',
    type: 'Slider',
    range: [0, 50],
    cast: 'string',
    field: 'buttonRadius',
    comments: '按钮的圆角半径'
  },
  {
    key: 'font_size',
    name: 'Font Size',
    field: 'labelFontSize',
    comments: '字体大小',
    type: 'Slider',
    range: [5, 100]
  },
  {
    key: 'font_weight',
    name: 'Font Weight',
    type: 'Slider',
    field: 'labelFontWeight',
    comments: '按钮内部文本字体粗细',
    step: 100,
    range: [100, 900]
  },
  {
    key: 'default_state',
    name: 'Default State',
    type: 'Number',
    field: 'defaultState'
  },
  {
    key: 'has_panel',
    name: 'Has Panel',
    type: 'Switch',
    field: 'isPanel',
    comments: '是否显示面板',
    range: [0, 1]
  },
  {
    key: 'panel',
    name: 'Panel',
    type: 'Object',
    field: 'panel',
    childs: [
      {
        key: 'style',
        name: 'Style',
        type: 'Select',
        range: [
          { key: DspEcmButtonStyle.NOPANEL, text: 'noPanel' },
          { key: DspEcmButtonStyle.FRAME, text: 'Frame' },
          { key: DspEcmButtonStyle['3-D'], text: '3-D' },
          { key: DspEcmButtonStyle.FILLED_FRAME, text: 'Filled Frame' }
        ],
        childs: {
          [DspEcmButtonStyle.NOPANEL]: [],
          [DspEcmButtonStyle.FRAME]: [
            {
              key: 'line_width',
              name: 'Line Width',
              type: 'Slider',
              field: 'lineWidth',
              range: [0, 100]
            },
            {
              key: 'line_color',
              name: 'Line Color',
              type: 'Color',
              field: 'lineColor'
            },
            {
              key: 'shading',
              name: 'Shading',
              type: 'Slider',
              range: [0, 10]
            },
            {
              key: 'label',
              name: 'Label',
              type: 'Text'
            }
          ],
          [DspEcmButtonStyle['3-D']]: [
            {
              key: 'box_color',
              name: 'Box Color',
              type: 'Color',
              field: 'boxColor'
            },
            {
              key: 'shading',
              name: 'Shading',
              type: 'Slider',
              range: [0, 10]
            },
            {
              key: 'label',
              name: 'Label',
              type: 'Text'
            }
          ],
          [DspEcmButtonStyle.FILLED_FRAME]: [
            {
              key: 'line_width',
              name: 'Line Width',
              type: 'Slider',
              field: 'lineWidth',
              range: [0, 100]
            },
            {
              key: 'line_color',
              name: 'Line Color',
              type: 'Color',
              field: 'lineColor'
            },
            {
              key: 'box_color',
              name: 'Box Color',
              type: 'Color',
              field: 'boxColor'
            },
            {
              key: 'shading',
              name: 'Shading',
              type: 'Slider',
              range: [0, 10]
            },
            {
              key: 'label',
              name: 'Label',
              type: 'Text'
            }
          ]
        }
      }
    ]
  },
  {
    key: 'state_vec',
    name: 'State List',
    type: 'Table',
    field: 'stateList',
    column: ['State', 'Label', 'Wait-For Condition', 'On Color'],
    row: [
      {
        key: 'state',
        name: 'State',
        type: 'Number'
      },
      {
        key: 'label',
        name: 'Label',
        type: 'Text'
      },
      {
        key: 'wait_vec',
        name: 'Wait-For Condition',
        type: 'ParamList'
      },
      {
        key: 'on_color',
        name: 'On Color',
        type: 'Color',
        field: 'onColor'
      }
    ],
    comments: 'State List'
  }
]
export const dspEcmButtonDefault: Record<string, any> = {
  ...baseDefault,
  width: 200,
  height: 60,
  param_id: 'None',
  type: DspEcmButtonType.MOMENTARY,
  label_color: 'Black',
  warn_color: 'NewYellow',
  off_color: 'LightGray',
  radius: '5',
  font_size: 23,
  font_weight: 400,
  default_state: 0,
  has_panel: 0,
  panel: {
    style: DspEcmButtonStyle.NOPANEL,
    line_width: 0,
    line_color: 'Black',
    box_color: 'White',
    shading: 0,
    label: ''
  },
  state_vec: [
    {
      label: 'ON',
      state: 1,
      on_color: 'Green',
      wait_vec: ['']
    },
    {
      label: 'OFF',
      state: 0,
      on_color: 'LightGray',
      wait_vec: ['']
    }
  ]
}
