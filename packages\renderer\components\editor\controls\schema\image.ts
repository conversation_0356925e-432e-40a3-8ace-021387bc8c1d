import { baseConfig, BaseConfigType, baseDefault } from '../common'
import { FileConfigType, NumberConfigType } from '../types'

export type DspImageConfigType = BaseConfigType | NumberConfigType | FileConfigType
export const dspImageConfig: Array<DspImageConfigType> = [
  ...baseConfig,
  {
    key: 'image_name',
    name: 'File Name',
    type: 'File',
    field: 'imageSrc',
    comments: 'The name of the image file',
    required: true
  }
]
export const dspImageDefault: Record<string, any> = {
  ...baseDefault,
  width: 80,
  height: 80,
  image_name: 'loongair.png'
}
