import {
  WuiDspBar,
  WuiDspBarChart,
  WuiDspBox,
  WuiDspButton,
  WuiDspDigitalChart,
  WuiDspEcmButton,
  WuiDspFuncButton,
  WuiDspGauge,
  WuiDspImage,
  WuiDspIndicator,
  WuiDspInput,
  WuiDspLine,
  WuiDspPlot,
  WuiDspString,
  WuiDspSwitch,
  WuiDspText
} from '@wuk/wui'
import { SFCWithInstall } from '@wuk/wui/dist/utils'

export const schemaTypes: Record<string, SFCWithInstall<any>> = {
  WuiDspBar,
  WuiDspBox,
  WuiDspButton,
  WuiDspDigitalChart,
  WuiDspEcmButton,
  WuiDspFuncButton,
  WuiDspGauge,
  WuiDspImage,
  WuiDspIndicator,
  WuiDspInput,
  WuiDspLine,
  WuiDspPlot,
  WuiDspString,
  WuiDspSwitch,
  WuiDspBarChart,
  WuiDspText
}

export type SchemaTypes = typeof schemaTypes
export type ComponentType = keyof SchemaTypes

export * from './text'
export * from './bar'
export * from './box'
export * from './button'
export * from './digital'
export * from './ecmbutton'
export * from './funcbutton'
export * from './gauge'
export * from './image'
export * from './indicator'
export * from './input'
export * from './line'
export * from './plot'
export * from './string'
export * from './switch'
