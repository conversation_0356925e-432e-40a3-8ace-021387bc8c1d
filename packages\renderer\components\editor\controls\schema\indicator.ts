import { DspIndicatorType } from '@wuk/wui'
import { baseConfig, BaseConfigType, baseDefault } from '../common'
import {
  ColorConfigType,
  OperatorType,
  ParamConfigType,
  SelectConfigType,
  SliderConfigType,
  TableConfigType
} from '../types'

export type DspIndicatorConfigType =
  | BaseConfigType
  | ColorConfigType
  | SelectConfigType<number | string>
  | SliderConfigType
  | ParamConfigType

export const dspIndicatorTypes = Object.values(DspIndicatorType)

// param_id: string
// text_color: string
// font_size: number
// font_weight: number
// radius: string
// type: string
// label: string
// item_vec: Array<IndicatorItem>
// box_color: string
// border_color: string // todo: cfg-reader待接入
// border_width: number // todo: cfg-reader待接入

// IndicatorItem {
//   color: string
//   op: string
//   test_value_id: string
//   test_string: string
// }
export const dspIndicatorConfig: Array<
  DspIndicatorConfigType | TableConfigType<DspIndicatorConfigType>
> = [
  ...baseConfig,
  {
    key: 'param_id',
    name: 'Param Id',
    type: 'Param',
    required: true
  },
  {
    key: 'box_color',
    name: 'Box Color',
    type: 'Color',
    field: 'indicatorBoxColor'
  },
  {
    key: 'border_color',
    name: 'Border Color',
    type: 'Color',
    field: 'indicatorBorderColor'
  },
  {
    key: 'border_width',
    name: 'Border Width',
    type: 'Slider',
    range: [0, 30],
    field: 'indicatorBorderWidth'
  },
  {
    key: 'font_size',
    name: 'Label Font Size',
    type: 'Slider',
    field: 'indicatorLabelFontSize',
    range: [8, 72],
    comments: 'indicator label 字体大小'
  },
  {
    key: 'font_weight',
    name: 'Label Font Weight',
    type: 'Slider',
    field: 'indicatorLabelFontWeight',
    range: [100, 900],
    step: 100,
    comments: 'indicator label 字体粗细'
  },
  {
    key: 'radius',
    name: 'Box Radius',
    type: 'Slider',
    field: 'indicatorBoxRadius',
    range: [0, 100],
    cast: 'string',
    comments: 'indicator box radius'
  },
  {
    key: 'type',
    name: 'Type',
    type: 'Select',
    field: 'type',
    comments: 'Indicator Type',
    range: [
      { key: DspIndicatorType.STATUS, text: 'Status' },
      { key: DspIndicatorType.LIMIT, text: 'Limit' },
      { key: DspIndicatorType.LIGHT, text: 'Light' }
    ]
  },
  {
    key: 'label',
    name: 'Label',
    type: 'Text',
    field: 'indicatorLabel',
    comments: 'indicator 显示在中间的值 - 仅在type为LIMIT时配置'
  },
  {
    key: 'text_color',
    name: 'Label Color',
    type: 'Color',
    field: 'indicatorLabelColor',
    comments: 'indicator 显示在中间的字体颜色 - 仅在type为LIMIT时配置'
  },
  {
    key: 'item_vec',
    name: '',
    type: 'Table',
    column: ['Color', 'Op', 'Test Value', 'String'],
    row: [
      {
        key: 'color',
        type: 'Color',
        name: 'Color'
      },
      {
        key: 'op',
        type: 'Select',
        name: 'Op',
        range: [
          { key: OperatorType.Greater, text: OperatorType.Greater },
          { key: OperatorType.Less, text: OperatorType.Less },
          { key: OperatorType.Equal, text: OperatorType.Equal }
        ],
        default: OperatorType.Greater
      },
      {
        key: 'test_value_id',
        type: 'Param',
        field: 'testValueId',
        name: 'Test Value ID'
      },
      {
        key: 'test_string',
        type: 'Text',
        name: 'Test String',
        field: 'testString'
      }
    ],
    field: 'stateList'
  }
]

export const dspIndicatorDefault: Record<string, any> = {
  ...baseDefault,
  param_id: 'None',
  width: 200,
  height: 60,
  type: DspIndicatorType.STATUS,
  text_color: 'White',
  box_color: 'Black',
  font_size: 23,
  font_weight: 400,
  radius: '5',
  label: '',
  border_color: 'Black',
  border_width: 3,
  item_vec: [
    {
      color: 'LightGray',
      op: '=',
      test_value_id: '',
      test_string: 'test'
    }
  ]
}
