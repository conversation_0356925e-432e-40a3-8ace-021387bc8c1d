import { baseConfig, BaseConfigType, baseDefault } from '../common'
import {
  ColorConfigType,
  ParamConfigType,
  SelectConfigType,
  SliderConfigType,
  SwitchConfigType
} from '../types'

export type DspInputConfigType =
  | BaseConfigType
  | ColorConfigType
  | SliderConfigType
  | SwitchConfigType
  | ParamConfigType

export enum DspInputParamBox {
  NONE = 0,
  OUTLINE = 1,
  FILLED = 2
}

// param_id: string
// label: string
// label_color: string
// max: number
// min: number
// inter_val: number
// delta: string
// input_width: number
// prec: number
// bar: number
// left_label: string
// calc_label: string
// right_label: string
// param_box: number
// param_box_color: string
// shading: number
// digit_font_size: number
// digit_space: number
// label_font_size: number
// label_font_weight: number
// digit_radius: string
// digit_font_weight: number
export const dspInputConfig: Array<
  DspInputConfigType | SelectConfigType<number, DspInputConfigType>
> = [
  ...baseConfig,
  {
    key: 'digit_radius',
    name: 'Digit Radius',
    type: 'Slider',
    field: 'digitRadius',
    range: [0, 10],
    cast: 'string',
    comments: 'Digit box的圆角弧度'
  },
  {
    key: 'digit_font_weight',
    name: 'Digit Font Weight',
    type: 'Slider',
    field: 'digitFontWeight',
    range: [100, 900],
    step: 100,
    comments: 'digit字体的粗细程度'
  },
  {
    key: 'param_id',
    type: 'Param',
    required: true
  },
  {
    key: 'label',
    name: 'Label',
    type: 'Text',
    field: 'mainLabel',
    comments: '主要的label值 - 显示在头部'
  },
  {
    key: 'label_color',
    name: 'Label Color',
    type: 'Color',
    field: 'labelColor',
    comments: '头部 label颜色'
  },
  {
    key: 'max',
    name: 'Max Value',
    type: 'Number',
    field: 'maxValue',
    comments: 'Input最大值'
  },
  {
    key: 'min',
    name: 'Min Value',
    type: 'Number',
    field: 'minValue',
    comments: 'Input最小值'
  },
  {
    key: 'inter_val',
    name: 'Interval',
    type: 'Number',
    field: 'ticInterval',
    comments: 'Bar刻度线之间的刻度值'
  },
  {
    key: 'delta',
    name: 'Delta',
    type: 'Slider',
    field: 'delta',
    range: [0.1, 10],
    step: 0.1,
    comments: '每次增加或减少的值'
  },
  {
    key: 'left_label',
    name: 'Left Label',
    type: 'Text',
    field: 'leftLabel',
    comments: '底部左侧按钮的label值'
  },
  {
    key: 'right_label',
    name: 'Right Label',
    type: 'Text',
    field: 'rightLabel',
    comments: '底部右侧按钮的label值'
  },
  {
    key: 'calc_label',
    name: 'Calc Label',
    type: 'Text',
    field: 'calcLabel',
    comments: '底部中间按钮的label值'
  },
  {
    key: 'param_box',
    name: 'Param Box',
    type: 'Select',
    field: 'paramBox',
    comments: 'parambox属性有三个值：0：none 无（透明） 1：outline 边框 2：filled 填充',
    range: [
      { key: DspInputParamBox.NONE, text: 'None' },
      { key: DspInputParamBox.OUTLINE, text: 'Outline' },
      { key: DspInputParamBox.FILLED, text: 'Filled' }
    ]
  },
  {
    key: 'param_box_color',
    name: 'Param Box Color',
    type: 'Color',
    field: 'paramBoxColor',
    comments: 'parambox为1时中间digital的边框颜色，为2时中间digital的背景/边框颜色'
  },
  {
    key: 'shading',
    name: 'Shading',
    type: 'Slider',
    field: 'shading',
    range: [0, 10],
    comments: '边框的宽度'
  },
  {
    key: 'digit_font_size',
    name: 'Digit Font Size',
    type: 'Slider',
    field: 'digitFontSize',
    range: [5, 100],
    comments: '中间digit字体大小'
  },
  {
    key: 'digit_space',
    name: 'Digit Space',
    type: 'Number',
    field: 'digitSpace',
    comments: 'digit的上下外边距'
  },
  {
    key: 'label_font_size',
    name: 'Label Font Size',
    type: 'Slider',
    range: [5, 100],
    field: 'labelFontSize',
    comments: '头部label字体大小'
  },
  {
    key: 'label_font_weight',
    name: 'Label Font Weight',
    type: 'Slider',
    field: 'labelFontWeight',
    range: [100, 900],
    step: 100,
    comments: 'label字体粗细'
  },
  {
    key: 'input_width',
    name: 'Digit Width',
    type: 'Slider',
    field: 'digitWidth',
    unit: 'ch',
    range: [0, 15],
    comments: 'digital总长度'
  },
  {
    key: 'prec',
    name: 'Digit Precision',
    type: 'Slider',
    field: 'digitPrec',
    range: [0, 6],
    comments: 'Digital小数位'
  },
  {
    key: 'bar',
    name: 'Bar',
    type: 'Switch',
    field: 'bar',
    range: [0, 1],
    comments: '是否隐藏bar'
  }
  // {
  //   key: 'show_keypad',
  //   name: 'Show Keypad',
  //   type: 'Switch',
  //   field: 'showKeypad',
  //   range: ['0', '1'],
  //   comments: '是否需要显示数字键盘'
  // }
  // {
  //   key: 'bar_tic_num',
  //   name: 'Bar Tic Num',
  //   type: 'Slider',
  //   field: 'barTicNum',
  //   range: [1, 20],
  //   comments: 'bar 刻度线数'
  // },
  // {
  //   key: 'bar_progress_height',
  //   name: 'Bar Progress Height',
  //   type: 'Slider',
  //   field: 'barProgressHeight',
  //   range: [1, 50],
  //   comments: 'progress bar 高度'
  // },
  // {
  //   key: 'bar_limit_color',
  //   name: 'Bar Limit Color',
  //   type: 'Color',
  //   field: 'barLimitColor',
  //   comments: 'bar limit条背景颜色'
  // },
  // {
  //   key: 'bar_color',
  //   name: 'Bar Color',
  //   type: 'Color',
  //   field: 'barColor',
  //   comments: 'bar 条背景颜色'
  // },
  // {
  //   key: 'bar_progress_color',
  //   name: 'Bar Progress Color',
  //   type: 'Color',
  //   field: 'barProgressColor',
  //   comments: 'progress bar 条进度颜色'
  // },
  // {
  //   key: 'control_btn_border_color',
  //   name: 'Control Button Border Color',
  //   type: 'Color',
  //   field: 'controlBtnBorderColor',
  //   comments: '控制按钮边框颜色'
  // },
  // {
  //   key: 'control_btn_bg_color',
  //   name: 'Control Button Background Color',
  //   type: 'Color',
  //   field: 'controlBtnBgColor',
  //   comments: '控制按钮背景颜色'
  // },
  // {
  //   key: 'control_btn_color',
  //   name: 'Control Button Color',
  //   type: 'Color',
  //   field: 'controlBtnColor',
  //   comments: '控制按钮字体颜色'
  // },
  // {
  //   key: 'control_btn_font_size',
  //   name: 'Control Button Font Size',
  //   type: 'Slider',
  //   field: 'controlBtnFontSize',
  //   range: [8, 48],
  //   comments: '控制按钮字体大小'
  // },
  // {
  //   key: 'control_btn_font_weight',
  //   name: 'Control Button Font Weight',
  //   type: 'Slider',
  //   field: 'controlBtnFontWeight',
  //   range: [100, 900],
  //   step: 100,
  //   comments: '控制按钮字体粗细'
  // },
  // {
  //   key: 'digit_font_weight',
  //   name: 'Digit Font Weight',
  //   type: 'Slider',
  //   field: 'digitFontWeight',
  //   range: [100, 900],
  //   step: 100,
  //   comments: '中间digit字体粗细'
  // },
  // {
  //   key: 'digit_color',
  //   name: 'Digit Color',
  //   type: 'Color',
  //   field: 'digitColor',
  //   comments: '中间digit的数据颜色'
  // }
]

export const dspInputDefault: Record<string, any> = {
  ...baseDefault,
  width: 300,
  height: 150,
  param_id: 'None',
  digit_radius: '5',
  digit_font_weight: 400,
  label_color: 'Black',
  label_font_size: 23,
  label_font_weight: 400,
  label: 'label',
  left_label: '<',
  right_label: '>',
  calc_label: 'Cal',
  max: 1000,
  min: 0,
  delta: 1,
  inter_val: 10,
  param_box: DspInputParamBox.NONE,
  param_box_color: 'Black',
  shading: 0,
  bar: 1,
  digit_space: 3,
  input_width: 8,
  prec: 2,
  digit_font_size: 23
}
