import { DspPlotPosXType, DspPlotPosYType, DspPlotType } from '@wuk/wui'
import { baseConfig, BaseConfigType, baseDefault } from '../common'
import {
  ColorConfigType,
  MultiColorConfigType,
  ObjectConfigType,
  ParamConfigType,
  ParamListConfigType,
  SelectConfigType,
  TableConfigType
} from '../types'

export type DspPlotConfigType =
  | BaseConfigType
  | ParamConfigType
  | ColorConfigType
  | SelectConfigType<number | string, DspPlotConfigType>
  | ObjectConfigType<DspPlotConfigType>
  | TableConfigType<DspPlotConfigType>
  | MultiColorConfigType
  | ParamListConfigType

// plot_key: string // server端自动生成的不用设置
// type: string
// points: Array<string>
// spot_param_id: string
// line_width: number
// has_x_axis: boolean
// x_axis: XAxis
// has_reference_x: boolean
// reference_x: ReferenceX
// yaxis_vec: Array<Yaxis>
// has_hard_copy_plot: boolean
// hard_copy_plot: HardCopyPlot

// Yaxis {
//   param_id: string
//   top: string
//   height: string
//   tic_interval: string
//   x_intercept: string
//   axis_color: string
//   label_interval: string
//   grid: string
//   tic_position: string
//   label_position: string
//   bottom_to_x_axis: string
//   top_to_x_axis: string
//   color_vec: Array<string>
//   table_plot_vec: Array<TablePlot>
//   param_list: Array<string> TODO: 缺少
// }
// XAxis {
//   param_id: string
//   left: string
//   width: string
//   tic_interval: string
//   y_intercept: string
//   axis_color: string
//   label_interval: string
//   tic_position: string
//   label_position: string
// }
export const dspPlotConfig: Array<DspPlotConfigType> = [
  ...baseConfig,
  {
    key: 'spot_param_id',
    name: 'Spot Param Id',
    type: 'Param'
  },
  {
    key: 'type',
    name: 'Type',
    type: 'Select',
    range: [
      { key: DspPlotType.REALTIME, text: 'Real Time' },
      { key: DspPlotType.SCROLL, text: 'Scrolled' },
      { key: DspPlotType.REALTIMEPOINT, text: 'Real Time Point' },
      { key: DspPlotType.RECORDED, text: 'Recorded' },
      { key: DspPlotType.VIBSURVEY, text: 'Vib Survey' },
      { key: DspPlotType.PERFORMANCE, text: 'Performance' },
      { key: DspPlotType.SPECTRUMPLOT, text: 'Spectrum Plot' }
    ],
    parent: 'option'
  },
  {
    key: 'line_width',
    name: 'Line Width',
    type: 'Number',
    field: 'lineWidth',
    range: [0, 10],
    parent: 'option'
  },
  {
    key: 'x_axis',
    name: 'X Axis',
    type: 'Object',
    field: 'xAxisItem',
    parent: 'option',
    childs: [
      {
        key: 'param_id',
        name: 'Param ID',
        type: 'Param',
        field: 'paramId'
      },
      {
        key: 'axis_color',
        name: 'Axis Color',
        type: 'Color',
        field: 'axisColor'
      },
      {
        key: 'label_interval',
        name: 'Label Interval',
        type: 'Number',
        range: [0, Infinity],
        field: 'labelInterVal'
      },
      {
        key: 'label_position',
        name: 'Label Position',
        type: 'Select',
        range: [
          {
            key: DspPlotPosXType.ABOVE,
            text: 'Above'
          },
          {
            key: DspPlotPosXType.BELOW,
            text: 'Below'
          }
        ],
        field: 'labelPosition'
      },
      {
        key: 'tic_interval',
        name: 'Tic Interval',
        type: 'Number',
        range: [0, Infinity],
        field: 'ticInterVal'
      },
      {
        key: 'tic_position',
        name: 'Tic Position',
        type: 'Select',
        range: [
          {
            key: DspPlotPosXType.ABOVE,
            text: 'Above'
          },
          {
            key: DspPlotPosXType.BELOW,
            text: 'Below'
          }
        ],
        field: 'ticPosition'
      },
      {
        key: 'x_start',
        name: 'X Start',
        type: 'Number',
        field: 'xStart'
      },
      {
        key: 'x_end',
        name: 'x End',
        type: 'Number',
        field: 'xEnd'
      },
      {
        key: 'y_intercept',
        name: 'Y Intercept',
        type: 'Number',
        field: 'yIntercept'
      }
    ]
  },
  {
    key: 'reference_x',
    name: 'Reference X',
    type: 'Object',
    field: 'referenceXItem',
    parent: 'option',
    childs: [
      {
        key: 'y_intercept',
        name: 'Y Intercept',
        type: 'Number',
        field: 'yIntercept'
      },
      {
        key: 'tic_position',
        name: 'Tic Position',
        type: 'Select',
        range: [
          {
            key: DspPlotPosXType.ABOVE,
            text: 'Above'
          },
          {
            key: DspPlotPosXType.BELOW,
            text: 'Below'
          }
        ],
        field: 'ticPosition'
      },
      {
        key: 'label_position',
        name: 'Label Position',
        type: 'Select',
        range: [
          {
            key: DspPlotPosXType.ABOVE,
            text: 'Above'
          },
          {
            key: DspPlotPosXType.BELOW,
            text: 'Below'
          }
        ],
        field: 'labelPosition'
      }
    ]
  },
  {
    key: 'yaxis_vec',
    name: 'YAxis',
    type: 'Table',
    field: 'yAxisItemList',
    column: [
      'Param ID',
      'color List',
      'Top',
      'Height',
      'Tic Interval',
      'X Intercept',
      'Axis Color',
      'Label Interval',
      'Grid',
      'Tic Position',
      'Label Position',
      'Param List'
    ],
    row: [
      {
        key: 'param_id',
        name: 'Param ID',
        type: 'Param',
        field: 'paramId'
      },
      {
        key: 'color_vec',
        name: 'Color List',
        type: 'MultiColor'
      },
      {
        key: 'y_start',
        name: 'Y Start',
        type: 'Number',
        field: 'yStart'
      },
      {
        key: 'y_end',
        name: 'Y End',
        type: 'Number',
        field: 'yEnd'
      },
      {
        key: 'tic_interval',
        name: 'Tic Interval',
        type: 'Number',
        range: [0, Infinity],
        field: 'ticInterVal'
      },
      {
        key: 'x_intercept',
        name: 'X Intercept',
        type: 'Number',
        field: 'xIntercept'
      },
      {
        key: 'axis_color',
        name: 'Axis Color',
        type: 'Color',
        field: 'axisColor'
      },
      {
        key: 'label_interval',
        name: 'Label Interval',
        type: 'Number',
        range: [0, Infinity],
        field: 'labelInterVal'
      },
      {
        key: 'grid',
        name: 'Grid',
        type: 'Switch',
        range: ['0', '1']
      },
      {
        key: 'tic_position',
        name: 'Tic Position',
        type: 'Select',
        field: 'ticPosition',
        range: [
          {
            key: DspPlotPosYType.LEFT,
            text: 'Left'
          },
          {
            key: DspPlotPosYType.RIGHT,
            text: 'Right'
          }
        ]
      },
      {
        key: 'label_position',
        name: 'Label Position',
        type: 'Select',
        field: 'labelPosition',
        range: [
          {
            key: DspPlotPosYType.LEFT,
            text: 'Left'
          },
          {
            key: DspPlotPosYType.RIGHT,
            text: 'Right'
          }
        ]
      },
      {
        key: 'param_list',
        name: 'Param List',
        type: 'ParamList',
        field: 'paramList',
        comments: 'scrolled类型时，用于切换y轴绑定的参数'
      }
    ],
    parent: 'option'
  }
]
export const dspPlotDefault: Record<string, any> = {
  ...baseDefault,
  width: 350,
  height: 250,
  line_width: 1,
  type: DspPlotType.REALTIME,
  yaxis_vec: [
    {
      param_id: 'None',
      y_start: '0',
      y_end: '5',
      tic_interval: '1',
      x_intercept: '0.000000',
      axis_color: 'Black',
      label_interval: '1',
      grid: '0',
      tic_position: 'RIGHT',
      label_position: 'LEFT',
      color_vec: ['White']
    }
  ],
  x_axis: {
    axis_color: 'Black',
    label_interval: '2',
    label_position: 'BELOW',
    tic_interval: '10',
    tic_position: 'ABOVE',
    x_end: '100',
    x_start: '0',
    y_intercept: '0',
    param_id: 'None'
  },
  reference_x: null
}
