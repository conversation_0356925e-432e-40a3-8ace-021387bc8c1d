import { buildProps, definePropType } from '@wuk/wui/dist/utils'
import { defineComponent, ExtractPropTypes } from 'vue'
import { useDrag } from 'vue3-dnd'

import './index.scss'

import { toRefs } from '@vueuse/core'
import { prefix } from '../utils'
import { ItemTypes } from './common'
import { BaseDefineType } from './types'

export type FieldType = [string, string, string?, boolean?, string?]
export interface TargetBoxItem<T = string> {
  type: T
  imgname?: string
  name: string
  displayName: string
  icon: any
  parent?: T
  config?: Array<BaseDefineType>
  fields?: Record<string, FieldType>
  default?: Record<string, any>
  category: 'component'
}

export const targetBoxProps = buildProps({
  item: {
    type: definePropType<TargetBoxItem>(Object),
    default: () => {}
  }
})
export type TargetBoxProps = ExtractPropTypes<typeof targetBoxProps>

export const TargetBox = defineComponent({
  name: 'TargetBox',
  props: targetBoxProps,
  setup(props, { expose, slots }) {
    const [collect, drag, preview] = useDrag(() => {
      const { item } = props
      return {
        type: ItemTypes.CONTROL,
        item,
        collect: monitor => ({
          isDragging: monitor.isDragging(),
          opacity: monitor.isDragging() ? 0.4 : 1
        })
      }
    })
    const { opacity, isDragging } = toRefs(collect)

    const renderIcon = () => {
      const Icon = props.item?.icon
      return (Icon && <Icon style={{ width: '1em', height: '1em' }} />) || <></>
    }

    return () => (
      <div ref={drag} class={prefix('targetbox')} style={{ opacity: opacity.value }}>
        <div class={prefix('targetbox-icon')}>{renderIcon()}</div>
        <div class={prefix('targetbox-text')}>{props.item?.displayName || ''}</div>
      </div>
    )
  }
})
