import { camelize, IObject } from '@daybrush/utils'
import { Editor, Memory } from '../states'
import { nextTick, VNode } from 'vue'
import { prefix } from '../utils'
import { IconView } from './view'

export interface Maker {
  tag: string
  attrs: IObject<any>
  style: IObject<any>
}

export type MakerFunction = (memory: Memory) => Maker
export type MakeThenFunction = (target: HTMLElement | SVGElement) => any

export class Icon {
  public static id = ''
  public static maker?: MakerFunction
  public static makeThen: MakeThenFunction = () => {}

  public keys: string[] = []
  constructor(public readonly editor: Editor) {}

  public init() {
    const keys = this.keys
    if (keys.length) {
      this.editor.keywords.keydown(
        keys,
        e => {
          if (e.ctrlKey || e.metaKey) {
            return false
          }
          this.editor.events.trigger('on-icon-click')
        },
        (this.constructor as any).id
      )
    }
  }

  get id() {
    return (this.constructor as any).id
  }

  public renderIcon(): any {}

  public on(eventName: string, handler: (...args: any[]) => void) {
    this.editor.events.on(eventName, handler)
  }

  public off(eventName: string, handler: (...args: any[]) => void) {
    this.editor.events.off(eventName, handler)
  }

  public renderSubIcons(): VNode[] {
    return []
  }

  public blur = () => {
    this.editor.events.trigger('on-icon-blur')
  }

  public renderSubIcon(IconClass: typeof Icon, id: string, isSelect: boolean) {
    const icon = new IconClass(this.editor)
    return (
      <div
        key={id}
        class={prefix('icon', 'sub-icon', isSelect ? 'selected' : '')}
        onClick={() => {
          this.onSubSelect(id)
        }}>
        <IconView icon={icon} selected={false} />
        <div class={prefix('sub-icon-label')}>{camelize(` ${id}`)}</div>
      </div>
    )
  }

  public onSubSelect(id: string) {}

  protected async forceUpdate(callback?: () => any) {
    this.editor.events.trigger('on-icon-update')

    await nextTick(() => {
      callback?.()
    })
  }
}
