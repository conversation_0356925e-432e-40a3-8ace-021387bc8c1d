import { Icon } from './base'
import { Memory } from '../states'
import { prefix } from '../utils'

export class CircleIcon extends Icon {
  public static id = 'Circle'
  public static maker = (memory: Memory) => ({
    tag: 'div',
    attrs: {},
    style: {
      'background-color': memory.get('background-color'),
      'border-radius': '50%'
    }
  })
  public renderIcon() {
    return (
      <svg class={prefix('svg-icon')} viewBox='0 0 73 73'>
        <ellipse
          fill='#555'
          cx='36.5'
          cy='36.5'
          rx='15'
          ry='15'
          stroke-linejoin='round'
          stroke-width='3'
          // stroke="#fff"
        ></ellipse>
      </svg>
    )
  }
}
