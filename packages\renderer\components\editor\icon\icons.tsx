import { ImgHTMLAttributes } from 'vue'
import { ComponentRef } from '../../base'

import baricon from './images/icon_bar.svg'
import barGroupicon from './images/icon_barGroup.svg'
import boxicon from './images/icon_box.svg'
import btnicon from './images/icon_btn.svg'
import closeicon from './images/icon_close.svg'
import digitalicon from './images/icon_digital.svg'
import ecmbtnicon from './images/icon_ecmbtn.svg'
import funcbtnicon from './images/icon_funcbtn.svg'
import gaugeicon from './images/icon_guage.svg'
import imageicon from './images/icon_image.svg'
import inputicon from './images/icon_input.svg'
import lineicon from './images/icon_line.svg'
import ploticon from './images/icon_plot.svg'
import plusicon from './images/icon_plus.svg'
import questionicon from './images/icon_question.svg'
import subicon from './images/icon_sub.svg'
import switchicon from './images/icon_switch.svg'
import texticon from './images/icon_text.svg'
export const SvgIcon = ComponentRef<SVGSVGElement, Record<string, any>>(
  'SvgIcon',
  (props, ref, { slots }) => {
    return () => (
      <svg
        ref={ref}
        fill='var(--scena-editor-color-icon)'
        stroke='var(--scena-editor-color-icon)'
        {...props}>
        {slots.default?.()}
      </svg>
    )
  }
)
export const BoxIcon = ComponentRef<HTMLImageElement, ImgHTMLAttributes>(
  'BoxIcon',
  (props, ref, { slots }) => {
    return () => (
      <ImageIcon forRef={ref} forProps={{ src: boxicon, width: '0.8em', height: '0.8em' }} />
    )
  }
)
export const ImageIcon = ComponentRef<HTMLImageElement, ImgHTMLAttributes>(
  'ImageIcon',
  (props, ref, { slots }) => {
    return () => <img ref={ref} src={props.src} width={props.width} height={props.height} />
  }
)

export const BtnIcon = ComponentRef<HTMLImageElement, ImgHTMLAttributes>(
  'BtnIcon',
  (props, ref, { slots }) => {
    return () => <ImageIcon forRef={ref} forProps={{ src: btnicon }} />
  }
)

export const DigitalIcon = ComponentRef<HTMLImageElement, ImgHTMLAttributes>(
  'DigitalIcon',
  (props, ref, { slots }) => {
    return () => <ImageIcon forRef={ref} forProps={{ src: digitalicon }} />
  }
)

export const EcmBtnIcon = ComponentRef<HTMLImageElement, ImgHTMLAttributes>(
  'EcmBtnIcon',
  (props, ref, { slots }) => {
    return () => <ImageIcon forRef={ref} forProps={{ src: ecmbtnicon }} />
  }
)

export const FuncBtnIcon = ComponentRef<HTMLImageElement, ImgHTMLAttributes>(
  'FuncBtnIcon',
  (props, ref, { slots }) => {
    return () => <ImageIcon forRef={ref} forProps={{ src: funcbtnicon }} />
  }
)

export const ImageComponentIcon = ComponentRef<HTMLImageElement, ImgHTMLAttributes>(
  'ImageComponentIcon',
  (props, ref, { slots }) => {
    return () => <ImageIcon forRef={ref} forProps={{ src: imageicon }} />
  }
)

export const InputIcon = ComponentRef<HTMLImageElement, ImgHTMLAttributes>(
  'InputIcon',
  (props, ref, { slots }) => {
    return () => <ImageIcon forRef={ref} forProps={{ src: inputicon }} />
  }
)

export const GaugeIcon = ComponentRef<HTMLImageElement, ImgHTMLAttributes>(
  'GaugeIcon',
  (props, ref, { slots }) => {
    return () => <ImageIcon forRef={ref} forProps={{ src: gaugeicon }} />
  }
)

export const PlotIcon = ComponentRef<HTMLImageElement, ImgHTMLAttributes>(
  'PlotIcon',
  (props, ref, { slots }) => {
    return () => <ImageIcon forRef={ref} forProps={{ src: ploticon }} />
  }
)

export const LineIcon = ComponentRef<HTMLImageElement, ImgHTMLAttributes>(
  'LineIcon',
  (props, ref, { slots }) => {
    return () => <ImageIcon forRef={ref} forProps={{ src: lineicon }} />
  }
)

export const SwitchIcon = ComponentRef<HTMLImageElement, ImgHTMLAttributes>(
  'SwitchIcon',
  (props, ref, { slots }) => {
    return () => <ImageIcon forRef={ref} forProps={{ src: switchicon }} />
  }
)

export const TextIcon = ComponentRef<HTMLImageElement, ImgHTMLAttributes>(
  'TextIcon',
  (props, ref, { slots }) => {
    return () => <ImageIcon forRef={ref} forProps={{ src: texticon }} />
  }
)

export const BarIcon = ComponentRef<HTMLImageElement, ImgHTMLAttributes>(
  'BarIcon',
  (props, ref, { slots }) => {
    return () => <ImageIcon forRef={ref} forProps={{ src: baricon }} />
  }
)

export const BarGroupIcon = ComponentRef<HTMLImageElement, ImgHTMLAttributes>(
  'BarGroupIcon',
  (props, ref, { slots }) => {
    return () => <ImageIcon forRef={ref} forProps={{ src: barGroupicon }} />
  }
)
export const QuestionIcon = ComponentRef<HTMLImageElement, ImgHTMLAttributes>(
  'QuestionIcon',
  (props, ref, { slots }) => {
    return () => <ImageIcon forRef={ref} forProps={{ src: questionicon }} />
  }
)

export const MoveToolIcon = ComponentRef<SVGSVGElement, Record<string, any>>(
  'MoveToolIcon',
  (props, ref) => {
    return () => (
      <svg
        ref={ref}
        fill='var(--scena-editor-color-icon)'
        stroke='var(--scena-editor-color-icon)'
        {...props}>
        <path
          d='M 21,21 L 35,60 L 40,44 L 54,58 A 3,3 0,0,0, 58,54 L 44,40 L 60,35 L 21,21Z'
          stroke-linejoin='round'
          stroke-width='3'
          style={{ transformOrigin: '42px 42px', transform: 'rotate(10deg)' }}
        />
      </svg>
    )
  }
)

export const FontIcon = ComponentRef<SVGSVGElement, Record<string, any>>(
  'FontIcon',
  (props, ref) => {
    return () => (
      <SvgIcon forRef={ref} forProps={{ viewBox: '0 0 80 80', ...props }}>
        <path d='M64.286,17.81L20.714,17.81L20.714,29.56L29.214,23L39.262,23L39.262,55.476L27.77,61.262L27.77,62.071L57.23,62.071L57.23,61.262L45.738,55.476L45.738,23L55.786,23L64.286,29.56L64.286,17.81Z' />
      </SvgIcon>
    )
  }
)

export const CropIcon = ComponentRef<SVGSVGElement, Record<string, any>>(
  'CropIcon',
  (props, ref) => {
    return () => (
      <SvgIcon forRef={ref} forProps={{ viewBox: '0 0 80 80', ...props }}>
        <path
          d='M25,10L25,50L65,50   M10,25L50,25L50,65'
          style={{ stroke: 'var(--scena-editor-color-icon)', strokeWidth: 5, fill: 'none' }}
        />
      </SvgIcon>
    )
  }
)

export const ScenaIcon = ComponentRef<SVGSVGElement, Record<string, any>>(
  'ScenaIcon',
  (props, ref) => {
    return () => (
      <SvgIcon
        forRef={ref}
        forProps={{
          width: '100%',
          height: '100%',
          viewBox: '0 0 256 256',
          fill: 'none',
          stroke: '#fff',
          strokeWidth: '5',
          ...props
        }}>
        <rect x='32' y='80' width='192' height='96' />
        <path d='M224,176l-96,-0l-32,48l128,-0l0,-48Z' />
        <path d='M160,32l-128,0l-0,48l96,0l32,-48Z' />
        <path d='M88,176l-56,0l0,48l24,-0l32,-48Z' />
        <path d='M224,32l-24,-0l-32,48l56,0l0,-48Z' />
      </SvgIcon>
    )
  }
)

ScenaIcon.displayName = 'ScenaIcon'

export const RectIcon = ComponentRef<SVGSVGElement, Record<string, any>>(
  'RectIcon',
  (props, ref) => {
    return () => (
      <SvgIcon forRef={ref} forProps={{ viewBox: '0 0 73 73', ...props }}>
        <path
          d='M16.5,21.5 h40 a0,0 0 0 1 0,0 v30 a0,0 0 0 1 -0,0 h-40 a0,0 0 0 1 -0,-0 v-30 a0,0 0 0 1 0,-0 z'
          fill='#555'
          stroke-linejoin='round'
          stroke-width='3'
          stroke='#fff'></path>
      </SvgIcon>
    )
  }
)

export const RoundRectIcon = ComponentRef<SVGSVGElement, Record<string, any>>(
  'RoundRectIcon',
  (props, ref) => {
    return () => (
      <SvgIcon forRef={ref} forProps={{ viewBox: '0 0 73 73', ...props }}>
        <path
          d='M26.5,21.5 h20 a10,10 0 0 1 10,10 v10 a10,10 0 0 1 -10,10 h-20 a10,10 0 0 1 -10,-10 v-10 a10,10 0 0 1 10,-10 z'
          fill='#555'
          stroke-linejoin='round'
          stroke-width='3'
          stroke='#fff'></path>
      </SvgIcon>
    )
  }
)

export const OvalIcon = ComponentRef<SVGSVGElement, Record<string, any>>(
  'OvalIcon',
  (props, ref) => {
    return () => (
      <SvgIcon forRef={ref} forProps={{ viewBox: '0 0 73 73', ...props }}>
        <ellipse
          fill='#555'
          cx='36.5'
          cy='36.5'
          rx='20'
          ry='15'
          stroke-linejoin='round'
          stroke-width='3'
          stroke='#fff'></ellipse>
      </SvgIcon>
    )
  }
)

export const CircleIcon = ComponentRef<SVGSVGElement, Record<string, any>>(
  'OvalIcon',
  (props, ref) => {
    return () => (
      <SvgIcon forRef={ref} forProps={{ viewBox: '0 0 73 73', ...props }}>
        <ellipse
          fill='#555'
          cx='36.5'
          cy='36.5'
          rx='15'
          ry='15'
          stroke-linejoin='round'
          stroke-width='3'
          stroke='#fff'></ellipse>
      </SvgIcon>
    )
  }
)

export const PolygonIcon = ComponentRef<SVGSVGElement, Record<string, any>>(
  'PolygonIcon',
  (props, ref) => {
    return () => (
      <SvgIcon forRef={ref} forProps={{ viewBox: '0 0 73 73', ...props }}>
        <path
          d='M 20,15 L 10,35 L 20,55 L 35,45 L 40, 50 L 55,31 L 41,15 L 30, 25 Z'
          fill='#555'
          stroke-linejoin='round'
          stroke-width='3'
          stroke='#fff'></path>
      </SvgIcon>
    )
  }
)

export const TransformIcon = ComponentRef<SVGSVGElement, Record<string, any>>(
  'TransformIcon',
  (props, ref) => {
    return () => (
      <SvgIcon forRef={ref} forProps={{ viewBox: '0 0 80 80', ...props }}>
        {/* <rect x="20" y="20" width="40" height="40" stroke="#fff" strokeWidth="3" fill="rgba(255, 255, 255, 0.5)"></rect> */}
        <rect x='15' y='15' width='10' height='10'></rect>
        <rect x='35' y='15' width='10' height='10'></rect>
        <rect x='55' y='15' width='10' height='10'></rect>
        <rect x='15' y='35' width='10' height='10'></rect>
        <rect x='55' y='35' width='10' height='10'></rect>
        <rect x='15' y='55' width='10' height='10'></rect>
        <rect x='35' y='55' width='10' height='10'></rect>
        <rect x='55' y='55' width='10' height='10'></rect>
      </SvgIcon>
    )
  }
)

export const CheckedIcon = ComponentRef<SVGSVGElement, Record<string, any>>(
  'CheckedIcon',
  (props, ref) => {
    return () => (
      <SvgIcon forRef={ref} forProps={{ viewBox: '0 0 80 80', ...props }}>
        <path
          d='M 21,40 L 35,60 L 60,25 L35, 60, L 21,40 Z'
          stroke-linejoin='round'
          stroke-width='10'></path>
      </SvgIcon>
    )
  }
)

export const FolderIcon = ComponentRef<SVGSVGElement, Record<string, any>>(
  'FolderIcon',
  (props, ref) => {
    return () => (
      <SvgIcon forRef={ref} forProps={{ viewBox: '0 0 80 80', ...props }}>
        <path
          d='M 10,20 L 30,20 L 40,26 L70, 26, L 70,60 L 10,60 L 10,20 Z'
          stroke-linejoin='round'
          stroke-width='10'></path>
      </SvgIcon>
    )
  }
)

export const LayerIcon = ComponentRef<SVGSVGElement, Record<string, any>>(
  'LayerIcon',
  (props, ref) => {
    return () => (
      <SvgIcon forRef={ref} forProps={{ viewBox: '0 0 80 80', ...props }}>
        <path
          d='M40,20 L70,40 L40,60 L10,40 L40,20Z'
          stroke-width='3'
          style={{
            // fill: "rgba(255, 255, 255, 0.5)",
            transform: 'translateY(-7px)'
          }}></path>
        <path
          d='M40,20 L70,40 L40,60 L10,40 L40,20Z'
          stroke-width='3'
          style={{
            // fill: "rgba(255, 255, 255, 0.5)",
            transform: 'translateY(7px)'
          }}></path>
      </SvgIcon>
    )
  }
)

export const VisibleIcon = ComponentRef<SVGSVGElement, Record<string, any>>(
  'VisibleIcon',
  (props, ref) => {
    return () => (
      <SvgIcon
        forRef={ref}
        forProps={{ viewBox: '0 0 48 48', width: '48', height: '48', ...props }}>
        <path d='M24 31.5q3.55 0 6.025-2.475Q32.5 26.55 32.5 23q0-3.55-2.475-6.025Q27.55 14.5 24 14.5q-3.55 0-6.025 2.475Q15.5 19.45 15.5 23q0 3.55 2.475 6.025Q20.45 31.5 24 31.5Zm0-2.9q-2.35 0-3.975-1.625T18.4 23q0-2.35 1.625-3.975T24 17.4q2.35 0 3.975 1.625T29.6 23q0 2.35-1.625 3.975T24 28.6Zm0 9.4q-7.3 0-13.2-4.15Q4.9 29.7 2 23q2.9-6.7 8.8-10.85Q16.7 8 24 8q7.3 0 13.2 4.15Q43.1 16.3 46 23q-2.9 6.7-8.8 10.85Q31.3 38 24 38Zm0-15Zm0 12q6.05 0 11.125-3.275T42.85 23q-2.65-5.45-7.725-8.725Q30.05 11 24 11t-11.125 3.275Q7.8 17.55 5.1 23q2.7 5.45 7.775 8.725Q17.95 35 24 35Z' />
      </SvgIcon>
    )
  }
)

export const InvisibleIcon = ComponentRef<SVGSVGElement, Record<string, any>>(
  'InvisibleIcon',
  (props, ref) => {
    return () => (
      <SvgIcon
        forRef={ref}
        forProps={{ viewBox: '0 0 48 48', width: '48', height: '48', ...props }}>
        <path d='m31.45 27.05-2.2-2.2q1.3-3.55-1.35-5.9-2.65-2.35-5.75-1.2l-2.2-2.2q.85-.55 1.9-.8 1.05-.25 2.15-.25 3.55 0 6.025 2.475Q32.5 19.45 32.5 23q0 1.1-.275 2.175-.275 1.075-.775 1.875Zm6.45 6.45-2-2q2.45-1.8 4.275-4.025Q42 25.25 42.85 23q-2.5-5.55-7.5-8.775Q30.35 11 24.5 11q-2.1 0-4.3.4-2.2.4-3.45.95L14.45 10q1.75-.8 4.475-1.4Q21.65 8 24.25 8q7.15 0 13.075 4.075Q43.25 16.15 46 23q-1.3 3.2-3.35 5.85-2.05 2.65-4.75 4.65Zm2.9 11.3-8.4-8.25q-1.75.7-3.95 1.075T24 38q-7.3 0-13.25-4.075T2 23q1-2.6 2.775-5.075T9.1 13.2L2.8 6.9l2.1-2.15L42.75 42.6ZM11.15 15.3q-1.85 1.35-3.575 3.55Q5.85 21.05 5.1 23q2.55 5.55 7.675 8.775Q17.9 35 24.4 35q1.65 0 3.25-.2t2.4-.6l-3.2-3.2q-.55.25-1.35.375T24 31.5q-3.5 0-6-2.45T15.5 23q0-.75.125-1.5T16 20.15Zm15.25 7.1Zm-5.8 2.9Z' />
      </SvgIcon>
    )
  }
)

export const LinkIcon = ComponentRef<SVGSVGElement, Record<string, any>>(
  'LinkIcon',
  (props, ref) => {
    return () => (
      <SvgIcon
        forRef={ref}
        forProps={{ viewBox: '0 0 48 48', width: '48', height: '48', ...props }}>
        <path d='M22.5 34H14q-4.15 0-7.075-2.925T4 24q0-4.15 2.925-7.075T14 14h8.5v3H14q-2.9 0-4.95 2.05Q7 21.1 7 24q0 2.9 2.05 4.95Q11.1 31 14 31h8.5Zm-6.25-8.5v-3h15.5v3ZM25.5 34v-3H34q2.9 0 4.95-2.05Q41 26.9 41 24q0-2.9-2.05-4.95Q36.9 17 34 17h-8.5v-3H34q4.15 0 7.075 2.925T44 24q0 4.15-2.925 7.075T34 34Z' />
      </SvgIcon>
    )
  }
)

export const UnlinkIcon = ComponentRef<SVGSVGElement, Record<string, any>>(
  'UnlinkIcon',
  (props, ref) => {
    return () => (
      <SvgIcon
        forRef={ref}
        forProps={{ viewBox: '0 0 48 48', width: '48', height: '48', ...props }}>
        <path d='M37.5 33.45 35.05 31q2.55-.5 4.25-2.4 1.7-1.9 1.7-4.45 0-2.9-2.05-4.95-2.05-2.05-4.95-2.05h-7.75v-3H34q4.15 0 7.075 2.925T44 24.15q0 3.1-1.8 5.6-1.8 2.5-4.7 3.7Zm-7.8-7.8-3-3h5.05v3Zm11 19.55L3.15 7.65 5.3 5.5l37.55 37.55ZM22.5 34H14q-4.15 0-7.075-2.925T4 24q0-3.6 2.225-6.35Q8.45 14.9 11.9 14.2l2.8 2.8H14q-2.9 0-4.95 2.05Q7 21.1 7 24q0 2.9 2.05 4.95Q11.1 31 14 31h8.5Zm-6.25-8.5v-3h3.95l3 3Z' />
      </SvgIcon>
    )
  }
)

export const LightModeIcon = ComponentRef<SVGSVGElement, Record<string, any>>(
  'LightModeIcon',
  (props, ref) => {
    return () => (
      <SvgIcon
        forRef={ref}
        forProps={{ viewBox: '0 96 960 960', width: '48', height: '48', ...props }}>
        <path d='M479.843 709.334q55.49 0 94.491-38.844 39-38.843 39-94.333 0-55.49-38.844-94.491-38.843-39-94.333-39-55.49 0-94.491 38.844-39 38.843-39 94.333 0 55.49 38.844 94.491 38.843 39 94.333 39ZM480 776q-83 0-141.5-58.5T280 576q0-83 58.5-141.5T480 376q83 0 141.5 58.5T680 576q0 83-58.5 141.5T480 776ZM73.333 609.333q-14.166 0-23.75-9.617Q40 590.099 40 575.883q0-14.216 9.583-23.716 9.584-9.5 23.75-9.5h93.334q14.166 0 23.75 9.617Q200 561.901 200 576.117q0 14.216-9.583 23.716-9.584 9.5-23.75 9.5H73.333Zm720 0q-14.166 0-23.75-9.617Q760 590.099 760 575.883q0-14.216 9.583-23.716 9.584-9.5 23.75-9.5h93.334q14.166 0 23.75 9.617Q920 561.901 920 576.117q0 14.216-9.583 23.716-9.584 9.5-23.75 9.5h-93.334ZM479.883 296q-14.216 0-23.716-9.583-9.5-9.584-9.5-23.75v-93.334q0-14.166 9.617-23.75Q465.901 136 480.117 136q14.216 0 23.716 9.583 9.5 9.584 9.5 23.75v93.334q0 14.166-9.617 23.75Q494.099 296 479.883 296Zm0 720q-14.216 0-23.716-9.58-9.5-9.587-9.5-23.753v-93.334q0-14.166 9.617-23.75Q465.901 856 480.117 856q14.216 0 23.716 9.583 9.5 9.584 9.5 23.75v93.334q0 14.166-9.617 23.753-9.617 9.58-23.833 9.58ZM235.334 378 183 326.666q-10-9.666-9.586-23.735.414-14.069 9.517-24 9.931-9.931 24-9.931t23.735 10L282 331.334q9 10 9 23.333 0 13.333-9 23-9 9.666-22.833 9.5Q245.334 387 235.334 378Zm494 495L678 820.666q-9-10-9-23.749 0-13.75 9.333-22.917 9.334-10 22.834-9.833 13.499.166 23.499 9.833L777 825.334q10 9.666 9.586 23.735-.414 14.069-9.517 24-9.931 9.931-24 9.931t-23.735-10ZM678 378q-10-9.667-9.833-23.167.166-13.499 9.833-23.499L729.334 279q9.666-10 23.735-9.586 14.069.414 24 9.517 9.931 9.931 9.931 24t-10 23.735L724.666 378q-9.333 9-22.909 9-13.575 0-23.757-9ZM182.931 873.069q-9.931-9.931-9.931-24t10-23.735L235.334 774q9.866-9.667 23.433-9.667 13.566 0 22.972 9.667 10.261 9.667 10.094 23.167-.166 13.499-9.833 23.499L230.666 873q-9.666 10-23.735 9.586-14.069-.414-24-9.517ZM480 576Z' />
      </SvgIcon>
    )
  }
)

export const DarkModeIcon = ComponentRef<SVGSVGElement, Record<string, any>>(
  'DarkModeIcon',
  (props, ref) => {
    return () => (
      <SvgIcon
        forRef={ref}
        forProps={{ viewBox: '0 96 960 960', width: '48', height: '48', ...props }}>
        <path d='M480 936q-150 0-255-105T120 576q0-150 105-255t255-105q10 0 20.5.667 10.5.666 24.166 2-37.666 31-59.166 77.833T444 396q0 90 63 153t153 63q53 0 99.667-20.5 46.666-20.5 77.666-56.166 1.334 12.333 2 21.833.667 9.5.667 18.833 0 150-105 255T480 936Zm0-66.666q102 0 179.334-61.167t101.333-147.834q-23.333 9-49.111 13.667-25.778 4.666-51.556 4.666-117.459 0-200.063-82.603Q377.334 513.459 377.334 396q0-22.667 4.333-47.667t14.667-55q-91.334 28.666-150.501 107Q186.666 478.666 186.666 576q0 122 85.667 207.667T480 869.334Zm-6-288.001Z' />
      </SvgIcon>
    )
  }
)

export const ComponentIcon = ComponentRef<SVGSVGElement, Record<string, any>>(
  'ComponentIcon',
  (props, ref) => {
    return () => (
      <SvgIcon
        forRef={ref}
        forProps={{ viewBox: '0 0 1024 1024', width: '48', height: '48', ...props }}>
        <path
          d='M891.733333 249.6l-341.333333-196.266667c-23.466667-12.8-51.2-12.8-74.666667 0l-341.333333 196.266667c-23.466667 12.8-36.266667 38.4-36.266667 64v394.666667c0 25.6 14.933333 51.2 38.4 64l341.333334 196.266666c10.666667 6.4 23.466667 10.666667 38.4 10.666667 12.8 0 25.6-4.266667 36.266666-10.666667l341.333334-196.266666c23.466667-12.8 38.4-38.4 38.4-64V315.733333c-4.266667-27.733333-19.2-53.333333-40.533334-66.133333z m-384-140.8c0-2.133333 2.133333-2.133333 4.266667-2.133333s4.266667 0 4.266667 2.133333l311.466666 179.2-317.866666 166.4-313.6-166.4 311.466666-179.2zM164.266667 718.933333c-4.266667-2.133333-6.4-6.4-6.4-8.533333V341.333333l317.866666 168.533334 2.133334 390.4-313.6-181.333334z m695.466666 0l-315.733333 181.333334-2.133333-390.4L864 341.333333v369.066667c0 2.133333-2.133333 6.4-4.266667 8.533333z'
          p-id='1849'></path>
      </SvgIcon>
    )
  }
)

export const CloseIcon = ComponentRef<HTMLImageElement, ImgHTMLAttributes>(
  'CloseIcon',
  (props, ref, { slots }) => {
    return () => <ImageIcon forRef={ref} forProps={{ src: closeicon }} />
  }
)

export const PlusIcon = ComponentRef<HTMLImageElement, ImgHTMLAttributes>(
  'PlusIcon',
  (props, ref, { slots }) => {
    return () => <ImageIcon forRef={ref} forProps={{ src: plusicon }} />
  }
)

export const SubIcon = ComponentRef<HTMLImageElement, ImgHTMLAttributes>(
  'SubIcon',
  (props, ref, { slots }) => {
    return () => <ImageIcon forRef={ref} forProps={{ src: subicon }} />
  }
)

export const ControlsIcon = ComponentRef<SVGSVGElement, Record<string, any>>(
  'ControlsIcon',
  (props, ref, { slots }) => {
    return () => (
      <SvgIcon
        forRef={ref}
        forProps={{ viewBox: '0 0 1024 1024', width: '48', height: '48', ...props }}>
        <path
          d='M384 384C313.429333 384 256 326.570667 256 256s57.429333-128 128-128 128 57.429333 128 128-57.429333 128-128 128z m0-170.666667c-23.509333 0-42.666667 19.157333-42.666667 42.666667s19.157333 42.666667 42.666667 42.666667 42.666667-19.157333 42.666667-42.666667-19.157333-42.666667-42.666667-42.666667z'
          p-id='2006'></path>
        <path
          d='M298.666667 298.666667H170.666667a42.666667 42.666667 0 0 1 0-85.333334h128a42.666667 42.666667 0 0 1 0 85.333334zM853.333333 298.666667h-384a42.666667 42.666667 0 0 1 0-85.333334h384a42.666667 42.666667 0 1 1 0 85.333334zM384 896c-70.570667 0-128-57.429333-128-128s57.429333-128 128-128 128 57.429333 128 128-57.429333 128-128 128z m0-170.666667a42.709333 42.709333 0 0 0 0 85.333334 42.709333 42.709333 0 0 0 0-85.333334z'
          p-id='2007'></path>
        <path
          d='M298.666667 810.666667H170.666667a42.666667 42.666667 0 1 1 0-85.333334h128a42.666667 42.666667 0 1 1 0 85.333334zM853.333333 810.666667h-384a42.666667 42.666667 0 1 1 0-85.333334h384a42.666667 42.666667 0 1 1 0 85.333334zM640 640c-70.570667 0-128-57.429333-128-128s57.429333-128 128-128 128 57.429333 128 128-57.429333 128-128 128z m0-170.666667a42.709333 42.709333 0 0 0 0 85.333334 42.666667 42.666667 0 0 0 0-85.333334z'
          p-id='2008'></path>
        <path
          d='M554.666667 554.666667H170.666667a42.666667 42.666667 0 1 1 0-85.333334h384a42.666667 42.666667 0 1 1 0 85.333334zM853.333333 554.666667h-128a42.666667 42.666667 0 1 1 0-85.333334h128a42.666667 42.666667 0 1 1 0 85.333334z'
          p-id='2009'></path>
      </SvgIcon>
    )
  }
)

export const LayersIcon = ComponentRef<SVGSVGElement, Record<string, any>>(
  'LayersIcon',
  (props, ref, { slots }) => {
    return () => (
      <SvgIcon
        forRef={ref}
        forProps={{
          viewBox: '0 0 1024 1024',
          width: '48',
          height: '48',
          ...props
        }}>
        <path
          d='M976 672c-6.496 0-12.688 1.328-18.352 3.664v-0.016l-0.208 0.08-0.208 0.08L512 860.048 66.784 675.824a1.12 1.12 0 0 0-0.208-0.08l-0.208-0.08-0.016 0.016a48 48 0 0 0-36.704 88.656v0.016l463.584 191.824a1.12 1.12 0 0 0 0.208 0.08l0.208 0.08 0.016-0.016a47.6 47.6 0 0 0 36.688 0.016l0.016 0.016 0.208-0.08a1.12 1.12 0 0 1 0.208-0.08l463.568-191.824v-0.016A48 48 0 0 0 976 672zM29.664 348.336v0.016l463.584 191.824 0.208 0.08 0.208 0.08 0.016-0.016a47.456 47.456 0 0 0 36.672 0.016l0.016 0.016 0.208-0.08a1.12 1.12 0 0 1 0.208-0.08l463.568-191.824v-0.016A48.048 48.048 0 0 0 1024 304a48 48 0 0 0-29.648-44.336v-0.016L530.784 67.824a1.12 1.12 0 0 0-0.208-0.08l-0.208-0.08-0.016 0.016a47.744 47.744 0 0 0-36.688-0.016l-0.016-0.016-0.208 0.08a1.12 1.12 0 0 1-0.208 0.08L29.648 259.648v0.016A48 48 0 0 0 0 304a48 48 0 0 0 29.664 44.336zM976 464c-6.496 0-12.688 1.328-18.352 3.664v-0.016l-0.208 0.096a1.12 1.12 0 0 1-0.208 0.08L512 652.048 66.784 467.824a1.12 1.12 0 0 0-0.208-0.08l-0.208-0.096-0.016 0.016a48 48 0 0 0-36.704 88.672v0.016l463.584 191.824 0.208 0.08 0.208 0.08 0.016-0.016a47.6 47.6 0 0 0 36.688 0.016l0.016 0.016 0.208-0.08a1.12 1.12 0 0 1 0.208-0.08l463.568-191.824v-0.016A48 48 0 0 0 976 464z'
          p-id='1688'></path>
      </SvgIcon>
    )
  }
)

export const OptionsIcon = ComponentRef<SVGSVGElement, Record<string, any>>(
  'OptionsIcon',
  (props, ref, { slots }) => {
    return () => (
      <SvgIcon
        forRef={ref}
        forProps={{ viewBox: '0 0 1024 1024', width: '48', height: '48', ...props }}>
        <path
          d='M298.666667 605.013333V128a42.666667 42.666667 0 0 0-85.333334 0v477.013333a128 128 0 0 0 0 240.64V896a42.666667 42.666667 0 0 0 85.333334 0v-50.346667a128 128 0 0 0 0-240.64zM896 554.666667a128 128 0 0 0-85.333333-120.32V128a42.666667 42.666667 0 0 0-85.333334 0v306.346667a128 128 0 0 0 0 240.64V896a42.666667 42.666667 0 0 0 85.333334 0v-221.013333A128 128 0 0 0 896 554.666667zM640 213.333333a128 128 0 1 0-170.666667 120.32V896a42.666667 42.666667 0 0 0 85.333334 0V333.653333A128 128 0 0 0 640 213.333333z'
          p-id='7559'></path>
      </SvgIcon>
    )
  }
)

export const PropertiesIcon = ComponentRef<SVGSVGElement, Record<string, any>>(
  'PropertiesIcon',
  (props, ref, { slots }) => {
    return () => (
      <SvgIcon
        forRef={ref}
        forProps={{ viewBox: '0 0 1024 1024', width: '48', height: '48', ...props }}>
        <path
          d='M288 640C270.08 640 256 654.08 256 672 256 689.92 270.08 704 288 704 305.92 704 320 689.92 320 672 320 654.08 305.92 640 288 640zM288 256C270.08 256 256 270.08 256 288 256 305.92 270.08 320 288 320 305.92 320 320 305.92 320 288 320 270.08 305.92 256 288 256zM288 448C270.08 448 256 462.08 256 480 256 497.92 270.08 512 288 512 305.92 512 320 497.92 320 480 320 462.08 305.92 448 288 448zM768 64 192 64C121.6 64 64 121.6 64 192l0 576c0 70.4 57.6 128 128 128l576 0c70.4 0 128-57.6 128-128L896 192C896 121.6 838.4 64 768 64zM832 768c0 35.2-28.8 64-64 64L192 832c-35.2 0-64-28.8-64-64L128 192c0-35.2 28.8-64 64-64l576 0c35.2 0 64 28.8 64 64L832 768zM672 256l-256 0C398.08 256 384 270.08 384 288 384 305.92 398.08 320 416 320l256 0C689.92 320 704 305.92 704 288 704 270.08 689.92 256 672 256zM672 448l-256 0C398.08 448 384 462.08 384 480 384 497.92 398.08 512 416 512l256 0C689.92 512 704 497.92 704 480 704 462.08 689.92 448 672 448zM672 640l-256 0C398.08 640 384 654.08 384 672 384 689.92 398.08 704 416 704l256 0c17.92 0 32-14.08 32-32C704 654.08 689.92 640 672 640z'
          p-id='5198'></path>
      </SvgIcon>
    )
  }
)

export const HistoryIcon = ComponentRef<SVGSVGElement, Record<string, any>>(
  'HistoryIcon',
  (props, ref, { slots }) => {
    return () => (
      <SvgIcon
        forRef={ref}
        forProps={{ viewBox: '0 0 1024 1024', width: '48', height: '48', ...props }}>
        <path
          d='M512 109.714286c222.183619 0 402.285714 180.102095 402.285714 402.285714S734.183619 914.285714 512 914.285714l-2.901333-0.121904c-104.545524-8.313905-189.415619-43.52-253.074286-105.130667L256 889.904762h-73.142857V658.285714H390.095238v73.142857h-106.105905c52.857905 65.048381 128.560762 101.400381 229.449143 109.714286l7.631238-0.121905C698.660571 836.217905 841.142857 690.736762 841.142857 512c0-181.784381-147.358476-329.142857-329.142857-329.142857S182.857143 330.215619 182.857143 512h-73.142857c0-222.183619 180.102095-402.285714 402.285714-402.285714zM463.238095 292.571429h73.142857v182.857142h182.857143v73.142858H463.238095V292.571429z'
          p-id='6564'></path>
      </SvgIcon>
    )
  }
)

export const MoreIcon = ComponentRef<SVGSVGElement, Record<string, any>>(
  'MoreIcon',
  (props, ref, { slots }) => {
    return () => (
      <SvgIcon
        forRef={ref}
        forProps={{ viewBox: '0 0 1024 1024', width: '48', height: '48', ...props }}>
        <path
          d='M576 832a64 64 0 1 1-128 0 64 64 0 0 1 128 0zM512 256a64 64 0 1 0 0-128 64 64 0 0 0 0 128z m0 320a64 64 0 1 0 0-128 64 64 0 0 0 0 128z'
          fill='#000000'
          fill-opacity='.9'
          p-id='4594'></path>
      </SvgIcon>
    )
  }
)

export const SaveIcon = ComponentRef<SVGSVGElement, Record<string, any>>(
  'SaveIcon',
  (props, ref, { slots }) => {
    return () => (
      <SvgIcon
        forRef={ref}
        forProps={{ viewBox: '0 0 1024 1024', width: '48', height: '48', ...props }}>
        <path
          d='M906.666667 298.666667L725.333333 117.333333c-14.933333-14.933333-32-21.333333-53.333333-21.333333H170.666667C130.133333 96 96 130.133333 96 170.666667v682.666666c0 40.533333 34.133333 74.666667 74.666667 74.666667h682.666666c40.533333 0 74.666667-34.133333 74.666667-74.666667V349.866667c0-19.2-8.533333-38.4-21.333333-51.2zM652.8 864H371.2V648.533333h281.6v215.466667z m211.2-10.666667c0 6.4-4.266667 10.666667-10.666667 10.666667h-140.8V618.666667c0-17.066667-12.8-29.866667-29.866666-29.866667H341.333333c-17.066667 0-29.866667 12.8-29.866666 29.866667v245.333333H170.666667c-6.4 0-10.666667-4.266667-10.666667-10.666667V170.666667c0-6.4 4.266667-10.666667 10.666667-10.666667h140.8V320c0 17.066667 12.8 29.866667 29.866666 29.866667h277.333334c17.066667 0 29.866667-12.8 29.866666-29.866667s-12.8-29.866667-29.866666-29.866667H371.2V160h302.933333c2.133333 0 6.4 2.133333 8.533334 2.133333l179.2 179.2c2.133333 2.133333 2.133333 4.266667 2.133333 8.533334V853.333333z'
          p-id='4735'></path>
      </SvgIcon>
    )
  }
)
