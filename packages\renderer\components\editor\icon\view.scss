.scena-icon {
  position: relative;
  display: block;
  font-size: 0;
  padding: 3px;
  box-sizing: border-box;
  cursor: pointer;
  border: 1px solid transparent;
  margin-bottom: 5px;
  border-radius: 3px;
  transition: all ease 0.2s;
}

.scena-icon.scena-selected>svg path,
.scena-icon.scena-selected>svg ellipse,
.scena-sub-icon.scena-selected path,
.scena-sub-icon.scena-selected ellipse {
  fill: var(--scena-editor-icon-selected);
  stroke: var(--scena-editor-icon-selected);
}

.scena-icon .scena-extends-icon {
  position: absolute;
  right: 2px;
  bottom: 2px;
  border-bottom: 5px solid #eee;
  border-right: 0;
  border-left: 5px solid transparent;
}

.scena-extends-container {
  position: absolute;
  left: 110%;
  top: -30px;
  background: var(--scena-editor-color-back2);
  /* width: 200px;
  height: 200px; */
  border-radius: 5px;
  z-index: 1;
  transform: translate(10px) translateZ(2px);
  box-shadow: 1px 1px 2px var(--scena-editor-color-back1);
  display: none;
}

.scena-sub-icon {
  white-space: nowrap;
  padding: 0px 7px;
  margin: 7px 10px;
}

.scena-sub-icon .scena-icon {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  width: 25px;
  height: 25px;
  margin: 0;
}

.scena-sub-icon-label {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  color: white;
  font-size: 14px;
  font-weight: bold;
  padding: 0;
  margin: 0;
  margin-left: 5px;
}

.scena-keyboard {
  background: #fff;
  border-radius: 3px;
  width: 90%;
  height: 15px;
  margin: 3px 0px 7px;
  text-align: center;
  box-sizing: border-box;
  padding: 2px;
}

.scena-key {
  display: inline-block;
  width: 2px;
  height: 2px;
  background: var(--scena-editor-color-back2);
  margin: 1px;
}
.scena-space {
  display: inline-block;
  width: 12px;
  height: 2px;
  background: var(--scena-editor-color-back2);
  margin: 1px;
  border-radius: 1px;
}
