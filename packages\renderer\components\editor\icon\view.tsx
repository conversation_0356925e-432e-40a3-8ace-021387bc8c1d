import { defineComponent, ExtractPropTypes, onMounted, ref, watch } from 'vue'
import { buildProps, definePropType } from '@wuk/wui/dist/utils'
import type { Icon } from './base'
import { prefix } from '../utils'

import './view.scss'

export const iconProps = buildProps({
  icon: {
    type: definePropType<Icon>(Object)
  },
  selected: {
    type: Boolean,
    default: false
  },
  onSelect: {
    type: definePropType<(id: string) => any>(Function)
  }
})
export type IconProps = ExtractPropTypes<typeof iconProps>

export const IconView = defineComponent({
  name: 'IconView',
  props: iconProps,
  setup(props, { expose }) {
    const containerRef = ref<HTMLDivElement>()

    const focusSub = () => {
      const container = containerRef.value
      if (!container) {
        return
      }

      if (container.style.display === 'block') {
        container.style.display = 'none'
      } else {
        container.style.display = 'block'
      }
    }
    const blur = () => {
      const container = containerRef.value
      if (!container) {
        return
      }

      container.style.display = 'none'
    }

    const onClick = () => {
      if (!props.icon) return

      if (props.selected) {
        focusSub()
      }

      props.onSelect?.(props.icon.id)
    }

    const onSubClick = (e: any) => {
      e.stopPropagation()
    }

    const renderSubContainer = () => {
      const subIcons = props.icon?.renderSubIcons()

      if (!subIcons?.length) {
        return []
      }

      return [
        <div key={'extends-icon'} class={prefix('extends-icon')}></div>,
        props.selected && (
          <div
            key={'extends-container'}
            class={prefix('extends-container')}
            ref={containerRef}
            onClick={onSubClick}>
            {subIcons}
          </div>
        )
      ]
    }

    const renderIcon = () => props.icon?.renderIcon() || <></>

    onMounted(() => {
      props.icon?.on('on-icon-click', () => {
        onClick()
      })
      props.icon?.on('on-icon-blur', () => {
        blur()
      })
      // icon?.on('on-icon-update', () => {
      //   updateRef.value = updateRef.value + 1
      // })
      props.icon?.init()
    })

    return () => (
      <div class={prefix('icon', props.selected ? 'selected' : '')} onClick={onClick}>
        {renderIcon()}
        {renderSubContainer()}
      </div>
    )
  }
})
