.scena-editor {
  // position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  font-family: sans-serif;
  transform-style: preserve-3d;
  background-color: var(--scena-editor-color-canvas);

  --scena-editor-color-background-tool: #2a2a2a;
  --scena-editor-color-background-bold: #1a1a1a;
  --scena-editor-color-divider: #444;
  --scena-editor-color-text: #fff;
  --scena-editor-color-text-unlit: #555;
  --scena-editor-color-text-hover: #fff;
  --scena-editor-color-text-selected: #fff;
  --scena-editor-color-canvas: #1a1a1a;
  --scena-editor-color-guides: #333;
  --scena-editor-color-icon: #fff;

  --scena-editor-thumb-color: #555;

  --scena-editor-color-folder-selected: #55bbff;
  --scena-editor-color-folder-fold: #fff;
  --scena-editor-color-folder-selected-text: #fff;

  --scena-editor-color-main: #4af;
  --scena-editor-color-selected: #5bf;
  --scena-editor-color-selected2: #55bbffaa;
  --scena-editor-color-selected3: #55bbff55;

  --scena-editor-color-back1: #1a1a1a;
  --scena-editor-color-back2: #2a2a2a;
  --scena-editor-color-back3: #333;
  --scena-editor-color-back5: #555;
  --scena-editor-color-back6: #666;
  --scena-editor-color-fill2: #eee;

  --scena-editor-color-viewport: #fff;
  --scena-editor-icon-selected: #fff;

  --scena-editor-size-top: 38px;
  --scena-editor-size-guides: 30px;
  --scena-editor-size-menu: 45px;
  --scena-editor-size-tabs: 40px;
  --scena-editor-size-tab: 200px;
}

.scena-light-mode {
  --scena-editor-color-background-tool: #fff;
  --scena-editor-color-background-bold: #ddd;
  --scena-editor-color-divider: #dcdfe6;
  --scena-editor-color-text: #555;
  --scena-editor-color-text-unlit: #aaa;
  --scena-editor-color-text-hover: #333;
  --scena-editor-color-text-selected: #333;
  --scena-editor-color-canvas: #f7f7f7;
  --scena-editor-color-guides: #eee;
  --scena-editor-color-icon: #555;

  --scena-editor-thumb-color: #aaa;

  --scena-editor-color-folder-selected: #44aaff33;
  --scena-editor-color-folder-selected-text: #333;
  --scena-editor-color-folder-fold: #aaa;

  --scena-editor-color-back1: #fff;
  --scena-editor-color-back2: #eee;
  --scena-editor-color-back3: #bbb;
  --scena-editor-color-back5: #999;
  --scena-editor-color-back6: #888;

  --scena-editor-color-fill2: #222;
}

.scena-editor-top {
  height: var(--scena-editor-size-top);
  width: 100%;
  background-color: var(--scena-editor-color-back2);
  border-bottom: 1px solid var(--scena-editor-color-divider);
}

.scena-editor-area {
  width: 100%;
  height: 100%;
  display: flex;
}

.scena-editor-area-left {
  width: var(--scena-editor-size-menu);
  height: 100%;
  overflow: hidden;
}

.scena-editor-area-center {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  border-left: 1px solid var(--scena-editor-color-divider);
  border-right: 1px solid var(--scena-editor-color-divider);
}

.scena-editor-area-right {
  width: var(--scena-editor-size-menu);
  height: 100%;
}

.scena-hide-guides {
  --scena-editor-size-guides: 0px;
}

[class*="scena-"] {
  font-family: "Open Sans", sans-serif;
}

.scena-svg-icon {
  fill: var(--scena-editor-color-text);
  stroke: var(--scena-editor-color-text);
}

.scena-center {
  position: relative;
}

.scena-canvas {
  position: relative;
  width: 100%;
  height: 100%;
}

.moveable {
  pointer-events: all;
}

.moveable-node {
  pointer-events: none;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.scena-panel {
  position: relative;
  top: 0px;
  left: 0px;
  width: 100%;
  height: 100%;
  pointer-events: none;

  .scena-properties-panel {
    padding: 8px 16px;
    height: 100%;
    z-index: 1;
    position: absolute;
    margin-top: 16px;
    right: 0;
    top: 0;
  }
}

.scena-panel-toolbar {
  //padding: var(--scena-editor-size-guides) 0 0 var(--scena-editor-size-guides);
  pointer-events: all;
  padding: 10px;
  background-color: #fff;
  border-right: 1px solid var(--scena-editor-color-divider);
}

.scena-panel-left,
.scena-panel-right {
  position: relative;
  background: #fff
  //min-width: 180px;
}

.scena-panel-left {
  display: flex;
  //padding: var(--scena-editor-size-guides) 0 0 var(--scena-editor-size-guides);
  //左侧加阴影
  box-shadow: inset 0 0 10px 0 rgba(0, 0, 0, 0.1);

}

// .scena-panel-right {
//   padding: var(--scena-editor-size-guides) 0;
// }

.scena-panel-center {
  position: relative;
  pointer-events: none;
}
.scena-resize-handle:after {
  background: transparent !important;
}
.scena-resize-handle {
  position: relative;
  z-index: 2;
  transform: translateZ(1px);
  pointer-events: all;
}

.scena-resize-handle:before,
.scena-resize-handle:after {
  position: absolute;
  content: "";
  width: 10px;
  height: 100%;
  top: 0;
  left: 50%;
  transform: translate(-50%);
  z-index: 1;
}

.scena-resize-handle:after {
  width: 1px;
  background: var(--scena-editor-color-background-tool);
}

.scena-viewer {
  position: absolute !important;
  left: var(--scena-editor-size-guides);
  top: var(--scena-editor-size-guides);
  width: calc(100% - var(--scena-editor-size-guides));
  height: calc(100% - var(--scena-editor-size-guides));
  background: var(--scena-editor-color-canvas);
  overflow: hidden;
}

.scena-viewer .infinite-viewer-scroll-thumb {
  background: var(--scena-editor-thumb-color);
}

// .scena-editor .scena-selecto {
//   border: 1px solid var(--scena-editor-color-back6);
//   background-color: rgba(255, 0, 0, 0);
// }

.scena-viewport-container {
  position: relative;
}

.scena-viewport {
  position: relative;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
 // background: var(--scena-editor-color-viewport);
}

.scena-viewport:before {
  content: "";
  position: absolute;
  border: 1px solid var(--scena-editor-color-divider);
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  pointer-events: none;
}

.scena-viewer-move {
  cursor: grab;
}

.scena-viewer-move .scena-viewport {
  pointer-events: none;
}

.scena-guides {
  position: absolute !important;
  top: 0;
  left: 0;
  transform: translateZ(1px);
  z-index: 2;
  pointer-events: all;
}

.scena-guides.scena-horizontal {
  left: var(--scena-editor-size-guides);
  width: calc(100% - var(--scena-editor-size-guides));
  height: var(--scena-editor-size-guides) !important;
}

.scena-guides.scena-vertical {
  top: var(--scena-editor-size-guides);
  left: 0;
  height: calc(100% - var(--scena-editor-size-guides));
  width: 30px !important;
}

.scena-reset {
  position: absolute !important;
  background: var(--scena-editor-color-guides);
  width: var(--scena-editor-size-guides);
  height: var(--scena-editor-size-guides);
  z-index: 1;
  border-right: 1px solid var(--scena-editor-color-divider);
  border-bottom: 1px solid var(--scena-editor-color-divider);
  box-sizing: border-box;
  cursor: pointer;
  left: 0;
  top: 0;
}

.scena-overlay {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 100;
  transform-style: preserve-3d;
  transform: translateZ(15px);
  background: rgba(0, 0, 0, 0.2);
}

.moveable-dimension {
  position: absolute;
  background: var(--scena-editor-color-main);
  border-radius: 2px;
  padding: 1px 3px;
  color: white;
  font-size: 13px;
  white-space: nowrap;
  font-weight: bold;
  will-change: transform;
  transform: translate(-50%) translateZ(0px);
}
