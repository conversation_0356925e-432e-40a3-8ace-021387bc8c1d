import { defineComponent, ExtractPropTypes, onUnmounted } from 'vue'
import { DndProvider } from 'vue3-dnd'
import { HTML5Backend } from 'react-dnd-html5-backend'
import { buildProps } from '@wuk/wui/dist/utils'
import { Container } from './container'

export * from './states'

export const editorProps = buildProps({
  debug: {
    type: Boolean,
    default: false
  }
})
export type EditorProps = ExtractPropTypes<typeof editorProps>

export const EditorView = defineComponent({
  name: 'EditorView',
  props: editorProps,
  setup(props, { expose }) {
    return () => (
      <DndProvider backend={HTML5Backend}>
        <Container />
      </DndProvider>
    )
  }
})
