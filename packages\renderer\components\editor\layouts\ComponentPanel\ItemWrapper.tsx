import { defineComponent } from 'vue'
import { prefix } from '../../utils'

export default defineComponent({
  name: 'ItemWrapper',
  props: {
    index: {
      type: Number,
      required: true
    },
    onRightClick: {
      type: Function,
      required: true
    },
    isActive: {
      type: Boolean,
      default: false
    }
  },
  setup(props, { slots }) {
    const handleContextMenu = (e: MouseEvent) => {
      e.preventDefault() // 阻止默认的浏览器右键菜单
      props.onRightClick(e, props.index)
    }
    return () => (
      <div
        class={[prefix('component-item'), props.isActive && prefix('component-item-active')]}
        onContextmenu={handleContextMenu}>
        {slots.default?.()}
      </div>
    )
  }
})
