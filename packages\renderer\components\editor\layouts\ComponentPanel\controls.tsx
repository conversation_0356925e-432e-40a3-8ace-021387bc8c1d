import { BasePanel, type BasePanelDefaultSlot } from '@/renderer/components/BasePanel'
import { defineComponent } from 'vue'
import { components, TargetBox } from '../../controls'
import ItemWrapper from './ItemWrapper'
import { prefix } from '../../utils'

export default defineComponent({
  name: 'Controls',
  setup() {
    const list = Object.values(components)
    return () => (
      <BasePanel title='Controls' isSearch>
        {{
          default: ({ searchKey }: BasePanelDefaultSlot) => {
            return (
              <>
                {list.map((item, index) => {
                  if (
                    searchKey &&
                    !item.displayName.toLowerCase().includes(searchKey.toLowerCase())
                  )
                    return null
                  return (
                    <ItemWrapper
                      index={index}
                      onRightClick={() => {
                        console.log(index)
                      }}>
                      <TargetBox item={item}></TargetBox>
                    </ItemWrapper>
                  )
                })}
              </>
            )
          }
        }}
      </BasePanel>
    )
  }
})
