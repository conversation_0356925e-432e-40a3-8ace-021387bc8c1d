import { BasePanel, type BasePanelDefaultSlot } from '@/renderer/components/BasePanel'
import { useBizEngine, useRightMenu } from '@/renderer/hooks'
import { useStoreState } from '@/renderer/store'
import { DisplayItem } from '@wuk/cfg'
import { WuiButton, WuiDialog, WuiInput, WuiMessageBox } from '@wuk/wui'
import { defineComponent, onMounted, ref, watch } from 'vue'
import { Editor } from '../../states'
import { $currentScena, $fileIndex } from '../../stores'
import { prefix } from '../../utils'
import ItemWrapper from './ItemWrapper'
import './index.scss'
export default defineComponent({
  name: 'Displays',
  setup() {
    const bizEngine = useBizEngine()
    const editor = Editor.impl
    const [fileIndex, setFileIndex] = useStoreState($fileIndex)
    const index = ref(0)
    const showDialog = ref(false)
    const fileList = ref<DisplayItem[]>([])
    const action = ref('add')
    const fileName = ref('')
    const currentFileName = ref('')
    const [currentScena, setCurrentScena] = useStoreState($currentScena)
    onMounted(() => {
      handleLoadFile()
    })

    const validateInput = (value: string) => {
      return value.replace(/[\u4e00-\u9fa5\s]/g, '')
    }

    // 监听 fileName 的变化
    watch(fileName, newValue => {
      if (newValue) {
        fileName.value = validateInput(newValue)
      }
    })

    // 右键点击菜单
    const menuPtr = useRightMenu(
      [
        { key: 'insertBeforeKey', label: 'insertBefore' },
        { key: 'insertAfterKey', label: 'insertAfter' },
        { key: 'modifyKey', label: 'modify' },
        { key: 'deleteKey', label: 'delete' }
      ],
      async (key, ...args) => {
        switch (key) {
          case 'insertBeforeKey':
            action.value = 'add'
            index.value = args[0]
            showDialog.value = true
            break
          case 'insertAfterKey':
            console.log('insertAfterKey', args[0])
            action.value = 'add'
            index.value = args[0] + 1
            showDialog.value = true
            break
          case 'deleteKey':
            handleDeleteFile(args[0])
            break
          case 'modifyKey':
            action.value = 'modify'
            index.value = args[0]
            fileName.value = args[1]
            showDialog.value = true
            break

          default:
            break
        }
      }
    )

    const handleLoadFile = () => {
      bizEngine.value?.readDisplayDefinedOptions().then(res => {
        fileList.value = res?.files || []
        console.log('fileList', fileList.value)
      })
    }
    const handleDeleteFile = (index: number) => {
      WuiMessageBox.confirm('Are you sure you want to delete this file?').then(res => {
        bizEngine.value?.removeDisplayFile(index).then(res => {
          handleLoadFile()
        })
      })
    }
    const handleModifyFile = () => {
      console.log('index---------', index.value)
      bizEngine.value
        ?.modifyDisplayFile(index.value, {
          name: fileName.value,
          file: fileName.value,
          description: ''
        })
        .then(res => {
          handleLoadFile()
          showDialog.value = false
          fileName.value = ''
        })
    }
    const handleCreateFile = () => {
      bizEngine.value
        ?.addDisplayFile(
          { name: fileName.value, file: fileName.value, description: '' },
          index.value
        )
        .then(res => {
          showDialog.value = false
          fileName.value = ''
          handleLoadFile()
        })
    }
    return () => (
      <>
        <BasePanel
          title='Displays'
          isSearch
          onCreate={() => {
            console.log('create')
            action.value = 'add'
            showDialog.value = true
          }}>
          {{
            default: ({ searchKey }: BasePanelDefaultSlot) => {
              return (
                <>
                  {fileList.value.map((item, index) => {
                    if (searchKey && !item.name.includes(searchKey)) return null
                    return (
                      <ItemWrapper
                        index={index}
                        key={item.name}
                        isActive={currentFileName.value === item.name}
                        onRightClick={(e: MouseEvent, index: number) => {
                          menuPtr.show(e.clientX, e.clientY, index, item.name)
                        }}>
                        <div
                          class={prefix('display-item-name')}
                          onClick={async () => {
                            try {
                              const res = await bizEngine.value?.loadDisplay(index)
                              if (!res) {
                                return
                              }
                              console.log('res---------', res)
                              setFileIndex(index)
                              editor.loadDisplay(index, item.name, item.file, res)
                              currentFileName.value = item.name
                            } catch (error) {
                              // setCurrentDisplay(null)
                              console.error('error', error)
                            }
                          }}>
                          {item.name}
                        </div>
                      </ItemWrapper>
                    )
                  })}
                </>
              )
            }
          }}
        </BasePanel>
        <WuiDialog v-model={showDialog.value} title='Create Display' appendToBody width='500px'>
          <div class={prefix('create-display-form')}>
            <span class={prefix('create-display-form-label')}>Display Name</span>
            <WuiInput
              v-model={fileName.value}
              type='text'
              class={prefix('create-display-form-input')}
            />
          </div>
          <div class={prefix('create-display-form-actions')}>
            <WuiButton
              class={prefix('create-display-form-button')}
              type='primary'
              onClick={() => {
                if (action.value === 'add') {
                  handleCreateFile()
                } else if (action.value === 'modify') {
                  handleModifyFile()
                }
              }}>
              Confirm
            </WuiButton>
          </div>
        </WuiDialog>
      </>
    )
  }
})
