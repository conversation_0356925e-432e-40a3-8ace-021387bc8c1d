
$prefix: 'scena';
.#{$prefix}-component-panel {
  display: flex;
  height: 100%;
  flex-direction: column;
  box-sizing: border-box;
  width: 100%;
}

.#{$prefix}-component-item {
  padding: 4px 16px;
  display: flex;
  align-items: center;
  pointer-events: all;
  border-bottom: 1px solid #f5f5f5;
  //鼠标移上去加阴影浮动
  &:hover {
      background-color: #f5f5f5;
    }
  &-actions {
    margin-left: auto;
    position: relative;
  }

  .#{$prefix}-more-icon {
    cursor: pointer;
    opacity: 0.6;

    &:hover {
      opacity: 1;
    }
  }

  .#{$prefix}-dropdown-menu {
    position: absolute;
    left: 100%;
    top: -10px;
    margin-left: 8px;
    min-width: 120px;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    padding: 4px 0;
    z-index: 1000;

    &-arrow {
      position: absolute;
      left: -4px;
      top: 8px;
      width: 8px;
      height: 8px;
      background: #fff;
      transform: rotate(45deg);
    }
    .#{$prefix}-dropdown-menu-item {
      position: relative;
      z-index: 1;
      padding: 8px 12px;
      cursor: pointer;
      font-size: 12px;

      &:hover {
        background: #f5f5f5;
      }
    }
    // 下拉菜单项样式
    >div {
      padding: 8px 12px;
      cursor: pointer;

      &:hover {
        background: #f5f5f5;
      }
    }
}
}

.#{$prefix}-component-item-active {
  background: #f5f5f5;
}

.#{$prefix}-browser-item {
  background: #fff;
  border-radius: 4px;
  padding: 8px;

  &:last-child {
    margin-bottom: 0;
  }
}

.#{$prefix}-component-panel-container {
  position: relative;
}

.#{$prefix}-create-display-form {
  display: flex;
  flex-direction: row;
  gap: 8px;
  padding: 24px 16px 16px 16px;
  &-label {
    display: flex;
    align-items: center;
    font-size: 14px;
    font-weight: 600;
    width: 100px;
  }
  &-input {
    width: 100%;
    height: 32px;
    border-radius: 4px;
    border: 1px solid #f5f5f5;
  }
  &-actions {
    display: flex;
    justify-content: center;
    gap: 16px;
    margin-bottom: 16px;
    margin-top: 16px;
  }
}

.#{$prefix}-display-item-name {
  width: 100%;
  font-size: 14px;
  font-weight: 600;
  color: #333;
  padding: 4px;
}
