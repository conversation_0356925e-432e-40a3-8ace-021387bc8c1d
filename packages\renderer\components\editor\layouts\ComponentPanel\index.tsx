import { defineComponent } from 'vue'
import { prefix } from '../../utils'
import Controls from './controls'
import Displays from './displays'
import './index.scss'
import Layers from './layers'

export const ComponentPanel = defineComponent({
  name: 'ComponentPanel',
  setup(props, { slots }) {
    return () => (
      <div class={prefix('component-panel')}>
        <div class={prefix('component-panel-container')}>
          <Displays />
          <Controls />
          <Layers />
        </div>
      </div>
    )
  }
})
