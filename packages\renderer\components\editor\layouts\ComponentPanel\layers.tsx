import {
  BasePanel,
  type BasePanelDefaultSlot,
  type BasePanelExpose
} from '@/renderer/components/BasePanel'
import { useStoreStateValue } from '@/renderer/store'
import { WuiTree } from '@wuk/wui'
import { computed, defineComponent, onMounted, ref, watch } from 'vue'
import { $currentScena, $editor, $layers, $selectedLayers } from '../../stores'
import type Node from '@wuk/wui/dist/types/components/tree/src/model/node'
import type { DragEvents } from '@wuk/wui/dist/types/components/tree/src/model/useDragNode'
import type {
  AllowDropType,
  NodeDropType,
  TreeNodeData
} from '@wuk/wui/dist/types/components/tree/src/tree.type'

export default defineComponent({
  name: 'Layers',
  setup() {
    const panelRef = ref<InstanceType<typeof BasePanel> & BasePanelExpose>()
    const layers = useStoreStateValue($layers)
    const currentScena = useStoreStateValue($currentScena)
    const editorRef = useStoreStateValue($editor)
    const selectedLayers = useStoreStateValue($selectedLayers)

    const selectedKey = computed(() => {
      const [layer] = selectedLayers.value || []
      return layer?.id
    })

    const data = computed(() => {
      if (!currentScena.value) {
        return []
      }
      const label = currentScena.value.name
      const children = layers.value.map((layer, index) => {
        return { label: layer.title, index, layer, id: layer.id }
      })

      return [{ label, children }]
    })

    const allowDrop = (draggingNode: Node, dropNode: Node, type: AllowDropType) => {
      const data = dropNode.data
      const result =
        data.layer &&
        ((data.layer?.type === 'layer' && type !== 'inner') || data.layer?.type === 'group')
      return result
    }

    const allowDrag = (draggingNode: Node) => {
      return !!draggingNode.data?.layer
    }

    watch(
      () => currentScena.value?.file,
      (newFile, oldFile) => {
        if (newFile === oldFile) return
        panelRef.value?.handleSearch()
      }
    )

    const handleDrop = (
      draggingNode: Node,
      dropNode: Node,
      dropType: NodeDropType,
      ev: DragEvents
    ) => {
      const fromIdx = draggingNode.data.index
      const layer = draggingNode.data.layer
      let toIdx = dropNode.data.index
      if (fromIdx === undefined || toIdx === undefined) {
        return
      }
      switch (dropType) {
        case 'before':
          --toIdx
          break
        case 'after':
          ++toIdx
          break
      }
      if (toIdx > fromIdx) {
        --toIdx
      }
      if (fromIdx === toIdx || toIdx < 0) {
        return
      }

      layers.value?.splice(fromIdx, 1)
      layers.value?.splice(toIdx, 0, layer)

      editorRef.value?.setLayers([...layers.value])
    }

    const handleNodeClick = (data: TreeNodeData) => {
      const layer = data.layer
      layer && editorRef.value?.setSelectedLayers([layer], true)
    }

    return () => (
      <BasePanel ref={panelRef} title='Layers' isSearch>
        {{
          default: ({ searchKey }: BasePanelDefaultSlot) => {
            const filterData = data.value.map(firstData => ({
              ...firstData,
              children: firstData.children.filter(item => item.label.includes(searchKey))
            }))
            return (
              <>
                <div style={{ paddingBottom: '30px' }}>
                  <WuiTree
                    data={filterData}
                    nodeKey='id'
                    currentNodeKey={selectedKey.value}
                    allowDrop={allowDrop}
                    allowDrag={allowDrag}
                    onNode-drop={handleDrop}
                    onNode-click={handleNodeClick}
                    default-expand-all
                    draggable
                  />
                </div>
              </>
            )
          }
        }}
      </BasePanel>
    )
  }
})
