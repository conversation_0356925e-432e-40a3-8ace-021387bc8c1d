$prefix: 'scena';
.#{$prefix}-left {
  width: var(--scena-editor-size-menu);
  height: 100%;
  background: var(--scena-editor-color-back2);
  box-sizing: border-box;
  padding: 15px 7px 0px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  display: flex;
  justify-content: space-between;
  flex-direction: column;
}
.#{$prefix}-left-item-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: start;
  gap: 16px;

  .#{$prefix}-expand-icon {
    margin-bottom: 10px;
  }
}

.#{$prefix}-left svg,
.#{$prefix}-left .#{$prefix}-i {
  pointer-events: none;
  fill: var(--scena-editor-color-text);
  stroke: var(--scena-editor-color-text);
}
.#{$prefix}-left-item {
  padding: 4px;
  border-radius: 4px;
  border: 1px solid transparent;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}
.#{$prefix}-left-item-active {
  background: var(--scena-editor-color-main);
  border-color: var(--scena-editor-color-back1);
  border-radius: 4px;
  svg {
    fill: #fff;
    stroke: #fff;
  }
}

.#{$prefix}-left .#{$prefix}-selected {
  background: var(--scena-editor-color-main);
  border-color: var(--scena-editor-color-back1);
}
