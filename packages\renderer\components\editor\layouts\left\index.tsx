import { buildProps, definePropType } from '@wuk/wui/dist/utils'
import { defineComponent, ExtractPropTypes } from 'vue'
import { ComponentIcon, ControlsIcon, LayersIcon } from '../../icon/icons'
import { Editor } from '../../states'
import { prefix } from '../../utils'
import './index.scss'

export const leftProps = buildProps({
  selected: {
    type: String,
    default: ''
  },
  onSelect: {
    type: definePropType<(id: string) => any>(Function)
  }
})
export type LeftProps = ExtractPropTypes<typeof leftProps>

export const LeftView = defineComponent({
  name: 'LeftView',
  props: leftProps,
  setup(props, { expose }) {
    const menu = Editor.impl.menu

    const handleSelect = (id: string) => {
      props.onSelect?.(id)
    }

    return () => (
      <div class={prefix('left')}>
        <div class={prefix('left-item-group')}>
          <wui-tooltip content='Displays' placement='right'>
            <div class={`${prefix('left-item')} `}>
              <ComponentIcon style={{ width: '1.2em', height: '1.2em' }} />
            </div>
          </wui-tooltip>
          <wui-tooltip content='Controls' placement='right'>
            <div class={`${prefix('left-item')}`}>
              <ControlsIcon style={{ width: '1.2em', height: '1.2em' }} />
            </div>
          </wui-tooltip>
          <wui-tooltip content='Layers' placement='right'>
            <div class={`${prefix('left-item')}`}>
              <LayersIcon style={{ width: '1.2em', height: '1.2em' }} />
            </div>
          </wui-tooltip>
        </div>
      </div>
    )
  }
})
