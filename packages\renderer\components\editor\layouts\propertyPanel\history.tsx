import { defineComponent, onMounted, ref } from 'vue'
import { Editor } from '../../states'
import { HistoryAction } from '../../states/historys'
import { prefix } from '../../utils'
import './index.scss'

export const History = defineComponent({
  name: 'History',
  setup() {
    const editor = Editor.impl
    const historyList = ref<HistoryAction[]>(editor.historys.undoStack)
    onMounted(() => {
      historyList.value = editor.historys.undoStack
      editor.events.on('on-history-update', params => {
        if (params?.history) {
          historyList.value = [...params.history]
        }
      })
      editor.events.on('on-editor-loaded', () => {
        historyList.value = []
      })
    })
    return () => (
      <div class={prefix('history-container')}>
        {historyList.value.length ? (
          historyList.value.map((item, index) => (
            <div key={index} class={prefix('history-item')}>
              {item.type}
            </div>
          ))
        ) : (
          <div class={prefix('history-no-data')}>No Data</div>
        )}
      </div>
    )
  }
})
