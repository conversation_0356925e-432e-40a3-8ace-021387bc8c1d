$prefix: 'scena';
.#{$prefix}-property-panel {
  display: flex;
  height: 100%;
  flex-direction: column;
  padding: 8px 0;
  box-sizing: border-box;
  resize: horizontal;
  overflow: hidden;
  min-width: 300px;
//  max-width: 800px;
}

.#{$prefix}-panel-container {
  // display: flex;
  // flex-direction: column;
  // align-items: end;
  // margin-left: 10px;
}
.#{$prefix}-history-item {
  padding: 4px 8px;
  font-size: 14px;
  cursor: pointer;
  &:hover {
    background-color: #f5f5f5;
  }
}
.#{$prefix}-history-no-data {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 12px;
}

.#{$prefix}-property-container {
  padding: 8px;
  display: flex;
  flex-direction: column;
  padding-bottom: 100px;
  width: 100%;
  height: 60vh;
  overflow: auto;
  box-sizing: border-box;

  .#{$prefix}-property-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 6px 24px;  // 垂直间距16px，水平间距24px
    width: 100%;
  }

  .#{$prefix}-property-item {
    display: flex;
    align-items: flex-start;  // 改为顶部对齐
    gap: 8px;
    min-height: 32px;
    box-sizing: border-box;

    &--half {
      width: calc(50% - 12px);  // 考虑到水平gap是24px，所以每个item减去12px
      min-width: 200px;  // 添加最小宽度
    }

    &-childs {
      flex-wrap: wrap;
    }

    &--full {
      width: 100%;
      display: block;
    }

    .wui-form-item {
      width: 100%;
      margin-bottom: 0;
      display: flex;
      align-items: flex-start;  // 改为顶部对齐
      min-height: 32px;
    }

    .wui-form-item__label {
      width: 90px;  // 增加标签宽度
      flex-shrink: 0;
      margin-right: 8px;
      text-align: right;
      line-height: 32px;  // 添加行高
      white-space: nowrap;  // 防止标签换行
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .wui-form-item__content {
      flex: 1;
      min-width: 0;
      margin-left: 0;
    }

    .#{$prefix}-property-item-label {
      font-size: 12px;
      color: #333;
      text-align: right;
      flex-shrink: 0;
    }

    .#{$prefix}-property-item-value {
      font-size: 14px;
      flex: 1;
      min-width: 0;

      .wui-input {
        height: 32px;
        width: 100%;
      }

      .wui-select {
        width: 100%;
      }
    }
  }

}

.#{$prefix}-history-container {
  padding: 8px;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.#{$prefix}-options-container {
  padding: 8px;
  display: flex;
  flex-direction: column;
  .#{$prefix}-options-item {
    width: 100%;
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 5px;

    .#{$prefix}-options-item-label {
      font-size: 12px;
      width: 110px;
      color: #333;
      text-align: right;
    }
    .wui-select {
      width: 120px;
    }
  }
}

