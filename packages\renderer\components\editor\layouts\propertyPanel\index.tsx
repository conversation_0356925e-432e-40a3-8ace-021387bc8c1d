import { BasePanel } from '@/renderer/components/BasePanel'
import { useStoreStateValue } from '@/renderer/store'
import { defineComponent, ref } from 'vue'
import { Editor } from '../../states'
import { $currentScena, $selectedLayers } from '../../stores'
import { prefix } from '../../utils'
import AlignTab from '../tabs/aligntab'
import { Options } from './options'
import { Property } from './property'

export const PropertyPanel = defineComponent({
  name: 'PropertyPanel',
  props: {},
  setup(props) {
    const selectedLayers = useStoreStateValue($selectedLayers)

    return () => (
      <div class={prefix('property-panel')}>
        <div class={prefix('panel-container')}>
          <BasePanel title='Options' height='12vh'>
            <Options />
          </BasePanel>
          {((selectedLayers.value?.length || 0) > 1 && (
            <BasePanel title='Align' height='6vh'>
              <AlignTab />
            </BasePanel>
          )) || <></>}
          {(selectedLayers.value?.length && (
            <BasePanel title='Properties' height='55vh'>
              <Property />
            </BasePanel>
          )) || <></>}

          {/* <BasePanel title='History'>
            <History />
          </BasePanel> */}
        </div>
      </div>
    )
  }
})
