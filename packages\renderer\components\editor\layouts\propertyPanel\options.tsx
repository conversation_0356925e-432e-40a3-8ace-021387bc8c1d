import { ColorPicker } from '@/renderer/components/ColorPicker'
import { useBizMain } from '@/renderer/hooks'
import { useStoreStateValue } from '@/renderer/store'
import { WuiOption, WuiSelect } from '@wuk/wui'
import { computed, defineComponent, onMounted, ref, watch } from 'vue'
import { Editor } from '../../states'
import { $currentScena } from '../../stores'
import { prefix } from '../../utils'
import './index.scss'

export const Options = defineComponent({
  name: 'Options',
  setup() {
    const mainPtr = useBizMain()
    const editor = Editor.impl

    const currentScena = useStoreStateValue($currentScena)

    const backgroundColor = ref('')
    const screenRes = ref()

    const resolutions = computed<string[]>(() => {
      return mainPtr.value?.resolutions.list() || []
    })

    onMounted(() => {
      backgroundColor.value = currentScena.value?.background || ''
      screenRes.value = mainPtr.value?.resolutions.forValue(currentScena.value?.editres || '')
    })

    watch(
      currentScena,
      () => {
        backgroundColor.value = currentScena.value?.background || ''
        screenRes.value = mainPtr.value?.resolutions.forValue(currentScena.value?.editres || '')
      },
      {
        immediate: true,
        deep: true
      }
    )

    watch(backgroundColor, () => {
      if (currentScena.value?.background !== backgroundColor.value) {
        const background = backgroundColor.value
        editor.changeOption({ background })
      }
    })
    watch(screenRes, () => {
      const editres = mainPtr.value?.resolutions.forKey(screenRes.value)
      if (currentScena.value?.editres !== editres) {
        editor.changeOption({ editres })
      }
    })
    return () => (
      <div class={prefix('options-container')}>
        {/* <div class={prefix('options-item')}>
          <div class={prefix('options-item-label')}>Message Queue: </div>
          <div class={prefix('options-item-value')}>
            <WuiSwitch v-model={queue.value} />
          </div>
        </div> */}
        {/* <div class={prefix('options-item')}>
          <div class={prefix('options-item-label')}>Bitmap: </div>
          <div class={prefix('options-item-value')}>
            <WuiSelect />
          </div>
        </div>
        <div class={prefix('options-item')}>
          <div class={prefix('options-item-label')}>Foreground Color: </div>
          <div class={prefix('options-item-value')}>
            <ColorPicker v-model={foregroundColor.value} />
          </div>
        </div> */}
        <div class={prefix('options-item')}>
          <div class={prefix('options-item-label')}>Background Color: </div>
          <div class={prefix('options-item-value')}>
            <ColorPicker v-model={backgroundColor.value} />
          </div>
        </div>
        <div class={prefix('options-item')}>
          <div class={prefix('options-item-label')}>Screen Res: </div>
          <div class={prefix('options-item-value')}>
            <WuiSelect
              v-model={screenRes.value}
              onChange={editres => {
                editor.changeOption({ editres })
              }}>
              {resolutions.value.map(val => (
                <WuiOption value={val} label={val} />
              ))}
            </WuiSelect>
          </div>
        </div>
      </div>
    )
  }
})
