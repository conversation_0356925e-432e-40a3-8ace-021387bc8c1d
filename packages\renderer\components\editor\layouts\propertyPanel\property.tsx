import { CustomFormItem } from '@/renderer/components/CustomFormItem'
import { PropertyObject } from '@/renderer/components/PropertyObject'
import { PropertyTable } from '@/renderer/components/PropertyTable'
import { useStoreStateValue, useStoreValue } from '@/renderer/store'
import { defineComponent, onMounted, ref } from 'vue'
import { BaseDefineType } from '../../controls/types'
import { Editor } from '../../states'
import { $editor, $selectedLayers } from '../../stores'
import { ScenaElementLayer } from '../../types'
import { prefix } from '../../utils'
import './index.scss'

export const Property = defineComponent({
  name: 'Property',

  setup(props) {
    const editor = Editor.impl
    const selectedLayersStore = useStoreValue($selectedLayers)
    const editorRef = useStoreStateValue($editor)

    const selectedInfo = ref<ScenaElementLayer>()
    const handleSelectedInfo = (params?: {
      width: number
      height: number
      left: number
      top: number
    }) => {
      if (selectedLayersStore.value.length === 1) {
        selectedInfo.value = selectedLayersStore.value[0] as ScenaElementLayer
        if (params) {
          selectedInfo.value.meta.width = params.width
          selectedInfo.value.meta.height = params.height
          selectedInfo.value.meta.left = params.left
          selectedInfo.value.meta.top = params.top
        }
        console.log('selectedInfo-----------', selectedInfo.value)
      }
    }
    editor.actions.on('set.selected.layers', params => {
      handleSelectedInfo()
    })
    // const selectedLayers = computed(() => {
    //   console.log(selectedLayersStore.value)
    //   return selectedLayersStore.value
    // })
    // console.log('..........', selectedLayers)

    onMounted(() => {
      // editor.actions.on('set.selected.layers', params => {
      //   console.log('res---------', params)
      // })
      editor.events.on('update.rect', param => {
        handleSelectedInfo(param as any)
        const config = selectedInfo.value?.schema?.config
        const meta = selectedInfo.value?.meta || {}
        if (config) {
          for (const item of config) {
            //item.value = {}
            Object.entries(meta).forEach(([key, value]) => {
              if (item.type === 'Object') {
                if (key === item.key) {
                  const objValue: any = {}
                  item.childs?.forEach((child: any) => {
                    value &&
                      Object.entries(value).forEach(([k, v]) => {
                        if (k === child.key) {
                          child.value = v
                          if (child.childs) {
                            child.childs[v as any].forEach((child2: any) => {
                              child2.value = value[child2.key]
                            })
                          }
                        }
                      })
                  })
                  item.value = objValue
                }
              } else {
                if (key === item.key) {
                  item.value = value
                }
              }
            })
          }
        }
        console.log('选中信息', selectedInfo.value)
      })
    })

    return () => (
      <div class={prefix('property-container')}>
        {selectedInfo.value && selectedInfo.value?.schema?.config && (
          <div class={prefix('property-grid')}>
            {selectedInfo.value?.schema?.config.map((item: BaseDefineType) => {
              // Table 和 Object 类型的属性应该占据整行
              const isFullWidth = item.type === 'Table' || item.type === 'Object'
              // console.log(
              //   `item.childs[selectedInfo.value.meta?.[${item.key}]`,
              //   selectedInfo.value?.meta?.[item.key]
              // )
              return (
                <div
                  class={[
                    prefix('property-item'),
                    isFullWidth ? prefix('property-item--full') : prefix('property-item--half'),
                    (!!item.childs && prefix('property-item-childs')) || ''
                  ]}>
                  {item.type === 'Object' ? (
                    <PropertyObject
                      data={item}
                      selectedInfo={selectedInfo.value}
                      onUpdate:modelValue={(e: any) => {
                        item.value = e
                        if (selectedInfo.value) {
                          if (item.childs) {
                            const result: any = {}
                            item.childs.forEach((child: any) => {
                              result[child.key] = child.value
                              if (child.childs) {
                                child.childs[child.value].forEach((child2: any) => {
                                  result[child2.key] = child2.value
                                })
                              }
                            })
                            editorRef.value?.setProperty(selectedInfo.value, item, {
                              ...result
                            })
                          } else {
                            editorRef.value?.setProperty(selectedInfo.value, item, item.value)
                          }
                        }
                      }}
                    />
                  ) : item.type === 'Table' ? (
                    <PropertyTable
                      data={item.value}
                      columns={item.column}
                      row={item.row}
                      modelValue={item.value}
                      onUpdate:modelValue={(e: any) => {
                        item.value = e
                        if (selectedInfo.value) {
                          editorRef.value?.setProperty(selectedInfo.value, item, item.value)
                        }
                      }}
                    />
                  ) : (
                    <>
                      <CustomFormItem
                        key={item.key}
                        label={item.name}
                        type={item.type as any}
                        unit={item.unit}
                        comment={item.comments}
                        column={item.column}
                        row={item.row}
                        cast={item.cast}
                        range={item.range}
                        modelValue={item.value}
                        onUpdate:modelValue={(e: any) => {
                          item.value = e
                          if (selectedInfo.value) {
                            editorRef.value?.setProperty(selectedInfo.value, item, item.value)
                          }
                        }}
                      />

                      {item.childs && selectedInfo.value?.meta?.[item.key] && (
                        <>
                          {item.childs[selectedInfo.value.meta?.[item.key]]?.map((child: any) => (
                            <CustomFormItem
                              key={`${item.key}-${child.key}`}
                              label={child.name}
                              type={child.type as any}
                              range={child.range}
                              comment={child.comments}
                              modelValue={
                                selectedInfo.value?.meta ? selectedInfo.value?.meta[child.key] : ''
                              }
                              onUpdate:modelValue={(e: any) => {
                                if (selectedInfo.value) {
                                  editorRef.value?.setProperty(selectedInfo.value, child, e)
                                }
                              }}
                            />
                          ))}
                        </>
                      )}
                    </>
                  )}
                </div>
              )
            })}
          </div>
        )}
      </div>
    )
  }
})
