$prefix: 'scena';
.#{$prefix}-tabs {
  width: 40px;
  height: 100%;
  background: var(--scena-editor-color-back2);
  z-index: 10;
  transform: translateZ(10px);
  box-sizing: border-box;
  padding-top: 30px;
  transition: width ease 0.2s;
  text-align: left;
}

.#{$prefix}-tabs.#{$prefix}-over {
  width: 80px;
}
.#{$prefix}-tab-icon-label span:first-letter {
  visibility: visible;
  font-size: 11px;
}
.#{$prefix}-tab-icon-label {
  text-align: center;
}
.#{$prefix}-tabs.#{$prefix}-over .#{$prefix}-tab-icon-label {
  text-align: left;
}
.#{$prefix}-tab-icon-label span {
  visibility: hidden;
  font-size: 0;
  overflow: hidden;
}
.#{$prefix}-tabs.#{$prefix}-over .#{$prefix}-tab-icon-label span {
  font-size: 11px;
  visibility: visible;
}

.#{$prefix}-tab-icon {
  position: relative;
  margin: 5px;
  color: var(--scena-editor-color-text);
}

.#{$prefix}-selected .#{$prefix}-tab-icon-label {
  color: var(--scena-editor-icon-selected);
  background: var(--scena-editor-color-main);
  border-color: var(--scena-editor-color-back1);
}

.#{$prefix}-tab-icon-label {
  position: relative;
  color: var(--scena-editor-color-text);
  font-size: 11px;
  font-weight: bold;
  word-break: break-all;
  padding: 5px;
  width: 100%;
  line-height: 20px;
  height: 30px;
  box-sizing: border-box;
  border-radius: 3px;
  text-align: left;
  overflow: hidden;
  border: 1px solid transparent;
}

.#{$prefix}-tab-icon-label span {
  display: inline-block;
  pointer-events: none;
  font-size: 11px;
}

.#{$prefix}-tab {
  position: absolute;
  right: 100%;
  top: 0;
  transform: translate(-5px);
  width: var(--scena-editor-size-tab);
  padding: 0px 10px 10px;
  background: var(--scena-editor-color-back2);
  border-bottom: 1px solid var(--scena-editor-color-back1);
  text-align: left;
}

.#{$prefix}-tab h2 {
  margin: 0;
  color: var(--scena-editor-color-text);
  font-weight: bold;
  font-size: 14px;
  padding: 8px 0px;
}

.#{$prefix}-tab-line {
  position: relative;
  display: block;
}

// @media screen and (min-width: 100px) {

// }


.#{$prefix}-right {
  padding: 18px 7px 0px;
  .#{$prefix}-right-item-group {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 16px;

    .#{$prefix}-right-item {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 4px;
      border-radius: 4px;
      border: 1px solid transparent;
      cursor: pointer;
      svg {
        width: 1.2em;
        height: 1.2em;
        fill: var(--scena-editor-color-text);
        stroke: var(--scena-editor-color-text);
      }
    }
    .#{$prefix}-right-item-active {
      background: var(--scena-editor-color-main);
      border-color: var(--scena-editor-color-back1);
      border-radius: 4px;
      svg {
        fill: #fff;
        stroke: #fff;
      }
    }
  }
}
