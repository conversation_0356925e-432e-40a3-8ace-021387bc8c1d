import { buildProps, definePropType } from '@wuk/wui/dist/utils'
import { defineComponent, ExtractPropTypes, reactive, ref } from 'vue'
import { HistoryIcon, OptionsIcon, PropertiesIcon } from '../../icon/icons'
import { Editor } from '../../states'
import { prefix } from '../../utils'
import './index.scss'

export const rightProps = buildProps({
  editor: {
    type: definePropType<Editor>(Object)
  },
  showRightPanel: {
    type: Boolean,
    default: false
  }
})
export type RightProps = ExtractPropTypes<typeof rightProps>

export const RightView = defineComponent({
  name: 'RightView',
  props: rightProps,
  setup(props, { expose }) {
    const tabs = ref<HTMLDivElement>()
    const state = reactive({
      selected: ''
    })

    const blur = () => {
      // tabs.value?.classList.remove('scena-over')
    }

    const onMouseOver = () => {
      // tabs.value?.classList.add('scena-over')
    }

    const onClick = (e: any) => {
      onMouseOver()
      const target = e.target
      const prevSelected = state.selected
      const selected = target.getAttribute('data-target-id')

      state.selected = prevSelected === selected ? '' : selected
    }

    return () => (
      // <div class={prefix('tabs')} ref={tabs} onMouseover={onMouseOver} onMouseout={blur}>
      //   {renderTabs()}
      // </div>
      <div class={prefix('right')}>
        <div class={prefix('right-item-group')}>
          <div class={prefix('right-item')}>
            <OptionsIcon />
          </div>
          <div class={prefix('right-item')}>
            <PropertiesIcon />
          </div>
          <div class={prefix('right-item')}>
            <HistoryIcon />
          </div>
        </div>
      </div>
    )
  }
})
