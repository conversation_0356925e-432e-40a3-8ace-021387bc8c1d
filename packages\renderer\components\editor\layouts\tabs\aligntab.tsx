import { ComponentRef } from '@/renderer/components/base'
import { HTMLAttributes } from 'vue'
import { prefix } from '../../utils'

import { useStoreStateValue, useStoreValue } from '@/renderer/store'
import { Editor } from '../../states'
import { $editor, $selectedLayers } from '../../stores'
import { ScenaElementLayer } from '../../types'
import './index.scss'

type AlignType = 'horizontal' | 'vertical'
type AlignDirection = 'start' | 'center' | 'end'
export interface AlignProps {
  type: AlignType
  direction: AlignDirection
  onClick: (type?: AlignType, direction?: AlignDirection) => any
}

const TYPES = ['vertical', 'horizontal'] as const
const DIRECTIONS = ['start', 'center', 'end'] as const

const AlignView = ComponentRef<HTMLDivElement, Partial<AlignProps>>('AlignView', (props, ref) => {
  const handleClick = () => {
    props.onClick?.(props.type, props.direction)
  }

  return () => (
    <div
      ref={ref}
      class={prefix('align', `align-${props.type}`, `align-${props.direction}`)}
      onClick={handleClick}>
      <div class={prefix('align-line')}></div>
      <div class={prefix('align-element1')}></div>
      <div class={prefix('align-element2')}></div>
    </div>
  )
})

function getDirectionPos(
  type: AlignType,
  direction: AlignDirection,
  rect: { left: number; top: number; width: number; height: number }
): number {
  let size: number
  let start: number
  if (type === 'horizontal') {
    size = rect.height
    start = rect.top
  } else {
    size = rect.width
    start = rect.left
  }
  if (direction === 'start') {
    return start
  }
  if (direction === 'center') {
    return start + size / 2
  }
  return start + size
}

export const AlignTab = ComponentRef<HTMLDivElement, Partial<HTMLAttributes>>(
  'AlignView',
  (props, ref) => {
    const editor = Editor.impl
    const selectedLayers = useStoreValue($selectedLayers)
    const editorRef = useStoreStateValue($editor)

    const onClick = async (type?: AlignType, direction?: AlignDirection) => {
      if (!type || !direction) {
        return
      }

      const moveable = editor.moveable
      if (!moveable) {
        return
      }

      const flatted = editor.layers.toFlatten(selectedLayers.value)
      const moveables = (flatted.length > 1 && moveable.getManager().moveables) || []
      if (moveables.length < 2) {
        return
      }

      const rect = moveable.getRect()
      const pos = getDirectionPos(type, direction, rect)

      const targets: { layer: ScenaElementLayer; left: number; top: number }[] = []
      moveables.forEach(child => {
        const target = child.state.target!
        const layer = target && editor.layers.getLayerByElement(target)
        if (layer) {
          const rect = child.getRect()
          const subPos = getDirectionPos(type, direction, rect)
          const delta = pos - subPos
          const translate = [rect.left || 0, rect.top || 0]
          translate[type === 'horizontal' ? 1 : 0] += delta
          targets.push({ layer, left: translate[0], top: translate[1] })
        }
      })

      for (let index = 0; index < targets.length; ++index) {
        const { layer, left, top } = targets[index]
        await editorRef.value?.setFrameProperties(layer, { left, top })
      }

      moveable.updateRect()
    }

    return () => (
      <div ref={ref} {...props} class={prefix('align-tab')}>
        {TYPES.map(type => {
          return DIRECTIONS.map(direction => {
            return (
              <AlignView key={`${type}-${direction}`} forProps={{ type, direction, onClick }} />
            )
          })
        })}
      </div>
    )
  }
)

export default AlignTab
