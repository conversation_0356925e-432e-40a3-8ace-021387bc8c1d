$prefix: 'scena';
.#{$prefix}-top {
  width: 100%;
  height: var(--scena-editor-size-top);
  min-height: var(--scena-editor-size-top);
  background: var(--scena-editor-color-back2);
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
}

.#{$prefix}-top svg, .#{$prefix}-top .#{$prefix}-i {
  pointer-events: none;
}

.#{$prefix}-top .#{$prefix}-selected {
  background: var(--scena-editor-color-main);
  border-color: var(--scena-editor-color-back1);
}

.#{$prefix}-top-icon {
  width: 28px;
  height: 28px;
  margin: 0 5px;
}

.#{$prefix}-top-title {
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--scena-editor-color-text);
}

.#{$prefix}-top-left {
  display: flex;
  align-items: center;
}

.#{$prefix}-top-right {
  min-width: 28px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.#{$prefix}-top-save:hover {
  border-radius: 4px;
  border-color: #fff;
  border-style: solid;
  border-width: 1px;
  background: var(--scena-editor-color-main);
  svg {
    fill: var(--scena-editor-color-back1);
  }
}
