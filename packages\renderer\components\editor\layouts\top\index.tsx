import { buildProps, definePropType } from '@wuk/wui/dist/utils'
import { defineComponent, ExtractPropTypes } from 'vue'
import { IconView } from '../../icon'
import { DarkModeIcon, LightModeIcon, SaveIcon } from '../../icon/icons'
import { Editor } from '../../states'
import { prefix } from '../../utils'
import './index.scss'
import { useStoreState, useStoreStateValue } from '@/renderer/store'
import { $currentScena, $darkMode } from '../../stores'

export const topProps = buildProps({
  selected: {
    type: String,
    default: ''
  },
  onSelect: {
    type: definePropType<(id: string) => any>(Function)
  },
  onSave: {
    type: definePropType<() => any>(Function)
  }
})
export type TopProps = ExtractPropTypes<typeof topProps>

export const TopView = defineComponent({
  name: 'TopView',
  props: topProps,
  setup(props, { expose }) {
    const menu = Editor.impl.menu
    const currentScena = useStoreStateValue($currentScena)
    const [darkMode, setDarkMode] = useStoreState($darkMode)

    const handleSelect = (id: string) => {
      props.onSelect?.(id)
    }
    return () => (
      <div class={prefix('top')}>
        <div class={prefix('top-left')}>
          {menu.icons.map((icon, index) => {
            return (
              <IconView
                class={prefix('top-icon')}
                key={`top_${icon.id}_${index}`}
                icon={icon}
                selected={props.selected === icon.id}
                onSelect={handleSelect}
              />
            )
          })}
        </div>
        <div class={prefix('top-title')}>{currentScena.value?.file || ''}</div>
        <div class={prefix('top-right')}>
          <div
            onClick={() => {
              props.onSave?.()
            }}>
            <SaveIcon style={{ width: '20px', height: '20px', marginRight: '10px' }} />
          </div>
          <div
            onClick={() => {
              setDarkMode(!darkMode.value)
            }}>
            {(darkMode.value && (
              <LightModeIcon class={prefix('top-icon')} style={{ width: '20px', height: '20px' }} />
            )) || (
              <DarkModeIcon class={prefix('top-icon')} style={{ width: '20px', height: '20px' }} />
            )}
          </div>
        </div>
      </div>
    )
  }
})
