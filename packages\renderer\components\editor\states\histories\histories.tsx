import { diff } from '@egjs/list-differ'
import { ScenaElementLayer, ScenaElementLayerGroup, EditorInstance, MovedResult } from '../../types'
import Historys from '../historys'

export interface LayerGroupProps {
  layers: Array<ScenaElementLayer>
  groups: Array<ScenaElementLayerGroup>
  selects?: Array<ScenaElementLayer | ScenaElementLayerGroup>
}

export interface RenderLayerProps {
  prevs: LayerGroupProps
  nexts: LayerGroupProps
}

interface RenderHistoryProps {
  layer: ScenaElementLayer
  prev: any
  next: any
}

interface RenderGroupHistoryProps {
  infos: RenderHistoryProps[]
}

interface SelectHistoryProps {
  prevs: Array<ScenaElementLayer | ScenaElementLayerGroup>
  nexts: Array<ScenaElementLayer | ScenaElementLayerGroup>
}

function restoreRender(layer: ScenaElementLayer, prev: any, next: any, editor: EditorInstance) {
  const el = layer.root.current

  if (!el) {
    throw new Error('No Element')
    // return false;
  }

  const frame = editor.state.layers.getFrame(layer)

  frame.clear()
  frame.set(next)

  const result = diff(Object.keys(prev), Object.keys(next))
  const { removed, prevList } = result

  removed.forEach(index => {
    el.style.removeProperty(prevList[index])
  })
  el.style.cssText += frame.toCSSText()
  return true
}

function undoRender({ layer, prev, next }: RenderHistoryProps, editor: EditorInstance) {
  if (!restoreRender(layer, next, prev, editor)) {
    return
  }
  editor.state.moveable?.updateRect()
  editor.state.actions.act('render.end')
}
function redoRender({ layer, prev, next }: RenderHistoryProps, editor: EditorInstance) {
  if (!restoreRender(layer, prev, next, editor)) {
    return
  }
  editor.state.moveable?.updateRect()
  editor.state.actions.act('render.end')
}

function undoRenderGroup({ infos }: RenderGroupHistoryProps, editor: EditorInstance) {
  infos.forEach(({ layer, prev, next }) => {
    restoreRender(layer, next, prev, editor)
  })
  editor.state.moveable?.updateRect()
  editor.state.actions.act('render.end')
}

function redoRenderGroup({ infos }: RenderGroupHistoryProps, editor: EditorInstance) {
  infos.forEach(({ layer, prev, next }) => {
    restoreRender(layer, prev, next, editor)
  })
  editor.state.moveable?.updateRect()
  editor.state.actions.act('render.end')
}

function undoSelectTargets({ prevs }: SelectHistoryProps, editor: EditorInstance) {
  editor.setSelectedLayers(prevs, true)
}

function redoSelectTargets({ nexts }: SelectHistoryProps, editor: EditorInstance) {
  editor.setSelectedLayers(nexts, true)
}

function undoChangeLayer({ prevs }: RenderLayerProps, editor: EditorInstance) {
  const { layers, groups, selects } = prevs || {}
  if (layers === undefined) {
    return
  }
  editor.setLayers(layers, groups, selects, true)
}

function redoChangeLayer({ nexts }: RenderLayerProps, editor: EditorInstance) {
  const { layers, groups, selects } = nexts || {}
  if (layers === undefined) {
    return
  }
  editor.setLayers(layers, groups, selects, true)
}

function undoMove({ prevInfos }: MovedResult, editor: EditorInstance) {
  // editor.state.moves(prevInfos, true)
}

function redoMove({ nextInfos }: MovedResult, editor: EditorInstance) {
  // editor.state.moves(nextInfos, true)
}

export interface Histories {
  // render: RenderHistoryProps
  render: RenderGroupHistoryProps
  selectTargets: SelectHistoryProps
}

export function registerHistoryTypes(history: Historys) {
  history.registerType('layer', undoChangeLayer, redoChangeLayer, 'render change')
  history.registerType('render', undoRenderGroup, redoRenderGroup, 'render elements')
  history.registerType('selectTargets', undoSelectTargets, redoSelectTargets, 'select targets')
  history.registerType('move', undoMove, redoMove, 'move elements')
}
