import { IObject } from '@daybrush/utils'
import Debugger from '../utils/debugger'
import type { EditorInstance, ViewRef } from '../types'
import Actions from './actions'

export type RestoreCallback = (props: any, editor: EditorInstance) => any
export interface HistoryAction {
  type: string
  props: IObject<any>
  description?: string
}

export default class Historys<Histories extends IObject<any> = any> {
  public undoStack: HistoryAction[] = []
  public redoStack: HistoryAction[] = []
  public currentHistory: HistoryAction | null = null
  private types: Record<
    string,
    {
      redo: RestoreCallback
      undo: RestoreCallback
      description?: string
    }
  > = {}
  constructor(
    private readonly editor: ViewRef<EditorInstance | undefined>,
    private readonly actions: Actions
  ) {}

  public registerType(
    type: string,
    undo: RestoreCallback,
    redo: RestoreCallback,
    description?: string
  ) {
    this.types[type] = { undo, redo, description }
  }
  public addHistory<Name extends keyof Histories, <PERSON>ps extends Histories[Name]>(
    type: Name & string,
    props: Props,
    description?: string
  ) {
    const historyType = (this.types as any)[type] as HistoryAction

    if (!historyType) {
      return
    }
    Debugger.groupLog('history', `Add:`, type, props)

    this.currentHistory = {
      type,
      props,
      description: description || historyType.description
    }
    this.undoStack.push(this.currentHistory)
    this.redoStack = []

    this.actions.act('history.add')
  }
  public undo() {
    const editor = this.editor.current!
    const undoStack = this.undoStack
    const undoAction = undoStack.pop()

    if (!undoAction) {
      if (this.currentHistory) {
        this.currentHistory = null
        this.actions.act('history.undo')
      }
      return
    }
    this.currentHistory = undoStack[undoStack.length - 1]
    Debugger.groupLog('history', `Undo: ${undoAction.type}`, undoAction.props)
    this.types[undoAction.type].undo(undoAction.props, editor)
    this.redoStack.push(undoAction)
    this.actions.act('history.undo')
  }
  public redo() {
    const editor = this.editor.current!
    const redoAction = this.redoStack.pop()

    if (!redoAction) {
      return
    }
    this.currentHistory = redoAction
    Debugger.groupLog('history', `Redo: ${redoAction.type}`, redoAction.props)
    this.types[redoAction.type].redo(redoAction.props, editor)
    this.undoStack.push(redoAction)
    this.actions.act('history.redo')
  }
  public clear() {
    this.currentHistory = null
    this.redoStack = []
    this.undoStack = []
  }
}
