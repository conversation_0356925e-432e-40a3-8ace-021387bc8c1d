import KeyController from 'keycon'
import { input<PERSON>hecker } from '../utils'
import { StoreRootValue, StoreState } from '@/renderer/store'
import Debugger from '../utils/debugger'
import Actions from './actions'

export default class Keywords {
  public keycon = new KeyController()
  public keylist: Array<[string[], string]> = []
  public isEnable = true
  constructor(private readonly root: StoreRootValue, private readonly action: Actions) {}

  public enable() {
    this.isEnable = true
  }
  public disable() {
    this.isEnable = false
  }
  public toggleState(keys: string[], state: StoreState<boolean>, callback?: (e: any) => any) {
    this.keydown(
      keys,
      e => {
        callback?.(e)
        this.root.set(state, true)
      },
      `key toggle down`
    )

    this.keyup(
      keys,
      e => {
        callback?.(e)
        this.root.set(state, false)
      },
      `key toggle up`
    )
  }

  public actionDown(keys: string[], actionName: string) {
    this.keycon.keydown(
      keys,
      this.addCallback(
        'keydown',
        keys,
        e => {
          this.action.act(actionName, {
            inputEvent: e.inputEvent
          })
        },
        `action down: ${actionName}`
      )
    )
  }

  public actionUp(keys: string[], actionName: string) {
    this.keycon.keyup(
      keys,
      this.addCallback(
        'keyup',
        keys,
        e => {
          this.action.act(actionName, {
            inputEvent: e
          })
        },
        `action up: ${actionName}`
      )
    )
  }

  public keydown(keys: string[], callback: (e: any) => any, description?: any) {
    this.keycon.keydown(keys, this.addCallback('keydown', keys, callback, description))
  }

  public keyup(keys: string[], callback: (e: any) => any, description?: any) {
    this.keycon.keyup(keys, this.addCallback('keyup', keys, callback, description))
  }

  get altKey() {
    return this.keycon.altKey
  }

  get shiftKey() {
    return this.keycon.shiftKey
  }

  get metaKey() {
    return this.keycon.metaKey
  }

  get ctrlKey() {
    return this.keycon.ctrlKey
  }

  public destroy() {
    this.keycon.destroy()
  }

  public clear() {
    this.keycon.clear()
  }

  private addCallback(
    type: string,
    keys: string[],
    callback: (e: any) => any,
    description?: string
  ) {
    if (description) {
      this.keylist.push([keys, description])
    }
    return (e: any) => {
      if (!this.isEnable || !inputChecker(e)) {
        return false
      }
      const result = callback(e)

      if (result !== false && description) {
        Debugger.groupLog('key', `${type}: ${keys.join(' + ')}`, description)
      }
    }
  }
}
