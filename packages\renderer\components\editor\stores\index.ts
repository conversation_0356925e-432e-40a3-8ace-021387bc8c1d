import { UDisplay } from '@wuk/cfg'
import type Guides from 'vue3-guides'
import type InfiniteViewer from 'vue3-infinite-viewer'
import type Moveable from 'vue3-moveable'
import type Selecto from 'vue3-selecto'
import { MoveToolIcon } from '../icon'
import { HistoryAction } from '../states/historys'
import { EditorInstance, ScenaElementLayer, ScenaElementLayerGroup, ScenaInfo } from '../types'
import { atom } from './keys'

export * from './keys'

export const $showGuides = atom<boolean>(true)
export const $darkMode = atom<boolean>(false)

export const $horizontalGuidelines = atom<number[]>([])
export const $verticalGuidelines = atom<number[]>([])

export const $scrollPos = atom<number[]>([0, 0])
export const $zoom = atom<number>(1)
export const $isMove = atom<boolean>(false)
export const $groupOrigin = atom<string>('50% 50%')
export const $selectedTool = atom<string>('pointer')
export const $pointer = atom<string>('move')
export const $rect = atom<string>('rect')

export const $selectedMenu = atom<string>(MoveToolIcon.id)
export const $currentScena = atom<ScenaInfo | undefined>(undefined)

export const $selecto = atom<Selecto | null>(null)
export const $moveable = atom<Moveable | null>(null)
export const $infiniteViewer = atom<InfiniteViewer | null>(null)
export const $horizontalGuides = atom<Guides | null>(null)
export const $verticalGuides = atom<Guides | null>(null)
export const $display = atom<UDisplay | null>(null)
export const $historyList = atom<HistoryAction[]>([])
export const $fileIndex = atom<number>(0)
export const $originDisplay = atom<UDisplay | null>(null)
export const $editor = atom<EditorInstance | null>(null)

export const $selectedLayers = atom<Array<ScenaElementLayer | ScenaElementLayerGroup>>([])
export const $layers = atom<ScenaElementLayer[]>([])
