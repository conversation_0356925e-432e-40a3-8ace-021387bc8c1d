import { IObject } from '@daybrush/utils'
import { CrtResolutionType, UDisplay } from '@wuk/cfg'
import { SceneItem } from 'scenejs'
import type { Ref, VNode } from 'vue'
import { DragBoxItem } from '../controls'
import { BaseDefineType } from '../controls/types'
import { Editor } from '../states'

export interface ViewRef<T = any> {
  current: T
  value: T
}

export interface ViewFrameProperty extends Record<string, any> {
  left: number
  top: number
  width: number
  height: number
  position: string
  border: string
}

export interface ViewInvoke<T = any> {
  refresh: (data: Partial<T>) => boolean
}

export class ViewElement<T> implements ViewRef<T> {
  constructor(private _val: T) {}
  get value() {
    return this._val
  }
  set value(val: T) {
    this._val = val
  }
  get current() {
    return this._val
  }
  set current(val: T) {
    this._val = val
  }
}

export interface ScenaInfo {
  name: string
  file: string
  editres: string
  width: number
  height: number
  background: string
  index: number
}

export interface ScenaEditorState {
  currentScena?: ScenaInfo
  // selectedMenu: string
  zoom: number
  showComponentPanel: boolean // 组件面板
  showControlsPanel: boolean // 控制面板
  showLayersPanel: boolean // 图层面板
  showOptionsPanel: boolean // 选项面板
  showPropertiesPanel: boolean // 属性面板
  showHistoryPanel: boolean // 历史面板
  showRightPanel: boolean // 右侧面板
}

export interface TagAppendInfo {
  tag: any
  props: IObject<any>
  name: string
  frame: IObject<any>
}

export interface Clipboard {
  write(items: ClipboardItem[]): Promise<void>
}
export interface ClipboardItem {
  types: string[]
  getType(type: string): Promise<Blob>
}

export interface SavedScenaData {
  name: string
  jsxId: string
  componentId: string
  tagName: string
  view?: ViewRef<ViewInvoke>
  innerHTML?: string
  innerText?: string
  attrs: IObject<any>
  frame: IObject<any>
  schema?: DragBoxItem
  values?: IObject<any>
  children: SavedScenaData[]
}
export interface ScenaProps {
  scenaElementId?: string
  scenaAttrs?: IObject<any>
  scenaText?: string
  scneaHTML?: string
}

export interface ElementInfo {
  ref?: any
  jsx: ScenaJSXType
  name: string
  view?: ViewRef<ViewInvoke>
  frame?: IObject<any>
  schema?: DragBoxItem
  values?: IObject<any>
  scopeId?: string
  children?: ElementInfo[]
  attrs?: IObject<any>
  componentId?: string
  jsxId?: string
  el?: HTMLElement | null
  id?: string
  index?: number
  innerText?: string
  innerHTML?: string
}

export type ScenaFunctionComponent<T> = ((props: T & ScenaProps) => VNode) & {
  scenaComponentId: string
}
export type ScenaComponent = VNode & { scenaComponentId: string }
export type ScenaJSXElement = VNode | ScenaFunctionJSXElement
export type ScenaFunctionJSXElement = VNode
export type ScenaJSXType = ScenaJSXElement | string | ScenaComponent

export type ElementProps = { [key: string]: any }
export type ChildElement = VNode | string | number | null | undefined

export interface AddedInfo {
  added: ElementInfo[]
}

export interface RemovedInfo {
  removed: ElementInfo[]
}

export interface MovedInfo {
  info: ElementInfo
  parentInfo: ElementInfo
  prevInfo?: ElementInfo
}

export interface MovedResult {
  moved: ElementInfo[]
  prevInfos: MovedInfo[]
  nextInfos: MovedInfo[]
}

export interface DisplayScenaInfo {
  name: string
  option: ScenaInfo
  meta: UDisplay
}

export type ScenaTargetGroupsType = Array<
  Ref<HTMLElement | SVGElement | null> | ScenaTargetGroupsType
>

export interface ScenaElementLayer<T = any> {
  type?: 'layer'
  id: string
  title: string
  scope: string[]
  jsx: ScenaJSXType
  item: SceneItem
  root: ViewElement<SVGElement | HTMLElement | null>
  view?: ViewElement<ViewInvoke | null>
  frame: ViewElement<ViewInvoke | null>
  schema?: DragBoxItem
  meta: Partial<UDisplay> & Partial<ViewFrameProperty> & Record<string, any>
  slots: Array<ScenaElementLayer>
}

export interface ScenaElementLayerGroup {
  type: 'group'
  id: string
  title: string
  scope: string[]
  root?: ViewElement<SVGElement | HTMLElement | null>
  children: Array<ScenaElementLayerGroup | ScenaElementLayer>
  opacity: number
  display: string
}

export interface EditorInstance {
  readonly state: Editor
  setLayers(
    layers: ScenaElementLayer[],
    groups?: ScenaElementLayerGroup[],
    selectedLayerGroups?: Array<ScenaElementLayer | ScenaElementLayerGroup>,
    isRestore?: boolean
  ): Promise<boolean>
  setSelectedLayers(
    layerGroups: Array<ScenaElementLayer | ScenaElementLayerGroup>,
    isRestore?: boolean
  ): Promise<boolean>
  removeSelectedLayers(): Promise<boolean>
  removeLayers(
    layerGroups: Array<ScenaElementLayer | ScenaElementLayerGroup>,
    isRestore?: boolean
  ): Promise<boolean>
  setProperty<T extends BaseDefineType, V = any>(
    layer: ScenaElementLayer,
    cfg: T,
    val: V
  ): Promise<void>
  setFrameProperties(
    layer: ScenaElementLayer,
    attrs: Partial<ViewFrameProperty>,
    isRefresh?: boolean
  ): Promise<void>
  setViewProperties(
    layer: ScenaElementLayer,
    attrs: Record<string, any>,
    isRefresh?: boolean
  ): Promise<void>
  clear(): void
}
