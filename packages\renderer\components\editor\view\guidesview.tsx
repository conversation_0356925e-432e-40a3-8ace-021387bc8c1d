import { computed } from 'vue'
import Guides from 'vue3-guides'
import { GuideOptions } from '@scena/guides'
import { throttle } from '@daybrush/utils'
import { ComponentRef } from '../../base'

import { useStoreState, useStoreStateValue } from '../../../store'
import {
  $currentScena,
  $darkMode,
  $horizontalGuidelines,
  $scrollPos,
  $verticalGuidelines,
  $zoom
} from '../stores'
import { useAction } from '../hooks'
import { Editor } from '../states'

function dragPosFormat(value: number) {
  return `${value}px`
}

export const GuidesView = ComponentRef<Guides, GuideOptions>('GuidesView', (props, guidesRef) => {
  const editor = Editor.impl

  const isHorizontal = computed(() => props.type === 'horizontal')
  const [guidelines, setGuidelines] = useStoreState(
    isHorizontal.value ? $horizontalGuidelines : $verticalGuidelines
  )
  const darkMode = useStoreStateValue($darkMode)
  const zoom = useStoreStateValue($zoom)
  const currentScena = useStoreStateValue($currentScena)
  const scrollPos = useStoreStateValue($scrollPos)
  const snaps = computed(() => [0, ...guidelines.value])
  const rectAction = useAction<any>('get.rect')

  const unit = computed(() => {
    let unit = 50

    if (zoom.value < 0.8 && zoom.value !== 0) {
      unit = Math.floor(1 / zoom.value) * 50
    }

    return unit
  })

  const selectedRanges = computed(() => {
    let selectedRanges!: number[][]
    const rct = rectAction.value?.rect
    if (rct && rct.width && rct.height) {
      selectedRanges = [
        isHorizontal.value ? [rct.left, rct.left + rct.width] : [rct.top, rct.top + rct.height]
      ]
    } else if (currentScena.value) {
      const { width, height } = currentScena.value
      selectedRanges = [isHorizontal.value ? [0, width] : [0, height]]
    }

    return selectedRanges
  })

  const defaultScrollPos = computed(() => {
    const [h, v] = scrollPos.value
    return isHorizontal.value ? h : v
  })

  const defaultGuidesPos = computed(() => {
    const [h, v] = scrollPos.value
    return isHorizontal.value ? v : h
  })

  const onChangeGuides = (e: { guides: number[] }) => {
    setGuidelines(e.guides)
  }

  const textFormat = (v: any) => `${throttle(v, 0.1)}`

  return () => (
    <Guides
      ref={guidesRef}
      snapThreshold={5}
      snaps={snaps.value}
      displayDragPos={true}
      textFormat={textFormat}
      dragPosFormat={dragPosFormat}
      zoom={zoom.value}
      unit={unit.value}
      textColor={darkMode.value ? '#fff' : '#555'}
      backgroundColor={darkMode.value ? '#333' : '#eee'}
      lineColor={darkMode.value ? '#777' : '#ccc'}
      selectedBackgroundColor={'#55bbff33'}
      useResizeObserver={true}
      selectedRangesText={true}
      selectedRanges={selectedRanges.value}
      defaultGuidesPos={defaultGuidesPos.value}
      defaultScrollPos={defaultScrollPos.value}
      defaultGuides={guidelines.value}
      onChangeGuides={onChangeGuides}
      {...props}
    />
  )
})
