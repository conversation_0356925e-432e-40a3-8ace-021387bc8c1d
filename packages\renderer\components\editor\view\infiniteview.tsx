import { InfiniteViewerOptions } from 'infinite-viewer'
import <PERSON><PERSON>iewer from 'vue3-infinite-viewer'
import { useStoreState, useStoreStateSetValue, useStoreStateValue } from '../../../store'
import { ComponentRef } from '../../base'
import { Editor } from '../states'
import {
  $horizontalGuides,
  $isMove,
  $moveable,
  $scrollPos,
  $selectedLayers,
  $selecto,
  $space,
  $verticalGuides,
  $zoom
} from '../stores'
import { prefix } from '../utils'

export interface InfiniteViewProps extends InfiniteViewerOptions {
  className: string
}

export const InfiniteView = ComponentRef<InfiniteViewer, Partial<InfiniteViewProps>>(
  'InfiniteView',
  (props, viewerRef, { slots }) => {
    const editor = Editor.impl

    const selectoRef = useStoreStateValue($selecto)
    const moveableRef = useStoreStateValue($moveable)
    const horizontalGuidesRef = useStoreStateValue($horizontalGuides)
    const verticalGuidesRef = useStoreStateValue($verticalGuides)
    const selectedLayersStore = useStoreStateValue($selectedLayers)

    const isMove = useStoreStateValue($isMove)
    const isSpace = useStoreStateValue($space)
    const [zoom, setZoom] = useStoreState($zoom)
    const setScrollPos = useStoreStateSetValue($scrollPos)

    return () => (
      <InfiniteViewer
        ref={viewerRef}
        className={prefix('viewer', isMove.value || isSpace.value ? 'viewer-move' : '')}
        usePinch={true}
        useAutoZoom={true}
        useWheelScroll={true}
        useForceWheel={true}
        useMouseDrag={isMove.value || isSpace.value}
        useResizeObserver={true}
        wheelContainer={'.scena-canvas'}
        pinchThreshold={50}
        maxPinchWheel={3}
        margin={0}
        zoom={zoom.value}
        displayHorizontalScroll={false}
        displayVerticalScroll={false}
        onDragStart={(e: any) => {
          const target = e.inputEvent.target
          const flatted = editor.layers.toFlattenElement(selectedLayersStore.value)

          editor.actions.act('blur')

          const moveable = moveableRef.value
          if (
            target.nodeName === 'A' ||
            moveable?.isMoveableElement(target) ||
            moveable?.isDragging() ||
            flatted.some(t => t === target || t?.contains(target))
          ) {
            e.stop()
          }
        }}
        onDragEnd={(e: any) => {
          if (!e.isDrag) {
            selectoRef.value!.clickTarget(e.inputEvent)
          }
        }}
        onAbortPinch={(e: any) => {
          selectoRef.value!.triggerDragStart(e.inputEvent)
        }}
        onScroll={(e: any) => {
          const horizontalGuides = horizontalGuidesRef.value!
          const verticalGuides = verticalGuidesRef.value!

          if (horizontalGuides && verticalGuides) {
            let scroll = horizontalGuides.scroll as any
            scroll(e.scrollLeft, e.zoomX)
            let scrollGuides = horizontalGuides.scrollGuides as any
            scrollGuides(e.scrollTop, e.zoomY)

            scroll = verticalGuides.scroll as any
            scroll(e.scrollTop, e.zoomX)
            scrollGuides = verticalGuides.scrollGuides as any
            scrollGuides(e.scrollLeft, e.zoomY)
          }
          setScrollPos([e.scrollLeft, e.scrollTop])
          setZoom(e.zoomX)
        }}
        onPinch={(e: any) => {
          if (moveableRef.value?.isDragging()) {
            return
          }
          setZoom(e.zoom)
        }}>
        {slots.default?.()}
      </InfiniteViewer>
    )
  }
)
