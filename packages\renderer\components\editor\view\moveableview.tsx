import { computed, onMounted, reactive, ref, watch } from 'vue'
import Moveable, { MoveableProperties, SnapDirections } from 'vue3-moveable'
import { useStoreStateValue } from '../../../store'
import { ComponentRef } from '../../base'
import { Editor } from '../states'
import {
  $alt,
  $editor,
  $groupOrigin,
  $horizontalGuidelines,
  $infiniteViewer,
  $isMove,
  $layers,
  $pointer,
  $selectedLayers,
  $selectedMenu,
  $selecto,
  $verticalGuidelines,
  $zoom
} from '../stores'
import { getContentElement } from '../utils'
import { DeleteButtonViewable } from './ables/DeleteButtonViewable'
import { DimensionViewable } from './ables/DimensionViewable'

const SNAP_DIRECTIONS: SnapDirections = {
  top: true,
  left: true,
  right: true,
  center: true,
  middle: true,
  bottom: true
}

export const MoveableView = ComponentRef<Moveable, MoveableProperties>(
  'MoveableView',
  (props, viewerRef) => {
    const horizontalGuidelines = useStoreStateValue($horizontalGuidelines)
    const verticalGuidelines = useStoreStateValue($verticalGuidelines)
    const selectedMenu = useStoreStateValue($selectedMenu)
    const pointer = useStoreStateValue($pointer)
    const zoom = useStoreStateValue($zoom)
    const groupOrigin = useStoreStateValue($groupOrigin)
    const isMove = useStoreStateValue($isMove)
    const layers = useStoreStateValue($layers)
    const selectedLayers = useStoreStateValue($selectedLayers)
    const altStore = useStoreStateValue($alt)

    const editor = Editor.impl

    const infiniteViewerRef = useStoreStateValue($infiniteViewer)
    const selectoRef = useStoreStateValue($selecto)
    const editorRef = useStoreStateValue($editor)

    const selectedTargets = computed(() => {
      const targetList = editor.layers.toTargetList(selectedLayers.value)
      return [...targetList.displayed()]
    })

    const elementGuidelines = computed(() => {
      const visibleLayers = editor.layers.filterDisplayedLayers(layers.value) || []
      const flattenSelectedLayers = editor.layers.toFlatten(selectedLayers.value)

      return [
        '.scena-viewport',
        ...(visibleLayers
          .filter(layer => !flattenSelectedLayers?.includes(layer))
          .map(layer => layer.root!.value) || [])
      ]
    })

    const state = reactive<{
      isShift?: boolean
    }>({
      isShift: editor.keywords.shiftKey
    })
    const updateRef = ref<number>(0)

    onMounted(() => {
      editor.moveable = viewerRef.value

      state.isShift = editor.keywords.shiftKey
      editor.events.on('on-moveables-update', () => {
        updateRef.value = updateRef.value + 1
      })
    })

    return () => (
      <Moveable
        {...props}
        ables={[DimensionViewable, DeleteButtonViewable]}
        ref={viewerRef}
        target={selectedTargets.value}
        // props={{
        //   dimensionViewable: true,
        //   deleteButtonViewable: false
        // }}
        draggable={true}
        useAccuratePosition={true}
        useResizeObserver={true}
        useMutationObserver={true}
        rotateAroundControls={true}
        pinchable={['rotatable']}
        zoom={1 / zoom.value}
        edge={true}
        throttleResize={1}
        clippable={selectedMenu.value === 'Crop'}
        passDragArea={selectedMenu.value === 'text'}
        checkInput={selectedMenu.value === 'Text'}
        throttleDragRotate={state.isShift ? 45 : 0}
        throttleRotate={state.isShift ? 15 : 0}
        keepRatio={state.isShift}
        resizable={!isMove.value}
        scalable={!isMove.value}
        // rotatable={true}
        defaultGroupOrigin={groupOrigin.value}
        groupableProps={{
          keepRatio: true,
          clippable: false
        }}
        snappable={true}
        snapDirections={SNAP_DIRECTIONS}
        elementSnapDirections={SNAP_DIRECTIONS}
        snapGap={false}
        isDisplayInnerSnapDigit={true}
        roundable={false}
        isDisplayShadowRoundControls={true}
        roundPadding={10}
        roundClickable={true}
        verticalGuidelines={verticalGuidelines.value}
        horizontalGuidelines={horizontalGuidelines.value}
        elementGuidelines={elementGuidelines.value}
        clipArea={true}
        clipVerticalGuidelines={[0, '50%', '100%']}
        clipHorizontalGuidelines={[0, '50%', '100%']}
        clipTargetBounds={true}
        defaultClipPath={editor?.memory.get('crop') || 'inset'}
        scrollContainer={infiniteViewerRef.value?.getContainer()}
        scrollThreshold={30}
        scrollThrottleTime={30}
        getScrollPosition={() => {
          const current = infiniteViewerRef.value

          return (
            (current && [
              current.getScrollLeft({ absolute: true }),
              current.getScrollTop({ absolute: true })
            ]) ||
            []
          )
        }}
        onChangeTargets={() => {
          editor.actions.act('changed.targets')
        }}
        onBeforeResize={e => {
          e.setFixedDirection(altStore.value ? [0, 0] : e.startFixedDirection)
        }}
        onClick={e => {
          const target = e.inputTarget as any
          if (!target) {
            return
          }

          if (e.isDouble && target.isContentEditable) {
            editor?.selectMenu('Text')
            const el = getContentElement(target)
            el?.focus()
          } else if (e.isTrusted) {
            selectoRef.value?.clickTarget(e.inputEvent, target)
          }
        }}
        onClickGroup={e => {
          if (!e.moveableTarget) {
            editorRef.value?.setSelectedLayers([])
            return
          }
          if (e.isDouble) {
            const nextChilds = editor.layers.selectSubChilds(
              selectedTargets.value,
              e.moveableTarget
            )
            editorRef.value?.setSelectedLayers(editor.layers.toLayerGroups(nextChilds))
            return
          } else if (e.isTrusted) {
            selectoRef.value?.clickTarget(e.inputEvent, e.moveableTarget)
          }
        }}
        onRenderStart={e => {
          e.datas.prevData = editor.layers.getCSSByElement(e.target)
        }}
        onRender={e => {
          e.datas.isRender = true
          e.target.style.cssText += e.cssText
          editor.layers.setCSSByElement(e.target, e.cssText, true)
        }}
        onRenderEnd={e => {
          if (!e.datas.isRender) {
            return
          }
          editor.actions.requestAct('render.end')
          const layer = editor.layers.getLayerByElement(e.target)

          if (!layer) {
            return
          }

          editor.historys.addHistory('render', {
            infos: [
              {
                layer,
                prev: e.datas.prevData,
                next: editor.layers.getFrame(layer).toCSSObject()
              }
            ]
          })
        }}
        onRenderGroupStart={e => {
          e.datas.prevDatas = e.targets.map(target => editor.layers.getCSSByElement(target))
        }}
        onRenderGroup={e => {
          e.datas.isRender = true

          e.events.forEach(ev => {
            ev.target.style.cssText += ev.cssText
            editor.layers.setCSSByElement(ev.target, ev.cssText, true)
          })
        }}
        onRenderGroupEnd={e => {
          if (!e.datas.isRender) {
            return
          }
          editor.actions.requestAct('render.end')
          const prevDatas = e.datas.prevDatas
          const infos = e.targets.map((target, i) => {
            const layer = editor.layers.getLayerByElement(target)!

            return {
              layer,
              prev: prevDatas[i],
              next: editor.layers.getFrame(layer).toCSSObject()
            }
          })

          editor.historys.addHistory('render', {
            infos
          })
        }}
      />
    )
  }
)
