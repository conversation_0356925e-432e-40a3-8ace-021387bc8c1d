import { TargetList } from '@moveable/helper'
import { SelectoOptions } from 'selecto'
import { onMounted, ref } from 'vue'
import { getElementInfo } from 'vue3-moveable'
import Selecto from 'vue3-selecto'
import { useStoreStateValue, useStoreValue } from '../../../store'
import { ComponentRef } from '../../base'
import { Editor } from '../states'
import { $editor, $infiniteViewer, $isMove, $layers, $moveable, $selectedLayers } from '../stores'
import { $meta, $shift, $space } from '../stores/keys'

export const SelectoView = ComponentRef<Selecto, Partial<SelectoOptions>>(
  'SelectoView',
  (props, rootRef) => {
    const editor = Editor.impl

    const spaceStore = useStoreValue($space)
    const metaStore = useStoreValue($meta)
    const shiftStore = useStoreValue($shift)
    const isMove = useStoreStateValue($isMove)

    const layers = useStoreStateValue($layers)
    const selectedLayersStore = useStoreValue($selectedLayers)

    const moveableRef = useStoreStateValue($moveable)
    const infiniteViewerRef = useStoreStateValue($infiniteViewer)
    const editorRef = useStoreStateValue($editor)

    const mouseDown = ref<boolean>(false)

    return () => (
      <Selecto
        ref={rootRef}
        getElementRect={getElementInfo}
        dragContainer={'.scena-viewer'}
        hitRate={0}
        rootContainer={infiniteViewerRef as any}
        selectableTargets={layers.value.map(layer => layer.root)}
        selectByClick={true}
        selectFromInside={false}
        toggleContinueSelect={['shift']}
        preventDefault={true}
        scrollOptions={{
          container: () => infiniteViewerRef.value!.getContainer(),
          threshold: 30,
          throttleTime: 30,
          getScrollPosition: () => {
            const current = infiniteViewerRef.value!
            return [
              current.getScrollLeft({ absolute: true }),
              current.getScrollTop({ absolute: true })
            ]
          }
        }}
        onDragStart={e => {
          if (isMove.value || spaceStore.value) {
            e.stop()
            return
          }
          const inputEvent = e.inputEvent
          const target = inputEvent.target

          // check blur
          editor.actions.act('blur')

          const flatted = editor.layers.toFlattenElement(selectedLayersStore.value)
          if (
            (inputEvent.type === 'touchstart' && e.isTrusted) ||
            moveableRef.value!.isMoveableElement(target) ||
            flatted.some(t => t === target || t.contains(target))
          ) {
            e.stop()
          }
        }}
        onScroll={({ direction }) => {
          infiniteViewerRef.value!.scrollBy(direction[0] * 10, direction[1] * 10)
        }}
        onDragEnd={e => {
          e.inputEvent.__STOP__ = true
          mouseDown.value = false
        }}
        onSelectEnd={e => {
          const { isDragStart, isClick, added, removed, inputEvent } = e
          const moveable = moveableRef.value
          const selectedLayers = selectedLayersStore.value
          mouseDown.value = true

          if (isDragStart) {
            inputEvent.preventDefault()

            moveable?.waitToChangeTarget().then(() => {
              if (!mouseDown.value) {
                return
              }
              moveable?.dragStart(inputEvent)
            })
          }
          const layers = editor.layers
          const targets = layers.toTargetList(selectedLayers).targets()
          if (!targets) {
            return
          }

          let nextTargetList: TargetList | undefined
          if (isDragStart || isClick) {
            if (metaStore.value) {
              nextTargetList = layers.selectSingleChilds(targets, added, removed)
            } else {
              nextTargetList = layers.selectCompletedChilds(
                targets,
                added,
                removed,
                shiftStore.value
              )
            }
          } else {
            nextTargetList = layers.selectSameDepthChilds(targets, added, removed)
          }
          editorRef.value?.setSelectedLayers(
            (nextTargetList && layers.toLayerGroups(nextTargetList)) || []
          )
        }}
      />
    )
  }
)
