import { useBizMain, useRightMenu } from '@/renderer/hooks'
import { useStoreStateValue } from '@/renderer/store'
import { computed, h, HTMLAttributes, onMounted, ref } from 'vue'
import { ComponentRef } from '../../base'
import { DATA_SCENA_ELEMENT_ID } from '../consts'
import { Editor } from '../states'
import { $currentScena, $editor, $layers, $moveable, $selectedLayers, $zoom } from '../stores'
import {
  ScenaElementLayer,
  ScenaElementLayerGroup,
  ViewFrameProperty,
  ViewInvoke,
  ViewRef
} from '../types'
import { prefix } from '../utils'

export const ViewNode = ComponentRef<
  ViewInvoke,
  Partial<HTMLAttributes> & { ItemType: any; view: ViewRef<ViewInvoke> }
>('ViewNode', (props, viewRef, { slots }) => {
  const { ItemType, view, ...others } = props
  onMounted(() => {
    view.value = viewRef.value!
  })
  return () => <ItemType ref={viewRef} {...others} />
})

export interface ViewLayerProps {
  layer: ScenaElementLayer
}

class LayerInvoker implements ViewInvoke<ViewFrameProperty> {
  constructor(private readonly layer: ScenaElementLayer) {}

  refresh(data: Partial<ViewFrameProperty>) {
    const layers = Editor.impl.layers
    const { position = 'absolute', left = 0, top = 0, width = 0, height = 0 } = data
    this.layer.item.clear()

    layers.setCSS(
      this.layer,
      {
        position,
        left: `${left}px`,
        top: `${top}px`,
        width: `${width}px`,
        height: `${height}px`,
        overflow: 'hidden'
      },
      true
    )

    const el = this.layer.root.value
    el && (el.style.cssText = layers.compositeFrame(this.layer).toCSSText())

    return true
  }
}

export const ViewLayer = ComponentRef<SVGElement | HTMLElement, ViewLayerProps>(
  'ViewLayer',
  (props, viewRef, { slots }) => {
    onMounted(() => {
      const layer = props.layer
      if (!layer) {
        return
      }
      layer.root.value = viewRef.value || null
      layer.frame.value = new LayerInvoker(layer)
      layer.frame.value.refresh(layer.meta)
    })

    const render = () => {
      const layer = props.layer
      if (!layer) return <></>
      const jsx = layer.jsx as any
      const jsxProps: Record<string, any> = {
        ...jsx.props,
        key: layer.id,
        [DATA_SCENA_ELEMENT_ID]: layer.id,
        ref: viewRef
      }
      const childrens = layer.slots.map(layer => {
        const jsxProps: Record<string, any> = {
          key: layer.id,
          [DATA_SCENA_ELEMENT_ID]: layer.id,
          forProps: { layer }
        }
        return h(<ViewLayer />, jsxProps)
      })
      return h(jsx, jsxProps, ...childrens)
    }

    return () => <>{render()}</>
  }
)

export const Viewport = ComponentRef<
  HTMLDivElement,
  Partial<HTMLAttributes> & { onBlur?: (...args: any) => any }
>('Viewport', (props, viewRef, { slots }) => {
  const divRef = ref<HTMLDivElement | null>(null)
  const editor = Editor.impl
  const currentScena = useStoreStateValue($currentScena)
  const zoom = useStoreStateValue($zoom)
  const layers = useStoreStateValue($layers)
  const selectedLayers = useStoreStateValue($selectedLayers)
  const moveableRef = useStoreStateValue($moveable)
  const editorRef = useStoreStateValue($editor)
  const mainPtr = useBizMain()

  const backgroundColor = computed(() => {
    const colors = mainPtr.value?.colors
    return colors?.forValue(currentScena.value?.background || '')
  })

  const menuPtr = useRightMenu(
    [
      { key: 'bringForward', label: '   Bring Forward   ' }, // 上一层
      { key: 'sendBackward', label: '   Send Backward   ' }, // 下一层
      { key: 'bringToFront', label: '   Bring to Front  ' }, // 置顶
      { key: 'sendToBack', label: '   Send to Back    ' } // 置底
    ],
    async (key, list: Array<ScenaElementLayerGroup | ScenaElementLayer>) => {
      switch (key) {
        case 'bringForward':
          list.forEach(layer => {
            const idx = layers.value.findIndex(t => t.id === layer.id)
            const length = layers.value.length
            if (idx >= 0 && idx < length - 1) {
              const layer = layers.value[idx]
              const toIdx = idx + 1
              layers.value?.splice(idx, 1)
              layers.value?.splice(toIdx, 0, layer)

              editorRef.value?.setLayers([...layers.value])
            }
          })
          break
        case 'sendBackward':
          list.forEach(layer => {
            const idx = layers.value.findIndex(t => t.id === layer.id)
            if (idx > 0) {
              const layer = layers.value[idx]
              const toIdx = idx - 1
              layers.value?.splice(idx, 1)
              layers.value?.splice(toIdx, 0, layer)
              editorRef.value?.setLayers([...layers.value])
            }
          })
          break
        case 'bringToFront':
          list.forEach(layer => {
            const idx = layers.value.findIndex(t => t.id === layer.id)
            const length = layers.value.length
            if (idx >= 0 && idx < length - 1) {
              const layer = layers.value[idx]
              layers.value?.splice(idx, 1)
              layers.value?.push(layer)
              editorRef.value?.setLayers([...layers.value])
            }
          })
          break
        case 'sendToBack':
          list.forEach(layer => {
            const idx = layers.value.findIndex(t => t.id === layer.id)
            if (idx > 0) {
              const layer = layers.value[idx]
              layers.value?.splice(idx, 1)
              layers.value?.unshift(layer)
              editorRef.value?.setLayers([...layers.value])
            }
          })
          break
        default:
          break
      }
    }
  )

  const handleContextMenu = (e: MouseEvent) => {
    if (selectedLayers.value.length === 0 || !viewRef.value || !moveableRef.value) {
      e.preventDefault
      return
    }

    const { left: toLeft, top: toTop } = viewRef.value.getBoundingClientRect()
    const pageLeft = e.pageX - toLeft
    const pageTop = e.pageY - toTop

    const { left, top, width, height } = moveableRef.value.getRect()
    if (pageLeft < left || pageLeft > left + width || pageTop < top || pageTop > top + height) {
      e.preventDefault()
      return
    }

    const layers = selectedLayers.value
    menuPtr.show(e.clientX, e.clientY, layers)
  }

  return () => (
    <div
      class={prefix('viewport-container')}
      onContextmenu={handleContextMenu}
      style={{
        width: `${currentScena.value?.width || 0}px`,
        height: `${currentScena.value?.height || 0}px`,
        backgroundColor: backgroundColor.value
      }}>
      {slots.default?.() || <></>}
      <div
        class={prefix('viewport')}
        {...{ [DATA_SCENA_ELEMENT_ID]: 'viewport' }}
        onBlur={props.onBlur}
        ref={viewRef}>
        {layers.value.map(layer => {
          return <ViewLayer key={layer.id} forProps={{ layer }} />
        })}
      </div>
    </div>
  )
})
