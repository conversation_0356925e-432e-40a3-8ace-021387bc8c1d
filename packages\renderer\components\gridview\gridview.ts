import { CSSProperties, ExtractPropTypes } from 'vue'
import { buildProps, definePropType } from '@wuk/wui/dist/utils'
import { ActionKey, AppMenuItem } from '@wuk/cfg'
import { type ColDef } from 'ag-grid-community'

export type GridIdType = number | string
export type GridColumnRule = ColDef

export enum GridRowOpTag {
  AddNew = 'addNew'
}

export interface GridRowData<T = any> extends Record<string, any> {
  readonly key?: string | number
  readonly dataIndex: number
  readonly meta: T
  readonly opTag?: GridRowOpTag
}

export const kDefaultMenus: AppMenuItem[] = [
  { key: 'add_new_key', label: '  Add New Row     ' },
  { key: 'insert_new_key', label: '  Insert New Row  ' },
  { key: 'modify_new_key', label: '  modify The Row  ' },
  { key: 'delete_new_key', label: '  Delete The Row  ' }
]

export const gridViewProps = buildProps({
  className: {
    type: String,
    default: ''
  },
  options: {
    type: definePropType<GridColumnRule[]>(Array),
    default: () => []
  },
  list: {
    type: definePropType<GridRowData[]>(Array),
    default: () => []
  },
  menus: {
    type: definePropType<AppMenuItem[]>(Array),
    default: () => kDefaultMenus
  },
  border: Boolean,
  styles: {
    type: definePropType<CSSProperties | string>([Object, String]),
    default: () => {}
  },
  headerCellStyle: {
    type: definePropType<CSSProperties>(Object),
    default: () => {}
  }
} as const)
export type GridViewProps = ExtractPropTypes<typeof gridViewProps>

export const gridViewEmits = {
  rightMenuClicked: (key: ActionKey, rowIndex: number, row: GridRowData, column: GridColumnRule) =>
    undefined,
  valueChanged: (rowIndex: number, row: GridRowData, column: GridColumnRule, newValue: any) =>
    undefined,
  rowChanged: (rowIndex: number, row: GridRowData) => undefined,
  rowAdded: (rowIndex: number, row: GridRowData) => undefined
}
export type GridViewEmits = typeof gridViewEmits
