<template>
  <ag-grid-vue
    :class="styles.grid"
    class="ag-theme-quartz"
    :style="{ height: '100%' }"
    :theme="'legacy'"
    :grid-options="gridOptions"
    :get-row-id="getRowId"
    :on-grid-ready="onGridReady"
    :row-data="rowData"
    edit-type="fullRow"
    :column-defs="props.options"
    :default-col-def="defaultColDef"
    :suppress-agg-func-in-header="true"
    :stop-editing-when-cells-lose-focus="true"
    @row-value-changed="handleRowValueChanged"
    @cell-value-changed="handleCellValueChanged"
    @cell-editing-stopped="handleCellEditingStopped"
  />
</template>

<script lang="ts" setup>
import {
  computed,
  onMounted,
  defineProps,
  defineEmits,
  defineExpose,
  ref,
  watch,
  nextTick,
  shallowRef
} from 'vue'
import { AgGridVue } from 'ag-grid-vue3'
import {
  themeQuartz,
  AllCommunityModule,
  ClientSideRowModelModule,
  type ColDef,
  type GetRowIdParams,
  ModuleRegistry,
  type GridOptions,
  ICellRendererParams,
  RowValueChangedEvent,
  GridApi,
  CellEditingStoppedEvent,
  RowPinnedType
} from 'ag-grid-community'

import { gridViewProps, gridViewEmits, GridRowData, GridRowOpTag } from './gridview'
import { useRightMenu } from '@/renderer/hooks'
import styles from './index.module.scss'

const COMPONENT_NAME = 'GridView'
defineOptions({
  name: COMPONENT_NAME
})

const myTheme = themeQuartz.withParams({
  backgroundColor: '#1f2836',
  browserColorScheme: 'dark',
  chromeBackgroundColor: {
    ref: 'foregroundColor',
    mix: 0.07,
    onto: 'backgroundColor'
  },
  foregroundColor: '#FFF',
  headerFontSize: 14
})

ModuleRegistry.registerModules([AllCommunityModule, ClientSideRowModelModule])

const gridOptions: GridOptions = {
  theme: myTheme,
  asyncTransactionWaitMillis: 50
}

function getRowId(params: GetRowIdParams) {
  const { key, dataIndex, opTag } = params.data
  return `${opTag || 'data'}_${key || dataIndex}`
}

const props = defineProps(gridViewProps)
const emit = defineEmits(gridViewEmits)

const menuPtr = useRightMenu(props.menus || [], (key, rowIndex, row, column) => {
  emit('rightMenuClicked', key, rowIndex, row, column)
})
const rowData = ref<GridRowData[]>([])
watch(
  () => props.list,
  newList => {
    rowData.value = newList.map((item, index) => {
      const { opTag, ...rest } = item
      return {
        ...rest,
        ...item.meta
      }
    })
  }
)

const handleContextMenu = (
  event: MouseEvent,
  data: ICellRendererParams,
  rowIndex: number | null
) => {
  event.preventDefault()
  menuPtr.show(event.clientX, event.clientY, rowIndex, data.data, data.colDef)
}
const cellRenderer = (params: ICellRendererParams) => {
  const div = document.createElement('div')
  div.oncontextmenu = (e: MouseEvent) => handleContextMenu(e, params, params.node.rowIndex)
  div.style.pointerEvents = 'auto'
  div.style.width = '100%'
  div.style.height = '100%'
  div.innerHTML = params.value
  return div
}

const defaultColDef: ColDef = {
  flex: 1,
  editable: true,
  // singleClickEdit: true,
  // filter: false,
  // sortable: false,
  cellRenderer
}

const gridApi = shallowRef<GridApi | null>(null)
function onGridReady(params: any) {
  gridApi.value = params.api
  const columnApi = params.columnApi
}

const startEditingCell = (rowIndex: number, colKey: string, rowPinned?: RowPinnedType) => {
  gridApi.value?.startEditingCell({
    rowIndex,
    colKey,
    rowPinned
  })
}

const scrollToRow = (rowIndex: number) => {
  gridApi.value?.ensureIndexVisible(rowIndex, 'top')
}

const setFocusedCell = (rowIndex: number, colKey: string) => {
  gridApi.value?.setFocusedCell(rowIndex, colKey)
}

const startEditingRow = (rowIndex: number) => {
  // Get all editable columns
  const editableColumns = props.options.filter(col => col.editable !== false)
  if (editableColumns.length === 0) return
  editableColumns.forEach(col => {
    gridApi.value?.startEditingCell({
      rowIndex,
      colKey: col.field!
    })
  })
}

const applyTransactionAsync = async <T>(
  { addIndex, add, update, remove }: { addIndex?: number; add?: T[]; update?: T[]; remove?: T[] },
  callback?: (res: { addIndex?: number; add?: T[]; update?: T[]; remove?: T[] }) => void
) => {
  return gridApi.value?.applyTransactionAsync({ addIndex, add, update, remove }, res => {
    callback?.({
      addIndex,
      add: (res.add?.length && add) || undefined,
      update: (res.update?.length && update) || undefined,
      remove: (res.remove?.length && remove) || undefined
    })
  })
}

const applyTransaction = async <T>({
  addIndex,
  add,
  update,
  remove
}: {
  addIndex?: number
  add?: T[]
  update?: T[]
  remove?: T[]
}) => {
  return gridApi.value?.applyTransaction({ addIndex, add, update, remove })
}

const addNewRow = async <T>(meta: T, addIndex: number) => {
  let rowIndex = addIndex - 1
  if (rowIndex < 0) rowIndex = 0
  if (rowIndex >= rowData.value.length) rowIndex = rowData.value.length - 1
  scrollToRow(rowIndex)
  const add = [
    {
      meta,
      ...meta,
      dataIndex: addIndex,
      opTag: GridRowOpTag.AddNew
    }
  ]
  applyTransaction({ addIndex, add })
  setTimeout(() => {
    const colKeys = props.options.filter(col => col.editable !== false).map(col => col.field!)
    const colKey = colKeys[0] || 'dataIndex'
    startEditingCell(addIndex, colKey)
  }, 100)
}

// Expose grid API methods
defineExpose({
  startEditingCell,
  startEditingRow,
  addNewRow,
  scrollToRow,
  setFocusedCell,
  applyTransactionAsync,
  applyTransaction
})

const handleCellValueChanged = (params: any) => {
  const { data, colDef, newValue, rowIndex } = params
  emit('valueChanged', rowIndex, data, colDef, newValue)
}

const handleRowValueChanged = (event: RowValueChangedEvent) => {
  const { data, rowIndex } = event
  const { opTag, dataIndex, meta, ...rest } = data
  Object.assign(meta, { ...rest })
  if (opTag === GridRowOpTag.AddNew) {
    emit('rowAdded', rowIndex || dataIndex, { ...rest, dataIndex, meta })
  } else {
    emit('rowChanged', rowIndex || dataIndex, { ...rest, dataIndex, meta })
  }
}

const handleCellEditingStopped = (event: CellEditingStoppedEvent) => {
  const { data, valueChanged } = event
  if (data.opTag === GridRowOpTag.AddNew) {
    applyTransaction({
      remove: [data]
    })
  }
}

onMounted(() => {
  menuPtr.init(props.menus || [])
})
</script>
<style>
@import 'ag-grid-community/styles/ag-grid.css';
@import 'ag-grid-community/styles/ag-theme-quartz.css';

@media screen and (max-width: 720px) {
  div.ag-theme-quartz,
  div.ag-theme-quartz-dark {
    --ag-font-size: 12px;
    --ag-grid-size: 6px;
  }
}

.ag-theme-quartz .ag-row-group,
.ag-theme-quartz .ag-value-change-value-highlight,
.ag-theme-quartz-dark .ag-value-change-value-highlight {
  padding-left: 6px;
  padding-right: 6px;
  padding-top: 2px;
  padding-bottom: 2px;
  border-radius: 12px;
  margin-left: 4px;
}

.ag-right-aligned-cell {
  font-variant-numeric: tabular-nums;
  letter-spacing: -0.01em;
}

.ticker-name {
  opacity: 0.8;
}
</style>
