import { defineComponent } from 'vue'
import styles from './index.module.scss'

import { TitleBar, StatusBar } from '..'

export default defineComponent({
  name: 'EngineLayout',
  setup(props, { slots }) {
    return () => (
      <div class={styles.layouts}>
        <TitleBar class={styles.layouts_header}>{slots.header?.()}</TitleBar>
        {slots.default?.()}
        <StatusBar class={styles.layouts_footer}>{slots.footer?.()}</StatusBar>
      </div>
    )
  }
})
