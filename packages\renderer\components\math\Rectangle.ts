import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Rect } from './Shape'

export default class Rectangle extends Shape<Rect> {
  protected _width: number
  protected _height: number

  constructor(x?: number, y?: number, width?: number, height?: number) {
    super(SHAPES.RECT, x, y)
    this._width = width || 0
    this._height = height || 0
  }

  get left(): number {
    return this._x
  }

  get right(): number {
    return this._x + this._width
  }

  get top(): number {
    return this._y
  }

  get bottom(): number {
    return this._y + this._height
  }

  get width(): number {
    return this._width
  }

  set width(val: number) {
    this._width = val
  }

  get height(): number {
    return this._height
  }

  set height(val: number) {
    this._height = val
  }

  static get EMPTY(): Rectangle {
    return new Rectangle(0, 0, 0, 0)
  }

  get empty(): boolean {
    return this._width === 0 || this._height === 0
  }

  get bounds(): Rect {
    return { x: this._x, y: this._y, w: this._width, h: this._height }
  }

  clone(): Rectangle {
    return new Rectangle(this._x, this._y, this._width, this._height)
  }

  copy(info: Rectangle): this {
    super.copy(info)
    this._width = info.width
    this._height = info.height

    return this
  }

  assign(data: Rect): boolean {
    let result = super.assign(data)

    if (this._width !== data.w || this._height !== data.h) {
      this._width = data.w
      this._height = data.h

      result = true
    }

    return result
  }

  contains(x: number, y: number): boolean {
    if (this._width <= 0 || this._height <= 0) {
      return false
    }

    if (x >= this._x && x < this._x + this._width) {
      if (y >= this._y && y < this._y + this._height) {
        return true
      }
    }

    return false
  }

  pad(paddingX: number, paddingY: number): this {
    paddingX = paddingX || 0
    paddingY = paddingY || (paddingY !== 0 ? paddingX : 0)

    this._x -= paddingX
    this._y -= paddingY

    this._width += paddingX * 2
    this._height += paddingY * 2

    return this
  }

  fit(rect: Rectangle | { x: number; y: number; width: number; height: number }): this {
    if (this._x < rect.x) {
      this._width += this._x
      if (this._width < 0) {
        this._width = 0
      }

      this._x = rect.x
    }

    if (this._y < rect.y) {
      this._height += this._y
      if (this._height < 0) {
        this._height = 0
      }
      this._y = rect.y
    }

    if (this._x + this._width > rect.x + rect.width) {
      this._width = rect.width - this._x
      if (this._width < 0) {
        this._width = 0
      }
    }

    if (this._y + this._height > rect.y + rect.height) {
      this._height = rect.height - this._y
      if (this._height < 0) {
        this._height = 0
      }
    }

    return this
  }

  enlarge(rect: Rectangle | { x: number; y: number; width: number; height: number }): this {
    const x1 = Math.min(this._x, rect.x)
    const x2 = Math.max(this._x + this._width, rect.x + rect.width)
    const y1 = Math.min(this._y, rect.y)
    const y2 = Math.max(this._y + this._height, rect.y + rect.height)

    this._x = x1
    this._width = x2 - x1
    this._y = y1
    this._height = y2 - y1

    return this
  }

  equal(value: Rectangle): boolean {
    return super.equal(value) && value.width === this._width && value.height === this._height
  }
}
