export enum SHAPES {
  SHAPE = -1,
  POLY = 0,
  RECT = 1,
  CIRC = 2,
  ELIP = 3,
  RREC = 4
}

export type Kind =
  | SHAPES.SHAPE
  | SHAPES.POLY
  | SHAPES.RECT
  | SHAPES.CIRC
  | SHAPES.ELIP
  | SHAPES.RREC

export interface Point {
  x: number
  y: number
}

export interface Rect extends Point {
  w: number
  h: number
}

export class Shape<T extends Point = Point> {
  protected _x: number
  protected _y: number
  protected _kind: Kind
  constructor(k?: Kind, x?: number, y?: number) {
    this._x = x || 0
    this._y = y || 0
    this._kind = k || SHAPES.SHAPE
  }

  get empty(): boolean {
    return true
  }

  get x(): number {
    return this._x
  }

  get y(): number {
    return this._y
  }

  get kind(): Kind {
    return this._kind
  }

  get bounds(): Rect {
    return { x: 0, y: 0, w: 0, h: 0 }
  }

  clone(): Shape {
    return new Shape(this.x, this.y)
  }

  assign(data: T): boolean {
    let result = false
    if (this._x !== data.x || this._y !== data.y) {
      this._x = data.x
      this._y = data.y

      result = true
    }

    return result
  }

  copy(info: Shape): this {
    this._x = info.x
    this._y = info.y
    return this
  }

  contains(x: number, y: number): boolean {
    return this.x === x && this.y === y
  }

  equal(value: Shape): boolean {
    return value && value.kind === this.kind && value.x === this.x && value.y === this.y
  }
}
