import { CSSProperties, ExtractPropTypes } from 'vue'
import { buildProps, definePropType } from '@wuk/wui/dist/utils'
import { ActionKey, AppMenuItem } from '@wuk/cfg'
import { TableColumnCtx } from '@wuk/wui'

export type TableIdType = number | string
export interface TableColumnRule<T extends Record<string, any> = Record<string, any>>
  extends Record<string, any> {
  prop: string
  label: string
  sortable?: boolean
  width?: number | string
  minWidth?: number | string
  resizable?: boolean
  fixed?: boolean | string
  align?: string
  tooltip?: boolean
  filter?: boolean
}

export interface TableRowData<T = any> extends Record<string, any> {
  id?: TableIdType
  pid?: TableIdType
  index: number
  meta: T
  rowSpanCount?: number
}

export interface TableViewData<T extends TableRowData = TableRowData> {
  list: T[]
}

export interface TableViewOptions<T extends TableRowData = TableRowData> {
  spanColumnIndexs?: number[]
  columns: TableColumnRule<T>[]
}

export interface SpanMethodProps<T extends TableRowData = TableRowData> {
  row: T
  column: TableColumnCtx<T>
  rowIndex: number
  columnIndex: number
}

export const tableViewProps = buildProps({
  className: {
    type: String,
    default: ''
  },
  options: {
    type: definePropType<TableViewOptions>(Object),
    default: () => []
  },
  data: {
    type: definePropType<TableViewData>(Object),
    default: () => []
  },
  menus: {
    type: definePropType<AppMenuItem[]>(Array),
    default: () => []
  },
  border: Boolean,
  styles: {
    type: definePropType<CSSProperties | string>([Object, String]),
    default: () => {}
  },
  headerCellStyle: {
    type: definePropType<CSSProperties>(Object),
    default: () => {}
  }
} as const)
export type TableViewProps = ExtractPropTypes<typeof tableViewProps>

export const tableViewEmits = {
  rightMenuClicked: (key: ActionKey, row: TableRowData, column: TableColumnRule, event: Event) =>
    undefined,
  valueChanged: (key: ActionKey, row: TableRowData, column: TableColumnRule, event: Event) =>
    undefined
}
export type TableViewEmits = typeof tableViewEmits
