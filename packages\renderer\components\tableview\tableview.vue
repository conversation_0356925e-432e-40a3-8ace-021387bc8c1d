<template>
  <wui-table
    :class="styles.tableview"
    :data="data"
    :style="props.styles"
    :border="props.border"
    :span-method="handleSpanMethod"
    @row-contextmenu="handleRowContextmenu"
  >
    <wui-table-column
      v-for="item in columns"
      :key="item.prop"
      :prop="item.prop"
      :label="item.label"
      :width="item.width"
      :sortable="item.sortable"
      :resizable="item.resizable"
      :min-width="item.minWidth"
      :fixed="item.fixed"
      :align="item.align"
      :show-overflow-tooltip="item.tooltip"
    >
      <template v-if="$slots[item.prop]" #default="{ row, $index }">
        <slot :name="item.prop" :data="{ row, $index }" />
      </template>
    </wui-table-column>
    <template #empty>
      <slot name="empty" />
    </template>
  </wui-table>
</template>

<script lang="ts" setup>
import { computed, onMounted, reactive, ref, defineProps, defineEmits } from 'vue'
import {
  tableViewProps,
  tableViewEmits,
  TableRowData,
  TableColumnRule,
  SpanMethodProps
} from './tableview'
import { useRightMenu } from '@/renderer/hooks'
import styles from './index.module.scss'

const COMPONENT_NAME = 'TableView'
defineOptions({
  name: COMPONENT_NAME
})

const props = defineProps(tableViewProps)
const origin = ref<TableRowData[]>(props.data.list || [])
const list = reactive<{
  span: Record<string, { rowspan: number; colspan: number }>
  data: TableRowData[]
}>({
  span: {},
  data: []
})

const emit = defineEmits(tableViewEmits)

const data = computed(() => {
  const result: TableRowData[] = []
  const records: Record<string, TableRowData[]> = {}
  props.data.list.forEach(item => {
    const key = item.pid !== undefined ? item.pid : item.id
    if (key !== undefined) {
      const arr = records[key] || (records[key] = [])
      arr.push(item)
    } else {
      result.push(item)
    }
  })
  const keys = Object.keys(records)
  if (keys.length) {
    keys.sort((item1, item2) => (item1 < item2 ? -1 : item1 === item2 ? 0 : 1))
    keys.forEach(id => {
      const list = records[id] || []
      list.forEach(item => (item.rowSpanCount = list.length))
      result.push(...list)
    })
  }

  return result
})
const columns = computed(() => props.options.columns || [])

const menuPtr = useRightMenu(props.menus || [], (key, row, column, event) => {
  emit('rightMenuClicked', key, row, column, event)
})

const update = (list: TableRowData[]) => {
  origin.value.splice(0, origin.value.length, ...list)
}

onMounted(() => {
  menuPtr.init(props.menus || [])
})

const handleSpanMethod = ({ row, column, rowIndex, columnIndex }: SpanMethodProps) => {
  const spanColumnIndexs = props.options.spanColumnIndexs || []
  if (spanColumnIndexs.indexOf(columnIndex) > -1 && row.rowSpanCount) {
    const rowspan = (row.pid === row.id && row.rowSpanCount) || 0
    const colspan = (row.pid === row.id && 1) || 0
    return {
      rowspan,
      colspan
    }
  }
  return undefined
}
const handleRowContextmenu = (row: TableRowData, column: TableColumnRule, event: MouseEvent) => {
  event.preventDefault()
  menuPtr.show(event.clientX, event.clientY, row, column, event)
}

// expose({ update })
</script>
