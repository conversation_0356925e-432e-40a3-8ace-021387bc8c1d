@import '../../assets/variables';

.tabview {
  display: flex;
  flex-shrink: 0;
  padding: 20px 20px 0;

  &_tab {
    width: 100%;
    height: 41px;
    display: flex;
    flex-shrink: 0;
    background-color: $app-bar-color;

    &_box {
      width: calc(100% - 360px);
      display: flex;
      flex-shrink: 0;
      overflow-x: hidden;
      white-space: nowrap;
      transition: overflow-y 0.3s ease;

      &:hover {
        overflow-x: auto;
      }
      &::-webkit-scrollbar {
        height: 5px;
      }
      &::-webkit-scrollbar-track {
        background-color: #e0e3e8;
        border-radius: 10px;
      }
      &::-webkit-scrollbar-thumb {
        background-color: #dee0e2;
        border-radius: 10px;
      }
      &::-webkit-scrollbar-thumb:hover {
        cursor: pointer;
        background-color: #cccccc;
      }

      &_item,
      &_curr {
        display: flex;
        min-width: 215px;
        padding: 0px 10px;
        cursor: pointer;
        align-items: center;
        gap: 10px;
        background-image: url('./image/rectangle.png');
        background-size: 100% 100%;

        &_title {
          width: 65%;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          padding-left: 5px;
          font-size: 14px;
          font-weight: normal;
        }
      }

      &_curr {
        background-color: #e0e3e8;
      }

      &_close {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 20px;
        height: 20px;
        flex-shrink: 0;
        border-radius: 4px;
        z-index: 1;
      }
    }
  }

  &_search {
    width: 360px;
    position: relative;
    display: flex;
    align-items: center;
    margin-left: 10px;

    &_image {
      width: 30px;
      height: 28px;
      position: absolute;
      right: 10px;
      background-color: #ffffff;
      border-radius: 0 4px 4px 0;
      display: flex;
      align-items: center;

      img {
        width: 25px;
        height: 25px;
      }
    }
  }
  &_search :global(.wui-input) {
    height: 28px;
  }
  &_search :global(.wui-input__wrapper) {
    border-radius: 4px 0 0 4px;
    box-shadow: none;
  }

  &_content {
    display: flex;
    flex: 1 1 auto;
    height: 0;
  }
}
