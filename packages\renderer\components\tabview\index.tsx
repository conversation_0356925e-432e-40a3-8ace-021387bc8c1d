import { computed, defineComponent, ExtractPropTypes, ref, watchEffect } from 'vue'
import styles from './index.module.scss'

import { buildProps, definePropType } from '@wuk/wui/dist/utils'
import Search from '@/renderer/assets/search.png'

export type TabItemKey = string | number
export const NONE_TAB = -1
export interface TabItemProps {
  key: TabItemKey
  icon?: string
  title: string
  fixed?: boolean
}

export const tabviewProps = buildProps({
  tabs: {
    type: definePropType<TabItemProps[]>(Array),
    default: []
  },
  index: {
    type: Number,
    default: NONE_TAB
  },
  searchKeyword: {
    type: String,
    default: ''
  }
} as const)
export type TabViewProps = ExtractPropTypes<typeof tabviewProps>

export const tabviewEmits = {
  change: (index: number, item?: TabItemProps) => undefined,
  close: (index: number, item: TabItemProps) => undefined,
  'update:searchKeyword': (key: string) => typeof key === 'string'
}
export type TabviewEmits = typeof tabviewEmits

export const tabviewInvokes = {
  change: (index: number) => undefined,
  close: (index: number) => undefined,
  add: (item: TabItemProps) => true
}
export type TabviewInvokes = typeof tabviewInvokes

const TabItem = (props: {
  selected: boolean
  item: TabItemProps
  onChange?: () => any
  onClose?: () => any
}) => {
  const handleClicked = (evt: any) => {
    props.onChange?.()
  }
  const handleClose = (evt: any) => {
    evt.stopPropagation()
    props.onClose?.()
  }
  return (
    <div
      class={[styles.tabview_tab_box_item, (props.selected && styles.tabview_tab_box_curr) || null]}
      onClick={handleClicked}>
      {(props.item?.icon && <wui-icon />) || <></>}
      <span class={styles.tabview_tab_box_item_title}>{props.item?.title}</span>
      <div class={styles.tabview_tab_box_close} onClick={handleClose}>
        <wui-icon name='Close' />
      </div>
    </div>
  )
}

export const TabView = defineComponent({
  name: 'TabView',
  props: tabviewProps,
  emits: {
    ...tabviewEmits
  },
  setup(props, ctx) {
    const { slots } = ctx || {}

    const list = computed<TabItemProps[]>(() => props.tabs || [])
    const current = ref<number>(props.index)
    const change = (idx: number) => {
      current.value = idx
      const item = list.value[idx]
      ctx.emit('change', idx, item)
    }

    const add = (item: TabItemProps) => {
      const idx = list.value.findIndex(it => it.key === item.key)
      if (idx !== -1) {
        return
      }
      list.value.push(item)
      if (current.value === NONE_TAB) {
        change(0)
      }
    }

    const close = (idx: number) => {
      const item = list.value.splice(idx, 1)
      if (!item) {
        return
      }
      let index = current.value
      if (index >= idx) {
        --index
      }

      if (index < 0 && list.value.length) {
        index = 0
      }

      change(index)

      // ctx.emit('close', index, item!)
    }
    ctx.expose({
      change,
      close,
      add
    })

    const handleItemClicked = (idx: number, item: TabItemProps) => {
      change(idx)
    }

    const handleItemClose = (idx: number, item: TabItemProps) => {
      close(idx)
    }

    const searchInput = ref('')

    watchEffect(() => {
      searchInput.value = props.searchKeyword
    })
    // 添加input事件处理函数
    const handleSearchInput = (value: string) => {
      searchInput.value = value
      ctx.emit('update:searchKeyword', value) // 发射update:searchKeyword事件
    }

    return () => (
      <div class={styles.tabview}>
        {(list.value.length && (
          <div class={styles.tabview_tab}>
            <div class={styles.tabview_tab_box}>
              {list.value.map((item, idx) => (
                <TabItem
                  key={idx}
                  onChange={() => handleItemClicked(idx, item)}
                  onClose={() => handleItemClose(idx, item)}
                  selected={idx === current.value}
                  item={item}
                />
              ))}
            </div>
            <div class={styles.tabview_search}>
              <wui-input
                v-model={searchInput.value}
                placeholder='Search Setting'
                style='width: 310px'
                onChange={handleSearchInput}
              />
              <div class={styles.tabview_search_image}>
                <img src={Search} alt='' />
              </div>
            </div>
          </div>
        )) || <></>}
        <div class={styles.tabview_content}>{slots?.default?.() || <></>}</div>
      </div>
    )
  }
})
