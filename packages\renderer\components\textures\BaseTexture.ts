import {
  getUrlFileExtension,
  decomposeDataUri,
  getSvgSize,
  getResolutionOfUrl,
  BaseTextureCache,
  TextureCache,
  determineCrossOrigin,
  DataUriType
} from '../../utils'

import { UBase, uuid } from '@wuk/cfg'

/**
 * A texture stores the information that represents an image. All textures have a base texture.
 *
 * @class
 * @extends EventEmitter
 */
export default class BaseTexture extends UBase {
  private _width: number
  private _height: number
  private _realWidth: number
  private _realHeight: number
  private _hasLoaded: boolean
  private _isLoading: boolean
  private _source: HTMLImageElement | HTMLCanvasElement | null
  private _origSource: HTMLImageElement | HTMLCanvasElement | null
  private _imageType: string | null
  private _imageUrl: string | null
  private _destroyed: boolean
  private _textureCacheIds: string[]

  constructor(source: HTMLImageElement | HTMLCanvasElement) {
    super()
    this._width = 100
    this._height = 100
    this._realWidth = 100
    this._realHeight = 100
    this._hasLoaded = false
    this._isLoading = false
    this._source = null
    this._origSource = null
    this._imageType = null // set in updateImageType
    this._imageUrl = null
    this._destroyed = false
    this._textureCacheIds = []

    // if no source passed don't try to load
    if (source) {
      this.loadSource(source)
    }
  }

  get width(): number {
    return this._width
  }

  get height(): number {
    return this._height
  }

  get hasLoaded(): boolean {
    return this._hasLoaded
  }

  get origSource(): HTMLImageElement | HTMLCanvasElement | null {
    return this._origSource
  }

  get destroyed(): boolean {
    return this._destroyed
  }

  get textureCacheIds(): string[] {
    return this._textureCacheIds
  }

  get imageUrl(): string {
    return this._imageUrl || ''
  }

  set imageUrl(val: string) {
    this._imageUrl = val
  }

  get source(): HTMLImageElement | HTMLCanvasElement | null {
    return this._source
  }

  toString() {
    return `{uuid:${this.uuid},hasLoaded:${this._hasLoaded}}`
  }
  /**
   * Updates the texture on all the webgl renderers, this also assumes the src has changed.
   */
  update(): void {
    // Svg size is handled during load
    if (this._imageType !== 'svg' && this._source) {
      this._realWidth =
        this._source instanceof HTMLImageElement ? this._source.naturalWidth : this._source.width
      this._realHeight =
        this._source instanceof HTMLImageElement ? this._source.naturalHeight : this._source.height

      this._updateDimensions()
    }

    this.emit(BaseTexture.ONUPDATED, this)
  }

  private _updateDimensions() {
    this._width = this._realWidth
    this._height = this._realHeight
  }

  loadSource(source: HTMLImageElement | HTMLCanvasElement): void {
    const wasLoading = this._isLoading

    this._hasLoaded = false
    this._isLoading = false

    if (wasLoading && this._source) {
      this._source.onload = null
      this._source.onerror = null
    }

    const firstSourceLoaded = !this._source

    this._source = source
    // Apply source if loaded. Otherwise setup appropriate loading monitors.
    if (
      ((source instanceof HTMLImageElement && source.src && source.complete) ||
        (source instanceof HTMLCanvasElement && !!source.getContext)) &&
      source.width &&
      source.height
    ) {
      this._updateImageType()

      if (this._imageType === 'svg') {
        this._loadSvgSource()
      } else {
        this._sourceLoaded()
      }

      if (firstSourceLoaded) {
        // send loaded event if previous source was null and we have been passed a pre-loaded IMG element
        this.emit(BaseTexture.ONLOADED, this)
      }
    } else if (source instanceof HTMLImageElement) {
      // Image fail / not ready
      this._isLoading = true
      source.onload = () => {
        this._updateImageType()
        source.onload = null
        source.onerror = null

        if (!this._isLoading) {
          return
        }

        this._isLoading = false
        this._sourceLoaded()

        if (this._imageType === 'svg') {
          this._loadSvgSource()
          return
        }

        this.emit(BaseTexture.ONLOADED, this)
      }

      source.onerror = () => {
        source.onload = null
        source.onerror = null

        if (!this._isLoading) {
          return
        }

        this._isLoading = false
        this.emit(BaseTexture.ONERROR, this)
      }

      // Per http://www.w3.org/TR/html5/embedded-content-0.html#the-img-element
      //   "The value of `complete` can thus change while a script is executing."
      // So complete needs to be re-checked after the callbacks have been added..
      // NOTE: complete will be true if the image has no src so best to check if the src is set.
      if (source.complete && source.src) {
        // ..and if we're complete now, no need for callbacks
        source.onload = null
        source.onerror = null

        if (this._imageType === 'svg') {
          this._loadSvgSource()

          return
        }

        this._isLoading = false

        if (source.width && source.height) {
          this._sourceLoaded()

          // If any previous subscribers possible
          if (wasLoading) {
            this.emit(BaseTexture.ONLOADED, this)
          }
        } else if (wasLoading) {
          this.emit(BaseTexture.ONERROR, this)
        }
      }
    }
  }

  private _updateImageType(): void {
    if (!this._imageUrl) {
      return
    }

    const dataUri = decomposeDataUri(this._imageUrl)
    let imageType

    if (dataUri && dataUri.mediaType === 'image' && dataUri.subType) {
      // Check for subType validity
      const firstSubType = dataUri.subType.split('+')[0]

      imageType = getUrlFileExtension(`.${firstSubType}`)

      if (!imageType) {
        throw new Error('Invalid image type in data URI.')
      }
    } else {
      imageType = getUrlFileExtension(this._imageUrl)

      if (!imageType) {
        imageType = 'png'
      }
    }

    this._imageType = imageType
  }

  /**
   * Checks if `source` is an SVG image and whether it's loaded via a URL or a data URI. Then calls
   * `_loadSvgSourceUsingDataUri` or `_loadSvgSourceUsingXhr`.
   */
  private _loadSvgSource(): void {
    if (this._imageType !== 'svg') {
      // Do nothing if source is not svg
      return
    }

    const dataUri = decomposeDataUri(this._imageUrl || '')

    if (dataUri) {
      this._loadSvgSourceUsingDataUri(dataUri)
    } else {
      // We got an URL, so we need to do an XHR to check the svg size
      this._loadSvgSourceUsingXhr()
    }
  }

  /**
   * Reads an SVG string from data URI and then calls `_loadSvgSourceUsingString`.
   *
   */
  private _loadSvgSourceUsingDataUri(dataUri: DataUriType): void {
    let svgString
    if (dataUri.encoding === 'base64') {
      if (!atob) {
        throw new Error("Your browser doesn't support base64 conversions.")
      }
      svgString = atob(dataUri.data || '')
    } else {
      svgString = dataUri.data
    }

    this._loadSvgSourceUsingString(svgString || '')
  }

  /**
   * Loads an SVG string from `imageUrl` using XHR and then calls `_loadSvgSourceUsingString`.
   */
  private _loadSvgSourceUsingXhr(): void {
    if (!this._imageUrl) {
      return
    }

    const svgXhr = new XMLHttpRequest()

    // This throws error on IE, so SVG Document can't be used
    // svgXhr.responseType = 'document';

    // This is not needed since we load the svg as string (breaks IE too)
    // but overrideMimeType() can be used to force the response to be parsed as XML
    // svgXhr.overrideMimeType('image/svg+xml');

    svgXhr.onload = () => {
      if (svgXhr.readyState !== svgXhr.DONE || svgXhr.status !== 200) {
        throw new Error('Failed to load SVG using XHR.')
      }

      this._loadSvgSourceUsingString(svgXhr.response)
    }

    svgXhr.onerror = () => this.emit(BaseTexture.ONERROR, this)

    svgXhr.open('GET', this._imageUrl, true)
    svgXhr.send()
  }

  /**
   * Loads texture using an SVG string. The original SVG Image is stored as `origSource` and the
   * created canvas is the new `source`. The SVG is scaled using `sourceScale`. Called by
   * `_loadSvgSourceUsingXhr` or `_loadSvgSourceUsingDataUri`.
   *
   */
  private _loadSvgSourceUsingString(svgString: string): void {
    const svgSize = getSvgSize(svgString)

    const svgWidth = svgSize.width
    const svgHeight = svgSize.height

    if (!svgWidth || !svgHeight) {
      throw new Error(
        'The SVG image must have width and height defined (in pixels), canvas API needs them.'
      )
    }

    // Scale realWidth and realHeight
    this._realWidth = Math.round(svgWidth)
    this._realHeight = Math.round(svgHeight)

    this._updateDimensions()

    // Create a canvas element
    const canvas = document.createElement('canvas')
    if (!canvas) {
      throw new Error('canvas create error.')
    }
    canvas.width = this._realWidth
    canvas.height = this._realHeight
    canvas.accessKey = `canvas_${uuid()}`

    // Draw the Svg to the canvas
    this._source &&
      canvas
        .getContext('2d')
        ?.drawImage(
          this._source,
          0,
          0,
          svgWidth,
          svgHeight,
          0,
          0,
          this._realWidth,
          this._realHeight
        )

    // Replace the original source image with the canvas
    this._origSource = this._source
    this._source = canvas

    // Add also the canvas in cache (destroy clears by `imageUrl` and `source._uuid`)
    BaseTexture.addToCache(this, canvas.accessKey)

    this._isLoading = false
    this._sourceLoaded()

    this.emit(BaseTexture.ONLOADED, this)
  }

  /**
   * Used internally to update the width, height, and some other tracking vars once
   * a source has successfully loaded.
   */
  private _sourceLoaded(): void {
    this._hasLoaded = true
    this.update()
  }

  /**
   * Destroys this base texture
   *
   */
  destroy(): void {
    if (this._imageUrl) {
      delete TextureCache[this._imageUrl]
      this._imageUrl = null
    }

    this._source = null

    this.dispose()

    BaseTexture.removeFromCache(this)
    this._textureCacheIds = []

    this._destroyed = true
  }

  /**
   * Frees the texture from WebGL memory without destroying this texture object.
   * This means you can still use the texture later which will upload it to GPU
   * memory again.
   *
   */
  dispose(): void {
    this.emit(BaseTexture.ONDISPOSE, this)
  }

  /**
   * Changes the source image of the texture.
   * The original source must be an Image element.
   *
   */
  updateSourceImage(newSrc: string): void {
    if (!this._source) {
      return
    }
    if (this._source instanceof HTMLImageElement) {
      this._source.src = newSrc
    }

    this.loadSource(this._source)
  }

  /**
   * Helper function that creates a base texture from the given image url.
   * If the image is not in the base texture cache it will be created and loaded.
   */
  static fromImage(imageUrl: string, crossorigin?: boolean): BaseTexture {
    let baseTexture = BaseTextureCache[imageUrl]

    if (!baseTexture) {
      // new Image() breaks tex loading in some versions of Chrome.
      // See https://code.google.com/p/chromium/issues/detail?id=238071
      const image = new Image() // document.createElement('img');

      if (crossorigin === undefined && imageUrl.indexOf('data:') !== 0) {
        image.crossOrigin = determineCrossOrigin(imageUrl)
      } else if (crossorigin) {
        image.crossOrigin = typeof crossorigin === 'string' ? crossorigin : 'anonymous'
      }

      baseTexture = new BaseTexture(image)
      baseTexture.imageUrl = imageUrl

      // if there is an @2x at the end of the url we are going to assume its a highres image
      baseTexture.resolution = getResolutionOfUrl(imageUrl)

      image.src = imageUrl // Setting this triggers load

      BaseTexture.addToCache(baseTexture, imageUrl)
    }

    return baseTexture
  }

  /**
   * Helper function that creates a base texture from the given canvas element.
   *
   * @static
   * @param {HTMLCanvasElement} canvas - The canvas element source of the texture
   * @param {string} [origin='canvas'] - A string origin of who created the base texture
   */
  static fromCanvas(canvas: HTMLCanvasElement, origin?: string) {
    if (!canvas.accessKey) {
      canvas.accessKey = `${origin || 'canvas'}_${uuid()}`
    }

    let baseTexture = BaseTextureCache[canvas.accessKey]

    if (!baseTexture) {
      baseTexture = new BaseTexture(canvas)
      BaseTexture.addToCache(baseTexture, canvas.accessKey)
    }

    return baseTexture
  }

  /**
   * Helper function that creates a base texture based on the source you provide.
   * The source can be - image url, image element, canvas element. If the
   * source is an image url or an image element and not in the base texture
   * cache, it will be created and loaded.
   */
  static from(source: string | HTMLImageElement | HTMLCanvasElement): any {
    if (typeof source === 'string') {
      return BaseTexture.fromImage(source)
    } else if (source instanceof HTMLImageElement) {
      const imageUrl = source.src
      let baseTexture = BaseTextureCache[imageUrl]

      if (!baseTexture) {
        baseTexture = new BaseTexture(source)
        baseTexture.imageUrl = imageUrl
        BaseTexture.addToCache(baseTexture, imageUrl)
      }

      return baseTexture
    } else if (source instanceof HTMLCanvasElement) {
      return BaseTexture.fromCanvas(source)
    }

    // lets assume its a base texture!
    return source
  }

  /**
   * Adds a BaseTexture to the global BaseTextureCache. This cache is shared across the whole PIXI object.
   */
  static addToCache(baseTexture: BaseTexture, id: string): void {
    if (id) {
      if (baseTexture.textureCacheIds.indexOf(id) === -1) {
        baseTexture.textureCacheIds.push(id)
      }

      // @if DEBUG
      /* eslint-disable no-console */
      if (BaseTextureCache[id]) {
        console.warn(`BaseTexture added to the cache with an id [${id}] that already had an entry`)
      }
      /* eslint-enable no-console */
      // @endif

      BaseTextureCache[id] = baseTexture
    }
  }

  /**
   * Remove a BaseTexture from the global BaseTextureCache.
   *
   * @static
   */
  static removeFromCache(baseTexture: BaseTexture | string) {
    if (typeof baseTexture === 'string') {
      const baseTextureFromCache = BaseTextureCache[baseTexture]

      if (baseTextureFromCache) {
        const index = baseTextureFromCache.textureCacheIds.indexOf(baseTexture)

        if (index > -1) {
          baseTextureFromCache.textureCacheIds.splice(index, 1)
        }

        delete BaseTextureCache[baseTexture]

        return baseTextureFromCache
      }
    } else if (baseTexture && baseTexture.textureCacheIds) {
      for (let i = 0; i < baseTexture.textureCacheIds.length; ++i) {
        delete BaseTextureCache[baseTexture.textureCacheIds[i]]
      }

      baseTexture.textureCacheIds.length = 0

      return baseTexture
    }

    return null
  }

  static get ONUPDATED() {
    return 'BaseTexture.ONUPDATED'
  }
  static get ONLOADED() {
    return 'BaseTexture.ONLOADED'
  }
  static get ONERROR() {
    return 'BaseTexture.ONERROR'
  }
  static get ONDISPOSE() {
    return 'BaseTexture.ONDISPOSE'
  }
}
