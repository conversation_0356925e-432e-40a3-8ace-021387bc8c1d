import BaseTexture from './BaseTexture'
import { UBase } from '@wuk/cfg'
import { Rectangle } from '../math'
import { TextureCache, Browser } from '../../utils'

export interface ClipParam {
  clipX?: number
  clipY?: number
  splitX?: number
  splitY?: number
}

export interface GridParam {
  left?: number
  top?: number
  right?: number
  bottom?: number
  width?: number
  height?: number
}

export class Texture extends UBase {
  private _noFrame: boolean
  private _baseTexture: BaseTexture
  private _frame: Rectangle | undefined
  private _trim: Rectangle | undefined
  private _orig: Rectangle | undefined
  private _valid: boolean
  private _rotate: number
  private _updateID: number

  private _textureCacheIds: string[]
  private _clipsCaches: Record<string, any>
  private _gridCaches: Record<string, any>

  private _blobUrl: string
  private _blobRect: Rectangle | undefined

  /**
   * @param {BaseTexture} baseTexture - The base texture source to create the texture from
   * @param {Rectangle} [frame] - The rectangle frame of the texture to show
   * @param {Rectangle} [orig] - The area of original texture
   * @param {Rectangle} [trim] - Trimmed rectangle of original texture
   * @param {number} [rotate] - indicates how the texture was rotated by texture packer
   */
  constructor(
    texture: BaseTexture | Texture,
    frame?: Rectangle,
    orig?: Rectangle,
    trim?: Rectangle,
    rotate?: number
  ) {
    super()

    /**
     * Does this Texture have any frame data assigned to it?
     */
    this._noFrame = false

    if (!frame) {
      this._noFrame = true
      frame = new Rectangle(0, 0, 1, 1)
    }

    if (texture instanceof Texture) {
      this._baseTexture = texture.baseTexture || texture
    } else {
      this._baseTexture = texture
    }

    /**
     * This is the area of the BaseTexture image to actually copy to the Canvas / WebGL when rendering,
     * irrespective of the actual frame size or placement (which can be influenced by trimmed texture atlases)
     */
    this._frame = frame

    /**
     * This is the trimmed area of original texture, before it was put in atlas
     * Please call `_updateUvs()` after you change coordinates of `trim` manually.
     */
    this._trim = trim

    /**
     * This will let the renderer know if the texture is valid. If it's not then it cannot be rendered.
     */
    this._valid = false

    /**
     * This is the area of original texture, before it was put in atlas
     */
    this._orig = orig || frame // new Rectangle(0, 0, 1, 1);

    this._rotate = rotate || 0

    if (this._rotate % 2 !== 0) {
      throw new Error('attempt to use diamond-shaped UVs. If you are sure, set rotation manually')
    }

    if (this._baseTexture.hasLoaded) {
      if (this._noFrame) {
        frame = new Rectangle(0, 0, this._baseTexture.width, this._baseTexture.height)
        // if there is no frame we should monitor for any base texture changes..
        this._baseTexture.on(BaseTexture.ONUPDATED, this.onBaseTextureUpdated, this)
      }
      this.frame = frame
    } else {
      this._baseTexture.once(BaseTexture.ONLOADED, this.onBaseTextureLoaded, this)
    }

    /**
     * Fired when the texture is updated. This happens if the frame or the baseTexture is updated.
     */

    this._updateID = 0

    /**
     * The ids under which this Texture has been added to the texture cache. This is
     * automatically set as long as Texture.addToCache is used, but may not be set if a
     * Texture is added directly to the TextureCache array.
     */
    this._textureCacheIds = []

    this._clipsCaches = {}
    this._gridCaches = {}

    this._blobUrl = ''
  }

  get baseTexture(): BaseTexture {
    return this._baseTexture
  }

  get updateID(): number {
    return this._updateID
  }

  get textureCacheIds(): string[] {
    return this._textureCacheIds
  }

  get valid(): boolean {
    return this._valid
  }

  toString() {
    return `{uuid:${this.uuid},valid:${this._valid},baseTexture:${
      (this._baseTexture && this._baseTexture.toString()) || ''
    }}`
  }
  /**
   * Updates this texture on the gpu.
   *
   */
  update(): void {
    this._baseTexture.update()
  }

  /**
   * Called when the base texture is loaded
   */
  private onBaseTextureLoaded(baseTexture: BaseTexture) {
    this._updateID++

    // TODO this code looks confusing.. boo to abusing getters and setters!
    if (this._noFrame) {
      this.frame = new Rectangle(0, 0, baseTexture.width, baseTexture.height)
    } else {
      this.frame = this._frame
    }

    this._baseTexture.on(BaseTexture.ONUPDATED, this.onBaseTextureUpdated, this)

    this.emit(Texture.ONUPDATED, this)
  }

  /**
   * Called when the base texture is updated
   */
  onBaseTextureUpdated(baseTexture: BaseTexture) {
    if (!this._frame) {
      return
    }

    this._updateID++

    this._frame.width = baseTexture.width
    this._frame.height = baseTexture.height

    this.frame = this._frame

    this.emit(Texture.ONUPDATED, this)
  }

  /**
   * Destroys this texture
   */
  destroy(destroyBase?: boolean) {
    if (destroyBase) {
      // delete the texture if it exists in the texture cache..
      // this only needs to be removed if the base texture is actually destroyed too..
      if (TextureCache[this._baseTexture.imageUrl]) {
        Texture.removeFromCache(this._baseTexture.imageUrl)
      }

      this._baseTexture.destroy()
    }

    this._baseTexture.off(BaseTexture.ONUPDATED, this.onBaseTextureUpdated, this)
    this._baseTexture.off(BaseTexture.ONLOADED, this.onBaseTextureLoaded, this)

    this._frame = undefined
    this._trim = undefined
    this._orig = undefined

    this._valid = false
    Texture.removeFromCache(this)
    this._textureCacheIds = []
    this._clipsCaches = {}
    this._gridCaches = {}

    this._blobUrl && URL.revokeObjectURL(this._blobUrl)
    this._blobUrl = ''
  }

  /**
   * Creates a new texture object that acts the same as this one.
   */
  clone(): Texture {
    return new Texture(this._baseTexture, this._frame, this._orig, this._trim, this._rotate)
  }

  /**
   * Helper function that creates a Texture object from the given image url.
   * If the image is not in the texture cache it will be  created and loaded.
   */
  static fromImage(imageUrl: string, crossorigin?: boolean): Texture {
    let texture = TextureCache[imageUrl]

    if (!texture) {
      texture = new Texture(BaseTexture.fromImage(imageUrl, crossorigin))
      Texture.addToCache(texture, imageUrl)
    }

    return texture
  }

  /**
   * Helper function that creates a sprite that will contain a texture from the TextureCache based on the frameId
   * The frame ids are created when a Texture packer file has been loaded
   */
  static fromFrame(frameId: string): Texture {
    const texture = TextureCache[frameId]

    if (!texture) {
      throw new Error(`The frameId "${frameId}" does not exist in the texture cache`)
    }

    return texture
  }

  /**
   * Helper function that creates a new Texture based on the given canvas element.
   */
  static fromCanvas(canvas: HTMLCanvasElement, origin?: string): Texture {
    return new Texture(BaseTexture.fromCanvas(canvas, origin || 'canvas'))
  }

  /**
   * Helper function that creates a new Texture based on the source you provide.
   * The source can be - frame id, image url, video url, canvas element, video element, base texture
   */
  static from(source: string | HTMLImageElement | HTMLCanvasElement | BaseTexture): Texture {
    // TODO auto detect cross origin..
    // TODO pass in scale mode?
    if (typeof source === 'string') {
      const texture = TextureCache[source]

      if (!texture) {
        return Texture.fromImage(source)
      }

      return texture
    } else if (source instanceof HTMLImageElement) {
      return new Texture(BaseTexture.from(source))
    } else if (source instanceof HTMLCanvasElement) {
      return Texture.fromCanvas(source, 'HTMLCanvasElement')
    } else if (source instanceof BaseTexture) {
      return new Texture(source)
    }

    // lets assume its a texture!
    return source
  }
  /**
   * Create a texture from a source and add to the cache.
   */
  static fromLoader(
    source: HTMLImageElement | HTMLCanvasElement,
    imageUrl: string,
    name: string
  ): Texture {
    const baseTexture = new BaseTexture(source)
    const texture = new Texture(baseTexture)

    baseTexture.imageUrl = imageUrl

    // No name, use imageUrl instead
    if (!name) {
      name = imageUrl
    }

    // lets also add the frame to pixi's global cache for fromFrame and fromImage fucntions
    BaseTexture.addToCache(texture.baseTexture, name)
    Texture.addToCache(texture, name)

    // also add references by url if they are different.
    if (name !== imageUrl) {
      BaseTexture.addToCache(texture.baseTexture, imageUrl)
      Texture.addToCache(texture, imageUrl)
    }

    return texture
  }

  /**
   * Adds a Texture to the global TextureCache. This cache is shared across the whole PIXI object.
   */
  static addToCache(texture: Texture, id: string): void {
    if (id) {
      if (texture.textureCacheIds.indexOf(id) === -1) {
        texture.textureCacheIds.push(id)
      }

      // @if DEBUG
      /* eslint-disable no-console */
      if (TextureCache[id]) {
        console.warn(`Texture added to the cache with an id [${id}] that already had an entry`)
      }
      /* eslint-enable no-console */
      // @endif

      TextureCache[id] = texture
    }
  }

  /**
   * Remove a Texture from the global TextureCache.
   *
   */
  static removeFromCache(texture: string | Texture): Texture | null {
    if (typeof texture === 'string') {
      const textureFromCache = TextureCache[texture]

      if (textureFromCache) {
        const index = textureFromCache.textureCacheIds.indexOf(texture)

        if (index > -1) {
          textureFromCache.textureCacheIds.splice(index, 1)
        }

        delete TextureCache[texture]

        return textureFromCache
      }
    } else if (texture && texture.textureCacheIds) {
      for (let i = 0; i < texture.textureCacheIds.length; ++i) {
        // Check that texture matches the one being passed in before deleting it from the cache.
        if (TextureCache[texture.textureCacheIds[i]] === texture) {
          delete TextureCache[texture.textureCacheIds[i]]
        }
      }

      texture.textureCacheIds.length = 0

      return texture
    }

    return null
  }

  toGrid(grid?: GridParam): Texture | null {
    if (!this._valid) {
      return null
    }
    if (!grid) {
      return this
    }

    let { left, top, right, bottom, width, height } = grid || {}
    left = left || 0
    top = top || 0
    width = width || 0
    height = height || 0
    right = right || 0
    bottom = bottom || 0
    const key = `${left}|${top}|${right}|${bottom}|${width}|${height}|`
    let result = this._gridCaches[key] || null
    if (!result && this._baseTexture.source && this._frame) {
      const source = Browser.fromGrid(
        this._baseTexture.source,
        this._frame,
        left,
        top,
        right,
        bottom,
        width,
        height
      )
      result = Texture.from(source)
      this._gridCaches[key] = result
    }

    return result
  }

  drawSource(
    context: CanvasRenderingContext2D,
    dx: number,
    dy: number,
    dw: number,
    dh: number
  ): void {
    if (!this._frame || !this._baseTexture.source) {
      return
    }
    const { x, y, width, height } = this._frame
    context && context.drawImage(this._baseTexture.source, x, y, width, height, dx, dy, dw, dh)
  }

  drawGridSource(context: CanvasRenderingContext2D, grid: GridParam) {
    const { left, top, right, bottom, width, height } = grid || {}
    if (!context || !this._valid || !this._baseTexture.source || !this._frame) {
      return
    }
    Browser.drawGrid(
      context,
      this._baseTexture.source,
      this._frame,
      left,
      top,
      right,
      bottom,
      width,
      height
    )
  }

  toClips(clipParam?: ClipParam, noCache?: boolean): Texture[] | null {
    if (!this._valid) {
      return null
    }
    if (!clipParam) {
      return [this]
    }
    let { clipX, clipY, splitX, splitY } = clipParam || {}
    clipX = clipX || 1
    clipY = clipY || 1
    splitX = splitX || 0
    splitY = splitY || 0

    const key = clipX + '|' + clipY + '|' + splitX + '|' + splitY
    let result = this._clipsCaches[key] || null
    if (!result) {
      const splitW = splitX * (clipX - 1)
      const splitH = splitY * (clipY - 1)
      const width = Math.max((this.width - splitW) / clipX, 1)
      const height = Math.max((this.height - splitH) / clipY, 1)

      result = []
      for (let i = 0; i < clipY; i++) {
        const h = i * height + splitY * i
        for (let j = 0; j < clipX; j++) {
          const w = j * width + splitX * j
          const rect = new Rectangle(w, h, width, height)
          result.push(new Texture(this, rect))
        }
      }
      if (result && result.length > 0 && !noCache) {
        this._clipsCaches[key] = result
      }
    }

    return result
  }

  get frame(): Rectangle | undefined {
    return this._frame
  }

  set frame(frame) {
    if (!frame) {
      return
    }
    this._frame = frame
    this._noFrame = false

    const { x, y, width, height } = frame
    const xNotFit = x + width > this._baseTexture.width
    const yNotFit = y + height > this._baseTexture.height

    if (xNotFit || yNotFit) {
      const relationship = xNotFit && yNotFit ? 'and' : 'or'
      const errorX = `X: ${x} + ${width} = ${x + width} > ${this._baseTexture.width}`
      const errorY = `Y: ${y} + ${height} = ${y + height} > ${this._baseTexture.height}`

      throw new Error(
        'Texture Error: frame does not fit inside the base Texture dimensions: ' +
          `${errorX} ${relationship} ${errorY}`
      )
    }

    // this._valid = width && height && this._baseTexture.source && this._baseTexture.hasLoaded;
    this._valid = !!width && !!height && this._baseTexture.hasLoaded

    if (!this._trim && !this.rotate) {
      this._orig = frame
    }
  }

  get rotate(): number {
    return this._rotate
  }

  set rotate(val: number) {
    this._rotate = val
  }

  /**
   * The width of the Texture
   */
  get width(): number {
    return this._orig?.width || 0
  }

  /**
   * The height of the Texture
   */
  get height(): number {
    return this._orig?.height || 0
  }

  get dataURL(): string {
    const { x, y, width, height } = this._frame || {}
    return (
      (this._baseTexture.source &&
        Browser.toDataURL(this._baseTexture.source, x, y, width, height)) ||
      ''
    )
  }

  get blobURL(): string {
    if (this._frame && (!this._blobUrl || !this._blobRect || !this._blobRect.equal(this._frame))) {
      this._blobUrl && URL.revokeObjectURL(this._blobUrl)
      const blob = Browser.dataURLtoBlob(this.dataURL)
      this._blobUrl = URL.createObjectURL(blob)
      this._blobRect = this._frame.clone()
    }
    return this._blobUrl
  }

  static get ONUPDATED() {
    return 'Texture.ONUPDATED'
  }
}

export function createWhiteTexture() {
  const canvas = document.createElement('canvas')

  canvas.width = 10
  canvas.height = 10

  const context = canvas.getContext('2d')
  if (context) {
    context.fillStyle = 'white'
    context?.fillRect(0, 0, 10, 10)
  }

  return new Texture(new BaseTexture(canvas))
}

export function isTexture(x: any): x is Texture {
  return x instanceof Texture
}
