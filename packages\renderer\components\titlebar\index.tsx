import { defineComponent } from 'vue'
import styles from './index.module.scss'
import logo from '@/renderer/assets/logo.png'
import { App } from '@/renderer/boots'

export default defineComponent({
  name: 'TitleBar',
  setup(props, { slots }) {
    return () => (
      <div class={styles.navbar}>
        <div class={styles.navbar_title}>
          <div class={styles.navbar_logo}>{!App.isDarwin && <img src={logo} />}</div>
          {slots?.title?.() || <></>}
        </div>

        {slots?.default?.() || <></>}
      </div>
    )
  }
})
