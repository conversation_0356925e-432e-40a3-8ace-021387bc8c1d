import { defineComponent, ExtractPropTypes } from 'vue'
import styles from './index.module.scss'

import { buildProps } from '@wuk/wui/dist/utils'

export const toolBtnProps = buildProps({
  text: {
    type: String,
    default: ''
  },
  icon: {
    type: String,
    default: ''
  },
  iconSize: {
    type: Number,
    default: 16
  },
  image: {
    type: String,
    default: ''
  },
  imgWidth: {
    type: String,
    default: ''
  },
  imgHeight: {
    type: String,
    default: ''
  }
} as const)
export type ToolBtnProps = ExtractPropTypes<typeof toolBtnProps>

export const toolBtnEmits = {
  click: (evt: MouseEvent) => evt instanceof MouseEvent
}
export type ToolBtnEmits = typeof toolBtnEmits

export const ToolBtn = defineComponent({
  name: 'ToolBtn',
  props: toolBtnProps,
  emits: toolBtnEmits,
  setup(props, { slots, emit }) {
    const handleClick = (evt: MouseEvent) => {
      emit('click', evt)
    }
    return () => (
      <div class={styles.toolbtn} onClick={handleClick}>
        {/* {slots?.icon?.() || <wui-icon size={props.iconSize} name={props.icon} />} */}
        {slots?.image?.() || (
          <img
            src={props.image}
            style={{ width: props.imgWidth, height: props.imgHeight }}
            alt=''
          />
        )}
        {slots?.default?.() || <span>{props.text}</span>}
      </div>
    )
  }
})
