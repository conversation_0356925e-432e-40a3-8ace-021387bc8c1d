// styles 前缀
// prefix_block1_block2_block3
const _bem = (prefix: string, blocks: string[], op: '-' | '_' | '--') => {
  return blocks.reduce((acc, block) => {
    return `${acc}${op}${block}`
  }, `${prefix}`)
}
export const useBem = (prefix: string, style?: CSSModuleClasses) => {
  return {
    b: (...blocks: string[]) => {
      const cls = _bem(prefix, blocks, '-')
      return style ? style[cls] : cls
    },
    e: (...blocks: string[]) => {
      const cls = _bem(prefix, blocks, '_')
      return style ? style[cls] : cls
    },
    m: (modifier: string, ...blocks: string[]) => {
      if (blocks.length === 0) {
        const cls = `${prefix}--${modifier}`
        return style ? style[cls] : cls
      } else {
        const cls = `${_bem(prefix, blocks, '_')}--${modifier}`
        return style ? style[cls] : cls
      }
    }
  }
}
