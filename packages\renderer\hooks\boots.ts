import { Excuter, ExcuterPtr, ExNodeItr, useExcuter } from '@wuk/cfg'
import { App } from '@/renderer/boots'
import { onBeforeUnmount, onMounted, Ref, ref, watch } from 'vue'
import { LogTask } from '../boots/interface'
import { UDate } from '../utils'
import { StoreValue, useStoreState, useStoreStateValue, useStoreValue } from '../store'
import {
  $config,
  $engine,
  $invoker,
  $main,
  BizConfigs,
  BizEngine,
  BizInvoke,
  BizMain,
  clearStates
} from '../logic'

const useInnerLogic = <T extends Excuter<T>>(
  root: ExcuterPtr,
  node: ExNodeItr,
  handler?: (itr: T) => void
) => {
  const handlerRef = ref<any>()
  const nodeRef = ref<T>()
  handlerRef.value = handler

  const handleChange = () => {
    handlerRef.value?.call(handlerRef, nodeRef.value)
  }

  const useNode = () => {
    if (!root.has(node) || nodeRef.value) {
      return
    }
    nodeRef.value = useExcuter<T>(root, node)
    nodeRef.value?.on(node.READY, handleChange)
    nodeRef.value?.on(node.CHANGED, handleChange)
    nodeRef.value?.ready && handleChange()
  }
  const handleAdd = (n: ExNodeItr) => {
    if (node.key !== n.key) {
      return
    }
    useNode()
  }

  onMounted(() => {
    root.on(Excuter.ADD, handleAdd)
    useNode()
  })

  onBeforeUnmount(() => {
    nodeRef.value?.off(node.READY, handleChange)
    nodeRef.value?.off(node.CHANGED, handleChange)
    root.off(node.READY, handleAdd)
  })

  return nodeRef
}

export const useCore = <T extends Excuter<T>>(node: ExNodeItr, handler?: (itr: T) => void) =>
  useInnerLogic(App.logic.core, node, handler)

let logicInited = false
export const useLogic = (): {
  config: Ref<BizConfigs | undefined>
  engine: Ref<BizEngine | undefined>
  invoker: Ref<BizInvoke | undefined>
  main: Ref<BizMain | undefined>
} => {
  const [config, setConfig] = useStoreState($config)
  const [engine, setEngine] = useStoreState($engine)
  const [invoker, setInvoker] = useStoreState($invoker)
  const [main, setMain] = useStoreState($main)
  if (!logicInited) {
    useCore<BizConfigs>(BizConfigs, itr => setConfig(itr))
    useCore<BizInvoke>(BizInvoke, itr => setInvoker(itr))
    useCore<BizEngine>(BizEngine, itr => setEngine(itr))
    useCore<BizMain>(BizMain, itr => setMain(itr))
    logicInited = true
  }

  onBeforeUnmount(() => {
    clearStates()
  })

  return { config, engine, invoker, main }
}

export const useBizConfig = () => useStoreStateValue<BizConfigs | undefined>($config)
export const useBizEngine = () => useStoreStateValue<BizEngine | undefined>($engine)
export const useBizInvoke = () => useStoreStateValue<BizInvoke | undefined>($invoker)
export const useBizMain = () => useStoreStateValue<BizMain | undefined>($main)

export const useHandler = <T extends Excuter<T>>(
  target: Ref<T | undefined>,
  key: string,
  handler: (...args: any[]) => void
) => {
  const handleEvent = (...args: any[]) => {
    handler(...args)
  }
  watch(target, itr => {
    itr?.on(key, handleEvent)
  })

  onMounted(() => {
    target.value?.on(key, handleEvent)
  })
  onBeforeUnmount(() => {
    target.value?.off(key, handleEvent)
  })
}

export const useTimeout = (handler: (...args: any[]) => void, timeout: number, ...args: any[]) => {
  const objRef = ref<any>()
  objRef.value = Symbol.for('boots::use::timeout')

  const handlerRef = ref<any>()
  handlerRef.value = handler
  const timerRef = ref<any>()

  const start = (...args: any[]) => {
    timerRef.value = App.timer.doOnce(
      objRef.value,
      (...args: any[]) => {
        handlerRef.value?.call(handlerRef, ...args)
      },
      timeout,
      undefined,
      undefined,
      ...args
    )
  }

  const destroy = () => {
    if (timerRef.value) {
      App.timer.clearTimer(objRef.value, timerRef.value)
    }
  }

  onBeforeUnmount(() => destroy())

  return {
    start,
    destroy
  }
}

export const useInterval = (handler: (...args: any[]) => void, interval: number) => {
  const objRef = ref<any>()
  objRef.value = Symbol.for('boots::use::interval')

  const handlerRef = ref<any>()
  handlerRef.value = handler
  const timerRef = ref<any>()

  const start = (...args: any[]) => {
    timerRef.value = App.timer.doLoop(
      null,
      (...args: any[]) => {
        handlerRef.value?.call(handlerRef, ...args)
      },
      interval,
      undefined,
      undefined,
      ...args
    )
  }

  const destroy = () => {
    if (timerRef.value) {
      App.timer.clearTimer(null, timerRef.value)
    }
  }

  onBeforeUnmount(() => destroy())

  return {
    start,
    destroy
  }
}

export const useTimeCount = (
  handler: (count: number, end: boolean) => void,
  interval: number,
  count: number
) => {
  const objRef = ref<any>()
  objRef.value = Symbol.for('boots::use::interval:count')

  const handlerRef = ref<any>()
  handlerRef.value = handler
  const timerRef = ref<any>()
  const countRef = ref<number>(0)

  const start = (...args: any[]) => {
    countRef.value = 0
    timerRef.value = App.timer.doCount(
      null,
      (...args: any[]) => {
        ++countRef.value
        handlerRef.value?.call(handlerRef, countRef.value, countRef.value === count)
      },
      interval,
      count,
      undefined,
      ...args
    )
  }

  const destroy = () => {
    if (timerRef.value) {
      App.timer.clearTimer(null, timerRef.value)
    }
  }

  onBeforeUnmount(() => destroy())

  return {
    start,
    destroy
  }
}

export const useLog = (tag: string, timeout = 5 * 1000) => {
  const logRef = ref<LogTask>()
  logRef.value = logRef.value || App.log.log(`Cfg.${tag}`, tag)
  logRef.value.timeout(timeout)

  const append = (fmt: string, ...args: any[]) => {
    const now = new UDate().format('hh:mm:ss')
    fmt = `now=${now} ${fmt}`
    logRef.value && App.log.append(logRef.value, fmt, ...args)
  }

  onMounted(() => append('enter'))
  onBeforeUnmount(() => append('quit'))

  return { append }
}
