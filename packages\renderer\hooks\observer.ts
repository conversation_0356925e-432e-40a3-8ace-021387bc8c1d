import { onBeforeUnmount, onMounted, ref } from 'vue'

interface ResizeFn {
  (width: number, height: number): void
}

export const useResizer = function (fn: ResizeFn, nodeEl?: HTMLElement | null) {
  const handlerRef = ref<ResizeFn>()
  const sizeRef = ref<{ width?: number; height?: number }>({})

  handlerRef.value = fn
  let resizeObserver: ResizeObserver | undefined = undefined

  onMounted(() => {
    if (!nodeEl) return
    const { offsetWidth, offsetHeight } = nodeEl
    sizeRef.value = { width: offsetWidth, height: offsetHeight }
    handlerRef.value?.(offsetWidth, offsetHeight)

    resizeObserver = new ResizeObserver(
      (entries: ResizeObserverEntry[], observer: ResizeObserver) => {
        if (!handlerRef.value) {
          return
        }

        const entry = entries[0]
        const borderBoxSizes = entry?.borderBoxSize
        if (!borderBoxSizes?.length) {
          return
        }

        const borderBoxSize = borderBoxSizes[0]
        const width = borderBoxSize.inlineSize
        const height = borderBoxSize.blockSize
        if (sizeRef.value?.width !== width || sizeRef.value?.height !== height) {
          handlerRef.value?.(width, height)
        }
      }
    )
    resizeObserver.observe(nodeEl)
  })

  onBeforeUnmount(() => {
    handlerRef.value = undefined
    resizeObserver?.disconnect()
    resizeObserver = undefined
  })
}
