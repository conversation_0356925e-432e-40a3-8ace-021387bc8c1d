import { nextTick, ref, watch } from 'vue'
import { useParameterDialog } from '../utils/common'

/**
 * @description 基于wui-input的永久聚焦hook
 */
export const usePermanentFocus = <T extends { flag: boolean }>() => {
  const focusRowState = ref<{
    $row: T
    $prop: keyof T
  } | null>(null)
  let handleInputFocus: () => void
  let focusEqInputRef: HTMLInputElement | null = null
  const handleReset = () => {
    if (!focusEqInputRef) return false
    focusEqInputRef.parentElement?.classList.remove('is-focus')
    focusEqInputRef.removeEventListener('blur', handleInputFocus)
    focusEqInputRef.blur()
    focusEqInputRef = null
    return true
  }
  /**
   * @description
   * 实现输入框永久聚焦
   * 只存一个input实例方式
   */
  const handleInputClick = ($row: T, $prop: keyof T, event: MouseEvent) => {
    focusRowState.value = { $row, $prop }
    if (event.target === focusEqInputRef) return
    handleReset()
    focusEqInputRef = event.target as HTMLInputElement
    handleInputFocus = () => {
      focusEqInputRef?.focus()
    }
    handleInputFocus()
    focusEqInputRef.addEventListener('blur', handleInputFocus)
  }
  /**
   * 响应式赋值
   * @param val 输入框输入的值
   * @returns
   */
  const handleSelect = (val: string) => {
    if (!focusRowState.value) return
    const { $row, $prop } = focusRowState.value
    ;($row as any)[$prop as keyof T] += val
  }

  const cancelPermanentFocus = (event?: MouseEvent) => {
    if (!handleReset()) return
    if (!event) return
    const target = event.target as HTMLInputElement
    target.parentElement?.classList.add('is-focus')
    target.focus()
    focusRowState.value = null
  }

  const handleSelectParam = async () => {
    if (!focusEqInputRef) {
      return await useParameterDialog()
    }
    focusEqInputRef.removeEventListener('blur', handleInputFocus)
    focusEqInputRef.blur()
    nextTick(() => {
      focusEqInputRef?.parentElement?.classList.add('is-focus')
    })
    const res = await useParameterDialog()
    handleSelect(res)
    focusEqInputRef.focus()
    focusEqInputRef.addEventListener('blur', handleInputFocus)
    return res
  }

  watch(
    () => focusRowState.value?.$row['flag'],
    flag => {
      if (!flag && focusRowState.value) {
        focusRowState.value = null
        focusEqInputRef = null
      }
    }
  )
  return {
    focusRowState,
    handleInputClick,
    handleSelect,
    cancelPermanentFocus,
    handleSelectParam
  }
}
