import { IEmiter, IMenu, IWin } from '@wuk/cfg'
import { onBeforeUnmount, onMounted, Ref, ref } from 'vue'
import { App } from '../boots'

export type UseHandler = (key: string, handler: (...args: any[]) => void) => void
export const useSdk = <T extends IEmiter>(target?: T): [Ref<T | undefined>, UseHandler] => {
  const nodeRef = ref<T | undefined>(target)
  const useHandler = (key: string, handler: (...args: any[]) => void) => {
    if (!nodeRef.value) {
      return
    }
    onMounted(() => {
      nodeRef.value.on(key, handler)
    })

    onBeforeUnmount(() => {
      nodeRef.value.off(key, handler)
    })
  }

  return [nodeRef as Ref<T | undefined>, useHandler]
}

export const useWin = () => useSdk<IWin>(App.win)
export const useMenu = () => useSdk<IMenu>(App.sdk?.menu)
