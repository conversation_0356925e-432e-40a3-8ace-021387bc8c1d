import { ref } from 'vue'

interface SuccessState {
  [key: string]: boolean
}

export function useSettingSuccess() {
  const successStates = ref<SuccessState>({})

  const markSuccess = (key: string) => {
    successStates.value = {
      ...successStates.value,
      [key]: true
    }
  }

  const clearSuccess = (key: string) => {
    const newStates = { ...successStates.value }
    delete newStates[key]
    successStates.value = newStates
  }

  const clearAllSuccess = () => {
    successStates.value = {}
  }

  return {
    successStates,
    markSuccess,
    clearSuccess,
    clearAllSuccess
  }
}
