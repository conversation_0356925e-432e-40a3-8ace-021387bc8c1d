import { TargetClass, RootCore } from '@/renderer/boots'
import { ComConfig, BizConfigs, BizMenuItem } from './types'
import { IMenu } from '@wuk/cfg'

@TargetClass(BizConfigs, RootCore, true)
export default class ConfigsImpl extends BizConfigs {
  private _configs: ComConfig = {}
  private _sysMenu: BizMenuItem[] = []

  async init() {
    await super.init()

    // this.app.sdk?.menu?.on(IMenu.MenuChanged, this.handleMenuChanged)
    // this._sysMenu = (await this.app.sdk?.menu?.sysMenu()) || []
  }

  get configs(): ComConfig {
    return this._configs
  }

  get sysMenu(): BizMenuItem[] {
    return this._sysMenu
  }

  private async handleMenuChanged() {
    // this._sysMenu = (await this.app.sdk?.menu?.sysMenu()) || []
  }
}
