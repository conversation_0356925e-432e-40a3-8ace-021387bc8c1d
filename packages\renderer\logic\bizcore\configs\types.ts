import { TargetEntry, useTarget } from '@/renderer/boots'
import type { AppMenuItem as BizMenuItem } from '@wuk/cfg'

export interface ComConfig extends Record<string, any> {
  test?: string
}

export { BizMenuItem }

export abstract class BizConfigs extends TargetEntry<BizConfigs> {
  static key = 'logic.BizConfigs'

  abstract get configs(): ComConfig
  abstract get sysMenu(): BizMenuItem[]

  static get impl(): BizConfigs | undefined {
    return useConfigs()
  }
}

export const useConfigs = (): BizConfigs | undefined => useTarget<BizConfigs>(BizConfigs)
