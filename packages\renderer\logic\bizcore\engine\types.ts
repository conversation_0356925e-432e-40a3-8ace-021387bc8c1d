import { TargetEntry, useTarget } from '@/renderer/boots'
import {
  AttributeItem,
  AttributeOptions,
  CalsGroupItem,
  DashNumberItem,
  DashOptions,
  DisplayCrtOptions,
  DisplayItem,
  DisplayOptions,
  DisplayQuadOption,
  EngineOptions,
  CalcGroupable,
  SetupOptions,
  TablesOptions,
  TimerItem,
  TimersOptions,
  UDisplay,
  VibBandFilterOption,
  VibInputOption,
  VibSignalOption,
  VibSpectrumOption,
  VibSystemOption,
  VibTachInputOption,
  VibTFInputOption,
  TableGroup,
  TableData,
  TableCfg,
  TableLib,
  LimitOptions,
  AlarmRelay,
  LimitCounter,
  LimitParameter,
  PlcDriverOptions,
  PlcCfgOption,
  CfgFileType,
  Vxicard
} from '@wuk/cfg'

export abstract class BizEngine extends TargetEntry<BizEngine> {
  static key = 'bizcore.BizEngine'

  /**
   * Engine Options
   */
  abstract readEngineOptions(): Promise<EngineOptions | undefined>
  abstract writeEngineOptions(val: Partial<EngineOptions>): Promise<boolean>

  /**
   * Valid Engine Dash Numbers
   */
  abstract readDashOptions(): Promise<DashOptions | undefined>
  abstract removeDash(index: number): Promise<boolean>
  abstract addDash(val: DashNumberItem, index?: number): Promise<boolean>
  abstract modifyDash(index: number, val: Partial<DashNumberItem>): Promise<boolean>

  /**
   * Display list
   */
  abstract readDisplayOptions(): Promise<DisplayCrtOptions | undefined>
  abstract removeDisplayQuad(index: number, quadIdx?: number): Promise<boolean>
  abstract addDisplayCrt(name: string, index?: number): Promise<boolean>
  abstract addDisplayQuad(val: DisplayQuadOption, index: number, quadIdx?: number): Promise<boolean>
  abstract modifyDisplayCrt(index: number, name: string): Promise<boolean>
  abstract modifyDisplayQuad(
    index: number,
    val: Partial<DisplayQuadOption>,
    quadIdx: number
  ): Promise<boolean>

  /**
   * User Difined Displays
   */
  abstract readDisplayDefinedOptions(): Promise<DisplayOptions | undefined>
  abstract removeDisplayFile(index: number): Promise<boolean>
  abstract addDisplayFile(val: DisplayItem, index?: number): Promise<boolean>
  abstract modifyDisplayFile(index: number, val: Partial<DisplayItem>): Promise<boolean>

  abstract loadDisplay(index: number): Promise<UDisplay | undefined>
  abstract modifyDisplay(index: number, val: Partial<UDisplay>): Promise<boolean>
  abstract saveDisplay(index: number): Promise<boolean>

  /**
   * Vibration Signals
   */
  abstract readVibSignals(): Promise<Array<VibSignalOption>>
  abstract removeVibSignal(index: number): Promise<boolean>
  abstract addVibSignal(val: VibSignalOption, index?: number): Promise<boolean>
  abstract modifyVibSignal(index: number, val: Partial<VibSignalOption>): Promise<boolean>

  // Vibration Setup
  abstract readSetups(): Promise<Array<SetupOptions>>
  abstract removeSetup(index: number): Promise<boolean>
  abstract addSetup(val: SetupOptions, index?: number): Promise<boolean>
  abstract modifySetup(index: number, val: Partial<SetupOptions>): Promise<boolean>

  // Vibration System Setup
  abstract readVibSystem(index: number): Promise<VibSystemOption | undefined>
  abstract writeVibSystem(index: number, val: Partial<VibSystemOption>): Promise<boolean>

  // Vibration Input Setup
  abstract readVibInput(index: number): Promise<Array<VibInputOption> | undefined>
  abstract writeVibInput(index: number, idx: number, val: Partial<VibInputOption>): Promise<boolean>

  // Broadband Filter Setup
  abstract readVibBandFilter(index: number): Promise<Array<VibBandFilterOption> | undefined>
  abstract writeVibBandFilter(
    index: number,
    idx: number,
    val: Partial<VibBandFilterOption>
  ): Promise<boolean>

  // Tachometer Input Setup
  abstract readVibTachInput(index: number): Promise<Array<VibTachInputOption> | undefined>
  abstract writeVibTachInput(
    index: number,
    idx: number,
    val: Partial<VibTachInputOption>
  ): Promise<boolean>

  // Spectrum Analysis Setup
  abstract readVibSpectrum(index: number): Promise<Array<VibSpectrumOption> | undefined>
  abstract writeVibSpectrum(
    index: number,
    idx: number,
    val: Partial<VibSpectrumOption>
  ): Promise<boolean>

  // Tracking Filter Setup
  abstract readVibTFInput(index: number): Promise<Array<VibTFInputOption> | undefined>
  abstract writeVibTFInput(
    index: number,
    idx: number,
    val: Partial<VibTFInputOption>
  ): Promise<boolean>

  /**
   * Parameter Attributes
   */
  abstract readAttributes(): Promise<AttributeOptions | undefined>
  abstract removeAttribute(index: number): Promise<boolean>
  abstract addAttribute(val: AttributeItem, index?: number): Promise<boolean>
  abstract modifyAttribute(index: number, val: Partial<AttributeItem>): Promise<boolean>

  /**
   * PLC Setup
   */
  abstract readPLCSetups(): Promise<PlcDriverOptions | undefined>
  abstract modifyPLCSetup(index: number, val: Partial<PlcCfgOption>): Promise<boolean>

  /**
   * Calcs Initial
   */
  abstract readCalcsInitOptions(): Promise<Array<CalsGroupItem>>
  abstract removeCalcsInit(index: number): Promise<boolean>
  abstract addCalcsInit(val: CalsGroupItem, index?: number): Promise<boolean>
  abstract modifyCalcsInit(index: number, val: Partial<CalsGroupItem>): Promise<boolean>

  /**
   * Calcs Final
   */
  abstract readCalcsFinalOptions(): Promise<Array<CalsGroupItem>>
  abstract removeCalcsFinal(index: number): Promise<boolean>
  abstract addCalcsFinal(val: CalsGroupItem, index?: number): Promise<boolean>
  abstract modifyCalcsFinal(index: number, val: Partial<CalsGroupItem>): Promise<boolean>

  /**
   * Calcs
   */
  abstract readCalcsOptions(type?: CfgFileType): Promise<Array<CalsGroupItem>>
  abstract removeCalcFile(index: number, type?: CfgFileType): Promise<boolean>
  abstract addCalcFile(item: CalsGroupItem, index?: number, type?: CfgFileType): Promise<boolean>
  abstract modifyCalcFile(
    index: number,
    item: Partial<CalsGroupItem>,
    type?: CfgFileType
  ): Promise<boolean>

  abstract loadCalc(
    file: string,
    type?: CfgFileType,
    force?: boolean
  ): Promise<CalcGroupable | undefined>
  abstract modifyCalc(
    file: string,
    val: Partial<CalcGroupable>,
    type?: CfgFileType
  ): Promise<boolean>
  abstract saveCalc(file: string, type?: CfgFileType): Promise<boolean>

  abstract loadCalcText(
    file: string,
    type?: CfgFileType,
    force?: boolean
  ): Promise<string | undefined>
  abstract saveCalcText(file: string, text: string, type?: CfgFileType): Promise<boolean>

  /**
   * Calcs Virtual Signal
   */
  abstract readCalcsSignalOptions(): Promise<Array<CalsGroupItem>>
  abstract removeCalcsSignal(index: number): Promise<boolean>
  abstract addCalcsSignal(val: CalsGroupItem, index?: number): Promise<boolean>
  abstract modifyCalcsSignal(index: number, val: Partial<CalsGroupItem>): Promise<boolean>

  /**
   * Tables
   */
  abstract readTableOptions(): Promise<TablesOptions | undefined>
  abstract removeTableGroup(index: number): Promise<boolean>
  abstract addTableGroup(val: TableGroup, index?: number): Promise<boolean>
  abstract modifyTableGroup(index: number, val: Partial<TableGroup>): Promise<boolean>

  abstract removeTable(groupName: string, index: number): Promise<boolean>
  abstract addTable(groupName: string, val: TableData, index?: number): Promise<boolean>
  abstract modifyTable(groupName: string, index: number, val: Partial<TableData>): Promise<boolean>

  abstract readTableCfg(groupName: string): Promise<TableCfg | undefined>
  abstract modifyTableCfg(groupName: string, val: Partial<TableCfg>): Promise<boolean>

  abstract removeTableLib(groupName: string, tableName: string): Promise<boolean>
  abstract addTableLib(groupName: string, tableName: string, val: TableLib): Promise<boolean>
  abstract modifyTableLib(
    groupName: string,
    tableName: string,
    val: Partial<TableLib>
  ): Promise<boolean>

  /**
   * System Timers
   */
  abstract readTimersOptions(): Promise<TimersOptions | undefined>
  abstract removeTimer(index: number): Promise<boolean>
  abstract addTimer(val: TimerItem, index?: number): Promise<boolean>
  abstract modifyTimer(index: number, val: Partial<TimerItem>): Promise<boolean>

  /**
   * Limit Options
   */
  abstract readLimitOptions(): Promise<LimitOptions | undefined>
  abstract modifyLimitOptions(val: Partial<LimitOptions>): Promise<boolean>
  abstract modifyAlarmRelay(val: Partial<AlarmRelay>): Promise<boolean>
  abstract removeLimitCounter(index: number): Promise<boolean>
  abstract addLimitCounter(val: LimitCounter, index?: number): Promise<boolean>
  abstract modifyLimitCounter(index: number, val: Partial<LimitCounter>): Promise<boolean>
  abstract removeLimitParameter(index: number): Promise<boolean>
  abstract addLimitParameter(val: LimitParameter, index?: number): Promise<boolean>
  abstract modifyLimitParameter(index: number, val: Partial<LimitParameter>): Promise<boolean>

  /**
   * Device VxiCards
   */
  abstract readVxiCardsOptions(): Promise<Array<Vxicard> | undefined>
  abstract removeVxiCards(name: string): Promise<boolean>
  abstract addVxiCards(val: Vxicard): Promise<boolean>
  abstract modifyVxiCards(name: string, val: Partial<Vxicard>): Promise<boolean>

  static get impl(): BizEngine | undefined {
    return useEngine()
  }

  static get onEngineOptionsChanged() {
    return 'BizEngine.onEngineOptionsChanged'
  }

  static get onDashOptionsChanged() {
    return 'BizEngine.onDashOptionsChanged'
  }

  static get onDisplayOptionsChanged() {
    return 'BizEngine.onDisplayOptionsChanged'
  }

  static get onDisplayFileChanged() {
    return 'BizEngine.onDisplayFileChanged'
  }

  static get onVibrationSignalsChanged() {
    return 'BizEngine.onVibrationSignalsChanged'
  }

  static get onVibrationSystemSetupChanged() {
    return 'BizEngine.onVibrationSystemSetupChanged'
  }

  static get onVibrationSetupChanged() {
    return 'BizEngine.onVibrationSetupChanged'
  }

  static get onVibrationInputSetupChanged() {
    return 'BizEngine.onVibrationInputSetupChanged'
  }

  static get onBroadbandFilterSetupChanged() {
    return 'BizEngine.onBroadbandFilterSetupChanged'
  }

  static get onTachometerInputSetupChanged() {
    return 'BizEngine.onTachometerInputSetupChanged'
  }

  static get onSpectrumAnalysisSetupChanged() {
    return 'BizEngine.onSpectrumAnalysisSetupChanged'
  }

  static get onTrackingFilterSetupChanged() {
    return 'BizEngine.onTrackingFilterSetupChanged'
  }

  static get onAttributeOptionsChanged() {
    return 'BizEngine.onAttributeOptionsChanged'
  }

  static get onPLCSetupOptionsChanged() {
    return 'BizEngine.onPLCSetupOptionsChanged'
  }

  static get onCalcsInitialOChanged() {
    return 'BizEngine.onCalcsInitialOChanged'
  }

  static get onCalcsFinalChanged() {
    return 'BizEngine.onCalcsFinalChanged'
  }

  static get onCalcsSignalChanged() {
    return 'BizEngine.onCalcsSignalChanged'
  }

  static get onTablesOptionsChanged() {
    return 'BizEngine.onTablesOptionsChanged'
  }

  static get onTableCfgChanged() {
    return 'BizEngine.onTableCfgChanged'
  }

  static get onTimersOptionsChanged() {
    return 'BizEngine.onTimersOptionsChanged'
  }

  static get onCalcFileChanged() {
    return 'BizEngine.onCalcFileChanged'
  }

  static get onCalcsOptionsChanged() {
    return 'BizEngine.onCalcsOptionsChanged'
  }

  static get onLimitOptionsChanged() {
    return 'BizEngine.onLimitOptionsChanged'
  }

  static get onVxiCardsOptionsChanged() {
    return 'BizEngine.onVxiCardsOptionsChanged'
  }
}

export const useEngine = (): BizEngine | undefined => useTarget<BizEngine>(BizEngine)
