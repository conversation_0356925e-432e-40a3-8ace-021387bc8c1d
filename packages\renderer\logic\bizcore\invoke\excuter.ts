import { TargetClass, RootCore } from '@/renderer/boots'
import { BizInvoke, PageRouter } from './types'
import { ActionKey, AppMenuItem, AppState, IMenu, IWin } from '@wuk/cfg'
import { WuiMessageBox, WuiNotification } from '@wuk/wui'

@TargetClass(BizInvoke, RootCore)
export default class InvokeImpl extends BizInvoke {
  constructor(key: string) {
    super(key)

    this.hanldeMenuClicked = this.hanldeMenuClicked.bind(this)
    this.handleAppState = this.handleAppState.bind(this)
  }

  async init() {
    await super.init()

    this.app.sdk?.menu?.on(IMenu.MenuClicked, this.hanldeMenuClicked)
    this.app.sdk?.win.on(IWin.ONSTATE, this.handleAppState)
  }

  showMain() {
    this.app.sdk?.win.switchState(AppState.AP_MAIN)
  }

  exiteApp() {}

  showEngine() {
    this.app.sdk?.win.switchState(AppState.AP_ENGINE)
  }

  closeEngine() {}

  createEngine(): void {
    this.emit(BizInvoke.onCreateEngine)
  }

  showMessageBox(content: string, title?: string) {
    return new Promise<boolean>(resolve => {
      WuiMessageBox.confirm(content, { title })
        .then(() => {
          resolve(true)
        })
        .catch(() => {
          resolve(false)
        })
    })
  }

  showNotify(title: string, message: string, type = 'success') {
    WuiNotification({
      title,
      message,
      position: 'bottom-right',
      type: 'success'
    })
  }

  async showRightMenu(actionKey: ActionKey, items: AppMenuItem[], x: number, y: number) {
    const result = await this.app.sdk?.menu.showRightMenu(actionKey, items, x, y)
    return !!result
  }

  private handleAppState(state: AppState) {
    let name = PageRouter.PR_MAIN
    switch (state) {
      case AppState.AP_ENGINE:
        name = PageRouter.PR_ENGINE
        break
    }

    this.emit(BizInvoke.onRouterChange, name)
  }

  private async hanldeMenuClicked(actionKey: ActionKey, key: ActionKey) {
    this.emit(BizInvoke.onMenuClicked, actionKey, key)
  }
}
