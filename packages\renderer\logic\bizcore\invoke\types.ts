import { TargetEntry, useTarget } from '@/renderer/boots'
import { ActionKey, AppMenuItem } from '@wuk/cfg'

export enum PageRouter {
  PR_MAIN = 'main',
  PR_MAIN_HOME = 'main_welcome',
  PR_ENGINE = 'engine',
  PR_ENGINE_HONE = 'engine_home'
}

export abstract class BizInvoke extends TargetEntry<BizInvoke> {
  static key = 'bizcore.BizInvoke'

  abstract showMain(): void
  abstract exiteApp(): void
  abstract showEngine(): void
  abstract closeEngine(): void

  abstract createEngine(): void
  abstract showMessageBox(title: string, content: string): Promise<boolean>
  abstract showNotify(title: string, message: string, type?: string): void

  abstract showRightMenu(
    actionKey: ActionKey,
    items: AppMenuItem[],
    x: number,
    y: number
  ): Promise<boolean>

  static get impl(): BizInvoke | undefined {
    return useInvoke()
  }

  static get onCreateEngine() {
    return 'BizInvoke.onCreateEngine'
  }

  static get onCopyEngine() {
    return 'BizInvoke.onCopyEngine'
  }

  static get onDeleteEngine() {
    return 'BizInvoke.onDeleteEngine'
  }

  static get onRouterChange() {
    return 'BizInvoke.onRouterChange'
  }

  static get onMenuClicked() {
    return 'BizInvoke.onMenuClicked'
  }
}

export const useInvoke = (): BizInvoke | undefined => useTarget<BizInvoke>(BizInvoke)
