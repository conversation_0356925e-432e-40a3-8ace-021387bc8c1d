import { TargetEntry, useTarget } from '@/renderer/boots'
import { LinkMap } from '@/renderer/utils/linkmap'
import type {
  AppCustomer,
  AppEngine,
  AudioOptions,
  AudoItem,
  CRSOptions,
  CalcFile,
  CalcGroupable,
  CalcsOptions,
  CalsGroupItem,
  CfgFileType,
  CfgVersion,
  ColorOptions,
  CrtItem,
  CrtOptions,
  DataFormatOptions,
  DeviceOptions,
  DialogProperty,
  Hardware,
  MessageInfo,
  PrintModeOptions,
  ResolutionItem,
  ResolutionOptions,
  RgbItem,
  SystemOptions,
  TableCfg,
  TableData,
  TableGroup,
  TableLib,
  TablesOptions,
  HardwareInfo
} from '@wuk/cfg'
export { AppCustomer, CfgVersion, AppEngine }

export abstract class BizMain extends TargetEntry<BizMain> {
  static key = 'bizcore.BizMain'

  abstract get customer(): AppCustomer | undefined
  abstract get version(): CfgVersion | undefined
  abstract get engine(): AppEngine | undefined

  abstract get colors(): LinkMap<string, string, RgbItem>
  abstract get resolutions(): LinkMap<string, string, ResolutionItem>
  abstract showOpenDialog(title: string, properties?: DialogProperty[]): Promise<string>
  abstract uploadImage(path: string): Promise<string>
  abstract customerURL(path: string): Promise<string>
  abstract imageURL(path: string): Promise<string>

  abstract changeCustomer(name: string): Promise<boolean>

  abstract importVersion(name: string): Promise<boolean>
  abstract exportVersion(comments: string): Promise<boolean>

  abstract getCustomers(): AppCustomer[]
  abstract getVersions(): CfgVersion[]
  abstract getEngines(): AppEngine[]

  abstract getMessages(): Promise<Array<MessageInfo> | undefined>
  abstract openEngine(name: string): Promise<boolean>
  abstract createEngine(name: string): Promise<boolean>
  abstract copyEngine(name: string, newName: string): Promise<boolean>
  abstract deleteEngine(name: string): Promise<boolean>

  // System Options
  abstract readSystemOptions(): Promise<SystemOptions | undefined>
  abstract writeSystemOptions(val: Partial<SystemOptions>): Promise<boolean>

  // Print Modes
  abstract readPrintModes(): Promise<PrintModeOptions | undefined>
  abstract writePrintModes(val: Partial<PrintModeOptions>): Promise<boolean>

  // Data Formats
  abstract readDataFormat(): Promise<DataFormatOptions | undefined>
  abstract writeDataFormat(val: Partial<DataFormatOptions>): Promise<boolean>

  // CRS Options
  abstract readCRSOptions(): Promise<CRSOptions | undefined>
  abstract writeCRSOptions(val: Partial<CRSOptions>): Promise<boolean>

  // Crts Options
  abstract readCrtOptions(): Promise<CrtOptions | undefined>
  abstract setCrtRate(val: number): Promise<boolean>
  abstract removeCrt(index: number): Promise<boolean>
  abstract addCrt(val: CrtItem, index?: number): Promise<boolean>
  abstract modifyCrt(index: number, val: Partial<CrtItem>): Promise<boolean>

  // Audio Options
  abstract readAudioOptions(): Promise<AudioOptions | undefined>
  abstract setHostAddress(val: string): Promise<boolean>
  abstract removeAudio(index: number): Promise<boolean>
  abstract addAudio(val: AudoItem, index?: number): Promise<boolean>
  abstract modifyAudio(index: number, val: Partial<AudoItem>): Promise<boolean>

  // Device Options
  abstract readDeviceOptions(): Promise<DeviceOptions | undefined>
  abstract removeDevice(index: number): Promise<boolean>
  abstract addDevice(val: Hardware, index?: number): Promise<boolean>
  abstract modifyDevice(index: number, val: Partial<Hardware>): Promise<boolean>

  // Hardware Options
  abstract readHardwares(): Promise<Array<HardwareInfo> | undefined>
  abstract removeHardware(index: number): Promise<boolean>
  abstract addHardware(val: HardwareInfo, index?: number): Promise<boolean>
  abstract modifyHardware(index: number, val: Partial<HardwareInfo>): Promise<boolean>

  // Color Options
  abstract readColorOptions(): Promise<ColorOptions | undefined>
  abstract removeColor(index: number): Promise<boolean>
  abstract addColor(val: RgbItem, index?: number): Promise<boolean>
  abstract modifyColor(index: number, val: Partial<RgbItem>): Promise<boolean>

  // Resoltion Options
  abstract readResolutionOptions(): Promise<ResolutionOptions | undefined>
  abstract removeResolution(index: number): Promise<boolean>
  abstract addResolution(val: ResolutionItem, index?: number): Promise<boolean>
  abstract modifyResolution(index: number, val: Partial<ResolutionItem>): Promise<boolean>

  /**
   * Calcs
   */
  abstract readCalcsOptions(): Promise<Array<CalsGroupItem>>
  abstract removeCalcFile(index: number): Promise<boolean>
  abstract addCalcFile(item: CalsGroupItem, index?: number): Promise<boolean>
  abstract modifyCalcFile(index: number, item: Partial<CalsGroupItem>): Promise<boolean>

  abstract loadCalc(
    file: string,
    type?: CfgFileType,
    force?: boolean
  ): Promise<CalcGroupable | undefined>
  abstract modifyCalc(
    file: string,
    val: Partial<CalcGroupable>,
    type?: CfgFileType
  ): Promise<boolean>
  abstract saveCalc(file: string, type?: CfgFileType): Promise<boolean>

  abstract loadCalcText(file: string, type?: CfgFileType): Promise<string | undefined>
  abstract saveCalcText(file: string, text: string, type?: CfgFileType): Promise<boolean>

  /**
   * Tables
   */
  abstract readTableOptions(): Promise<TablesOptions | undefined>
  abstract removeTableGroup(index: number): Promise<boolean>
  abstract addTableGroup(val: TableGroup, index?: number): Promise<boolean>
  abstract modifyTableGroup(index: number, val: Partial<TableGroup>): Promise<boolean>

  abstract removeTable(groupName: string, index: number): Promise<boolean>
  abstract addTable(groupName: string, val: TableData, index?: number): Promise<boolean>
  abstract modifyTable(groupName: string, index: number, val: Partial<TableData>): Promise<boolean>

  abstract readTableCfg(groupName: string): Promise<TableCfg | undefined>
  abstract modifyTableCfg(groupName: string, val: Partial<TableCfg>): Promise<boolean>

  abstract removeTableLib(groupName: string, tableName: string): Promise<boolean>
  abstract addTableLib(groupName: string, tableName: string, val: TableLib): Promise<boolean>
  abstract modifyTableLib(
    groupName: string,
    tableName: string,
    val: Partial<TableLib>
  ): Promise<boolean>

  static get impl(): BizMain | undefined {
    return useMain()
  }

  static get onCustomersChanged() {
    return 'BizMain.onCustomersChanged'
  }

  static get onVersionsChanged() {
    return 'BizMain.onVersionsChanged'
  }

  static get onMessageArrived() {
    return 'BizMain.onMessageArrived'
  }

  static get onEnginesChanged() {
    return 'BizMain.onEnginesChanged'
  }

  static get onSystemOptionChanged() {
    return 'BizMain.onSystemOptionChanged'
  }

  static get onPrintModeChanged() {
    return 'BizMain.onPrintModeChanged'
  }

  static get onTablesOptionsChanged() {
    return 'BizMain.onTablesOptionsChanged'
  }

  static get onTableCfgChanged() {
    return 'BizMain.onTableCfgChanged'
  }

  static get onDataFormatChanged() {
    return 'BizMain.onDataFormatChanged'
  }

  static get onCRSOptionChanged() {
    return 'BizMain.onCRSOptionChanged'
  }

  static get onCrtOptionChanged() {
    return 'BizMain.onCrtOptionChanged'
  }

  static get onAudioOptionChanged() {
    return 'BizMain.onAudioOptionChanged'
  }

  static get onDeviceOptionChanged() {
    return 'BizMain.onDeviceOptionChanged'
  }

  static get onHandleHardwareOptionChanged() {
    return 'BizMain.onHandleHardwareOptionChanged'
  }

  static get onColorOptionChanged() {
    return 'BizMain.onColorOptionChanged'
  }

  static get onResolutionOptionChanged() {
    return 'BizMain.onResolutionOptionChanged'
  }

  static get onCalcFileChanged() {
    return 'BizMain.onCalcFileChanged'
  }

  static get onCalcsOptionsChanged() {
    return 'BizMain.onCalcsOptionsChanged'
  }
}

export const useMain = (): BizMain | undefined => useTarget<BizMain>(BizMain)
