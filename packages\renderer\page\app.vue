<template>
  <wui-config-provider :locale="locale">
    <router-view v-slot="{ Component }">
      <component :is="Component" />
    </router-view>
  </wui-config-provider>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { WuiConfigProvider, enUs } from '@wuk/wui'
import { useHandler, useLogic } from '../hooks'
import { BizInvoke } from '../logic'
import { useRouter } from 'vue-router'
import { PageRouter } from '@/renderer/logic'

const locale = computed(() => {
  return enUs
})

const router = useRouter()
const { invoker: invokePtr } = useLogic()

useHandler(invokePtr, BizInvoke.onRouterChange, (name: string) => {
  if (name === PageRouter.PR_MAIN) {
    name = PageRouter.PR_MAIN_HOME
  } else if (name === PageRouter.PR_ENGINE) {
    name = PageRouter.PR_ENGINE_HONE
  }
  router.replace({ name })
})
</script>
