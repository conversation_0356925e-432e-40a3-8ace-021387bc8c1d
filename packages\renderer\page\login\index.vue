<template>
  <wui-config-provider>
    <router-view v-slot="{ Component }">
      <component :is="Component" />
    </router-view>
  </wui-config-provider>
</template>

<script setup lang="ts">
import { reactive, computed } from 'vue'
import { WuiConfigProvider } from '@wuk/wui'
// import en from 'element-plus/es/locale/lang/en'

// element language
// const locale = computed(() => {
//   if (globalStore.language == 'en') return en
//   return en
// })
</script>

<style lang="scss" scoped>
@import './index.module';
</style>
