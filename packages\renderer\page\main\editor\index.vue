<template>
  <div :class="styles.editor" ref="divRef" style="height: 400px" />
</template>

<script lang="ts" setup name="MainMenu">
import { AiEditor } from 'aieditor'
import 'aieditor/dist/style.css'
import styles from './index.module.scss'

import { onMounted, ref } from 'vue'

const divRef = ref<Element>()
const editor = ref<AiEditor>()

onMounted(() => {
  const element = divRef.value
  editor.value =
    element &&
    new AiEditor({
      element,
      placeholder: 'Click to Input Content...',
      toolbarKeys: [
        'undo',
        'redo',
        'brush',
        'eraser',
        '|',
        'heading',
        'font-family',
        'font-size',
        '|',
        'bold',
        'italic',
        'underline',
        'strike',
        'link',
        'code',
        'subscript',
        'superscript',
        'hr',
        'todo',
        'emoji',
        '|',
        'highlight',
        'font-color',
        '|',
        'align',
        'line-height',
        '|',
        'bullet-list',
        'ordered-list',
        'indent-decrease',
        'indent-increase',
        'break',
        '|',
        'image',
        'video',
        'attachment',
        'quote',
        'code-block',
        'table',
        '|',
        'source-code',
        'printer',
        'fullscreen'
        // 'ai'
      ],
      content: '<code>AiEditor is an Open Source Rich Text Editor Designed for AI.  </code>',
      contentIsMarkdown: true
    })
})
</script>
