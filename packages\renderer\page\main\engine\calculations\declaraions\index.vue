<template>
  <div :class="b()">
    <div :class="e('search')">
      <div :class="e('search', 'content')">
        <span>Search: </span>
        <wui-input placeholder="Search Setting" style="width: 200px; height: 30px" />
      </div>
      <div :class="e('search', 'content')">
        <span :class="e('search', 'content', 'btn')" @click="handleChangeFile"
          >Edit Include File</span
        >
        <span :class="e('search', 'content', 'btn')" @click="handleSelectParam">Parameters</span>
      </div>
    </div>
    <div :class="e('code')">
      <CodeEditorTabs ref="editorTabsRef" v-model:tabs="tabs" />
    </div>
  </div>
</template>

<script lang="tsx" setup>
import { ref } from 'vue'
import { CodeEditorTabs, type CodeEditorTabsExpose } from '@/renderer/components/CodeEditor'
import $styles from './index.module.scss'
import { useBem } from '@/renderer/hooks'
import { useParameterDialog } from '@/renderer/utils/common'
import { WuiMessage } from '@wuk/wui'
const { b, e, m } = useBem('declaraions', $styles)
const editorTabsRef = ref<CodeEditorTabsExpose>()
const tabs = ref([
  {
    id: '1',
    title: 'file1.cal',
    content: '// 第一个文件',
    headerKeywords: '#include',
    ext: '*',
    isModified: false
  },
  {
    id: '2',
    title: 'file2.cal',
    content: '// 第二个文件',
    headerKeywords: '#include',
    ext: '*',
    isModified: false
  }
])
const handleSelectParam = async () => {
  const res = await useParameterDialog()
  editorTabsRef.value?.code?.insert(res)
}
const handleChangeFile = () => {
  const fileName = editorTabsRef.value?.code?.curFileName
  if (!fileName) {
    WuiMessage.error({
      message: '文件不存在，请聚焦#include开头的行再点击按钮',
      offset: 90
    })
    return
  }
  WuiMessage({
    message: `调整新的文件：${fileName}`,
    offset: 90
  })
  editorTabsRef.value?.addTab({
    title: fileName,
    content: `please input ${fileName}`
  })
  editorTabsRef.value?.code?.resetFileName()
}
</script>
