<template>
  <div :class="styles.box">
    <div :class="styles.box_search">
      <div :class="styles.box_search_content">
        <span>Search: </span>
        <wui-input
          v-model="inputA"
          placeholder="Search Setting"
          style="width: 200px; height: 30px"
        />
      </div>
      <div :class="styles.box_search_content">
        <span :class="styles.box_search_content_btn">Edit Include File</span>
        <span :class="styles.box_search_content_btn" @click="aModelShow = true">Parameters</span>
      </div>
    </div>
    <div :class="styles.box_table">
      <CodeEditor height="100%" />
    </div>
  </div>

  <MyDialog v-model="aModelShow" title="Parameter Selection" @ok="submit">
    <h5>Select Parameter</h5>
    <wui-table :data="tableData" border height="150px" style="width: 100%">
      <wui-table-column prop="name" label="" align="center" show-overflow-tooltip />
    </wui-table>
    <div :class="styles.modelBox">
      <div>
        <span>Select </span>
        <wui-input
          v-model="inputB"
          placeholder="Please input"
          style="width: 180px; height: 30px; margin-left: 5px"
        />
      </div>
      <div class="btnBox">
        <wui-button>Search</wui-button>
        <wui-button>Signal Info</wui-button>
      </div>
    </div>
  </MyDialog>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import styles from './index.module.scss'
import MyDialog from '@/renderer/components/dialog/index.vue'
import CodeEditor from '@/renderer/components/CodeEditor'

const inputA = ref('')
const inputB = ref('')
const aModelShow = ref(false)
const tableData = [
  {
    name: 'Param1'
  },
  {
    name: 'Param2'
  },
  {
    name: 'Param3'
  },
  {
    name: 'Param4'
  }
]

const submit = () => {
  aModelShow.value = false
}
</script>

<style lang="scss" stylemodule>
// .wui-table__header-wrapper {
//   display: none;
// }
</style>

<style lang="scss" scoped>
.btnBox {
  width: 190px;
  display: flex;
  justify-content: space-between;
  :deep(.wui-button + .wui-button) {
    margin-left: 0;
  }
}
</style>
