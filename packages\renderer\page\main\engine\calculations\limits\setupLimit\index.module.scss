$namespace: 'setup-limit';
.#{$namespace} {
  &_header {
    &_btn {
      margin-left: 20px;
    }
  }
  &_body {
    $body-max-height: 400px;
    $body-left-width: 460px;
    $body-right-width: 350px;
    width: 100%;
    height: 100%;
    max-height: $body-max-height;
    background-color: #ffffff;
    &_tabs {
      height: $body-max-height;
      &_pane {
        height: 100%;
      }
    }
    &_table {
      height: 100%;
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;
      padding-top: 30px;
      &_left {
        width: $body-left-width;
        height: 100%;
      }
      &_right {
        height: 100%;
      }
    }
    &_rate {
      width: $body-right-width;
      position: relative;
      &_title {
        position: absolute;
        top: -32px;
        font-size: 18px;
        font-weight: bold;
      }
    }
  }
  &-actions {
    &_body {
      height: 270px;
      background-color: #ffffff;
      :global {
        .wui-input {
          width: 100%;
          height: 32px;
        }
        .wui-select {
          width: 120px;
          height: 32px;
        }
        .wui-button {
          max-width: 200px;
          &.is-text {
            display: block !important;
          }
        }
        .wui-form-item {
          margin-bottom: 10px;
          &__label {
            margin-bottom: 1px;
          }
        }
      }
    }
    &_form {
      &_inline {
        display: flex;
        justify-content: space-between;
      }
    }
  }
}
