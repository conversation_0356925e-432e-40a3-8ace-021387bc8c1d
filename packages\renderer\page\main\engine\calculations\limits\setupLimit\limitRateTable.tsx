import { useBem, useTableCommonMenu } from '@/renderer/hooks'
import { defineComponent, ref, onMounted } from 'vue'
import $styles from './index.module.scss'
import TableTool, { isAddOrInsertType, OpType } from '@/renderer/components/TableTool'
import { SetupLimitRateTableRow, currentParameters } from '../type'
import { WuiInput, WuiInputNumber } from '@wuk/wui'

export default defineComponent({
  name: 'LimitRateTable',
  props: {
    limitType: {
      type: String as () => 'start' | 'run',
      required: true
    },
    currentParameter: {
      type: Object as () => currentParameters,
      default: () => ({})
    }
  },
  emits: ['limit-actions'],
  setup(props, { expose, emit }) {
    const { e } = useBem('setup-limit', $styles)
    const data = ref<SetupLimitRateTableRow[]>([])

    const getDataInfo = () => {
      const limitConfig =
        props.limitType === 'start'
          ? props.currentParameter?.parameter?.start
          : props.currentParameter?.parameter?.run
      limitConfig?.rate_list?.forEach(cfg => {
        data.value.push({
          ...cfg,
          flag: false,
          row_type: '*'
        })
      })
    }

    const columns = [
      {
        prop: 'eng_units',
        label: 'Eng-Units',
        align: 'center'
      },
      {
        prop: 'time_sec',
        label: 'Time(sec)',
        align: 'center'
      }
    ]

    const createRow = (row_type: SetupLimitRateTableRow['row_type']) => ({
      eng_units: 0,
      time_sec: 0,
      exceeded_message: '',
      back_message: '',
      phase: '',
      color: '',
      alarm: '',
      store_event: false,
      flag: true,
      row_type
    })

    const { handleRowMenu } = useTableCommonMenu(
      data,
      (key, ...args) => {
        const { row, rowIndex } = args[0]
        switch (key) {
          case 'addKey':
            data.value.push(createRow('add'))
            break
          case 'deleteKey':
            handleOp('delete', row, rowIndex)
            break
          case 'modifyKey':
            handleOp('edit', row, rowIndex)
            break
          case 'insertKey':
            data.value.splice(rowIndex + 1, 0, createRow('insert'))
            break
          case 'limitActionsKey':
            emit('limit-actions', row, 'rate')
        }
      },
      [1],
      [
        { key: 'addKey', label: 'add' },
        { key: 'insertKey', label: 'insert' },
        { key: 'modifyKey', label: 'modify' },
        { key: 'deleteKey', label: 'delete' },
        { key: 'limitActionsKey', label: 'limit Actions' }
      ]
    )

    const handleOp = (op: OpType, row: SetupLimitRateTableRow | undefined, index: number) => {
      if (!row) return
      switch (op) {
        case 'edit':
          row.flag = true
          break
        case 'delete':
          handleDelete(index)
          break
        case 'select':
          row.row_type = '*'
          row.flag = false
          break
        case 'cancel':
          handleOpCancel(row, index)
          break
      }
    }
    const handleDelete = (index: number) => {
      data.value.splice(index, 1)
    }
    const handleOpCancel = (row: SetupLimitRateTableRow, index: number) => {
      if (isAddOrInsertType(row.row_type)) {
        data.value.splice(index, 1)
        return
      }
      row.row_type = '*'
      row.flag = false
    }

    onMounted(() => {
      getDataInfo()
    })

    expose({
      getData: () => data.value
    })

    return () => (
      <div class={[e('body', 'rate'), 'cfg-setup']}>
        <div class={e('body', 'rate', 'title')}>Rate-of-Change Limits</div>
        <div class={[e('body', 'table', 'right'), 'cfg-setup_table']}>
          <wui-table height='100%' data={data.value} border onRow-contextmenu={handleRowMenu}>
            {{
              default: () => (
                <>
                  {columns.map(column => (
                    <wui-table-column {...column}>
                      {{
                        default: ({ row }: any) => {
                          if (!row.flag) {
                            const value = row[column.prop]
                            const shouldFormat = ['eng_units', 'time_sec'].includes(column.prop)
                            return shouldFormat && typeof value === 'number'
                              ? value.toFixed(2)
                              : value
                          }

                          if (['eng_units', 'time_sec'].includes(column.prop)) {
                            return (
                              <wui-input-number
                                v-model={row[column.prop]}
                                input-align='left'
                                clearable
                                controls={false}
                                min={0}
                                max={9999}
                                style={{ width: '100%' }}
                                precision={2}
                                placeholder='Please Input'
                              />
                            )
                          }

                          return <WuiInput placeholder='Please Input' v-model={row[column.prop]} />
                        }
                      }}
                    </wui-table-column>
                  ))}
                  <wui-table-column width='100px' label='Op' align='center'>
                    {{
                      default: ({ row, $index }: any) => (
                        <TableTool.Op
                          flag={row.flag}
                          onOp={(op: OpType) => handleOp(op, row, $index)}
                        />
                      )
                    }}
                  </wui-table-column>
                </>
              ),
              empty: () => <TableTool.Empty />
            }}
          </wui-table>
        </div>
      </div>
    )
  }
})
