.box {
  height: 100%;
  padding: 20px;

  &_table {
    width: 100%;
    height: calc(100% - 40px);
    border: 1px solid #bbbbbb;
    background-color: #ffffff;

    &_btn {
      display: flex;
      justify-content: space-around;
      align-items: center;
      span {
        display: inline-block;
        width: 70px;
        height: 30px;
        line-height: 30px;
        text-align: center;
        cursor: pointer;
        border: 1px solid #d4d4d4;
        border-radius: 4px;
      }
    }
    &_empty {
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}
.modelBox {
  display: flex;
  gap: 1px;

  &_btn {
    width: 98px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    cursor: pointer;

    &:last-child {
      margin-left: 6px;
    }
    &:active {
      background-color: #cecece;
      transform: scale(0.98);
      transition: transform 0.1s ease-in-out;
    }
  }
}
.form {
  &_box {
    display: flex;
    margin-bottom: 10px;
    &_content {
      width: 200px;
      display: flex;
      align-items: baseline;
      span {
        display: inline-block;
      }
    }
    &_change {
      width: auto;
      align-items: center;
    }
  }
}
