<template>
  <MyDialog v-model="isActive" title="Table Group: viscosity" width="700px" @ok="onSubmit">
    <div class="tabs">
      <wui-button>Operators</wui-button>
      <wui-button style="margin-left: 6px">Functions</wui-button>
      <wui-button style="margin-left: 6px" @click="onUseDialog">Parameters</wui-button>
    </div>
    <div :class="styles.modelBox">
      <wui-table
        border
        :data="tableData"
        style="width: 100%"
        height="300px"
        :header-cell-style="{
          background: '#EAF1FD',
          color: '#90AFE4',
          fontSize: '18px',
          fontWeight: 'bold'
        }"
      >
        <wui-table-column label="Name" min-width="120px" align="center" show-overflow-tooltip>
          <template #default="{ row }">
            <wui-input
              v-if="row.flag"
              v-model="row.signal_parameter"
              clearable
              placeholder="Please input"
              style="width: 100%; margin-left: 0; height: 32px"
            />
            <span v-else>{{ row.signal_parameter }}</span>
          </template>
        </wui-table-column>
        <wui-table-column label="Equation" min-width="150px" align="center" show-overflow-tooltip>
          <template #default="{ row }">
            <div v-if="row.flag" :class="styles.modelBox_range">
              <wui-input
                v-model="row.signal_signalRange.min"
                clearable
                placeholder="Please input"
                style="width: 100%; margin-left: 0; height: 32px"
                @input="onInput($event, row, 'signal_signalRange', 'min')"
              />
              <wui-input
                v-model="row.signal_signalRange.max"
                clearable
                placeholder="Please input"
                style="width: 100%; margin-left: 0; height: 32px"
                @input="onInput($event, row, 'signal_signalRange', 'max')"
              />
            </div>
            <span v-else>{{ `${row.signal_signalRange.min}-${row.signal_signalRange.max}` }}</span>
          </template>
        </wui-table-column>

        <wui-table-column label="Op" fixed="right" width="100px" align="center">
          <template #default="{ row, $index }">
            <wui-icon>
              <EditPen />
            </wui-icon>
          </template>
        </wui-table-column>
        <template #empty>
          <div>
            <p>No Data</p>
          </div>
        </template>
      </wui-table>
    </div>
  </MyDialog>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import styles from '../index.module.scss'
import { useVModel } from '@vueuse/core'
import MyDialog from '@/renderer/components/dialog/index.vue'
import { WuiMessage } from '@wuk/wui'
import { EditPen } from '@element-plus/icons-vue'
import { useParameterDialog } from '@/renderer/utils/common'

interface rangeItem {
  min: number
  max: number
}

interface tableType {
  signal_parameter: string
  signal_signalRange: rangeItem
  signal_unitRange: rangeItem
  flag: boolean
  type: string
}
const props = defineProps({
  params: {
    type: Object,
    default: {}
  },
  modelShow: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelShow'])
const isActive = useVModel(props, 'modelShow', emit)
const tableData = ref<tableType[]>([
  {
    signal_parameter: 'VishayInstrState',
    signal_signalRange: {
      min: 0,
      max: 1
    },
    signal_unitRange: {
      min: 0,
      max: 1
    },
    flag: false,
    type: ''
  },
  {
    signal_parameter: 'VishayInstrError',
    signal_signalRange: {
      min: 0,
      max: 1
    },
    signal_unitRange: {
      min: 0,
      max: 1
    },
    flag: false,
    type: ''
  },
  {
    signal_parameter: 'VishayInstrStatus',
    signal_signalRange: {
      min: 0,
      max: 1
    },
    signal_unitRange: {
      min: 0,
      max: 1
    },
    flag: false,
    type: ''
  }
])
const onInput = (event: string, row: any, key: string, value: string) => {
  if (!/^[0-9]*\.?[0-9]*$/.test(event)) {
    WuiMessage({
      message: 'Please enter the number',
      type: 'warning',
      offset: 80
    })
    row[key][value] = 0
  }
}
const onSubmit = async () => {
  isActive.value = false
}
const onUseDialog = async () => {
  const result = await useParameterDialog()
  console.log(result)
}
</script>

<style lang="scss" scoped>
.tabs {
  :deep(.wui-button) {
    width: auto;
    margin-bottom: 10px;
    color: #181010;
    --wui-button-hover-border-color: #d3d3d3;
    --wui-button-hover-bg-color: rgba(250, 250, 250, 0.1);
    --wui-button-hover-text-color: #181010;
    --wui-button-active-border-color: #dcdfe6;
  }
}
</style>
