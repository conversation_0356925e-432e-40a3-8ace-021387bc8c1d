<template>
  <div :class="[b(), 'cfg-setup']">
    <div :class="e('header')">
      <operators-button :class="e('header', 'btn')" @select="handleSelect" />
      <functions-button @select="handleSelect" />
      <div>
        <wui-button @click="handleSelectParam">Parameters</wui-button>
      </div>
    </div>
    <wui-form
      ref="timerFormRef"
      label-width="0"
      label-position="left"
      hide-required-asterisk
      inline-message
      validate-box-gap="3"
      validate-placement="bottom"
      :class="[e('content'), 'cfg-setup_table']"
      :model="model"
      :rules="timerRules"
    >
      <wui-table
        show-overflow-tooltip
        :data="model.list"
        height="100%"
        border
        @row-contextmenu="handleRowMenu"
      >
        <wui-table-column prop="name" label="Name" min-width="200px" align="center">
          <template #default="{ row, $index }">
            <wui-form-item v-if="row.flag" :prop="`list.${$index}.name`" :rules="timerRules.name">
              <wui-input
                v-model="row.name"
                clearable
                @click="cancelPermanentFocus"
                placeholder="Please input name"
              />
            </wui-form-item>
            <span v-else>{{ row.name }}</span>
          </template>
        </wui-table-column>
        <wui-table-column
          prop="equation"
          label="Equation"
          min-width="120px"
          align="center"
          show-overflow-tooltip
        >
          <template #default="{ row, $index }">
            <wui-form-item
              v-if="row.flag"
              :prop="`list.${$index}.equation`"
              :rules="timerRules.equation"
            >
              <wui-input
                v-model="row.equation"
                clearable
                placeholder="Please input equation"
                @click="handleInputClick(row, 'equation', $event)"
              />
            </wui-form-item>
            <span v-else>{{ row.equation }}</span>
          </template>
        </wui-table-column>
        <wui-table-column label="Type" min-width="120px" align="center">
          <template #default="{ row }">
            <wui-select v-if="row.flag" v-model="row.type" placeholder="Select">
              <wui-option
                v-for="item in ResetOption"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </wui-select>
            <template v-else>{{ row.type === TimerType.Reset ? 'Reset' : 'No Reset' }}</template>
          </template>
        </wui-table-column>
        <wui-table-column label="Op" width="100px" align="center">
          <template #default="{ row, $index }">
            <TableTool.Op :flag="row.flag" @op="handleOp($event, row, $index)" />
          </template>
        </wui-table-column>
        <template #empty>
          <TableTool.Empty />
        </template>
      </wui-table>
    </wui-form>
  </div>
</template>

<script setup lang="tsx">
import { onMounted, reactive, ref, toRef } from 'vue'
import { WuiForm, WuiFormItem } from '@wuk/wui'
import TableTool, {
  BaseTableRow,
  OpType,
  RowType,
  isAddOrInsertType
} from '@/renderer/components/TableTool'
import OperatorsButton from '@/renderer/components/OperatorsButton/index.vue'
import FunctionsButton from '@/renderer/components/FunctionsButton/index.vue'
import {
  useBem,
  useBizEngine,
  useHandler,
  usePermanentFocus,
  useTableCommonMenu
} from '@/renderer/hooks'
import { useParameterDialog } from '@/renderer/utils/common'
import $styles from './index.module.scss'
import { TimerItem, TimerType } from '@wuk/cfg'
import { BizEngine } from '@/renderer/logic'
import { WuiMessage } from '@wuk/wui'
import { useTimerRules } from './rule'
const { e, b } = useBem('timers', $styles)
type TableRow = BaseTableRow<{
  name: string
  equation: string
  type: TimerType
}>
const timerFormRef = ref<InstanceType<typeof WuiForm>>()
const bizEngine = useBizEngine()
const ResetOption = [
  {
    label: 'Reset',
    value: TimerType.Reset
  },
  {
    label: 'No Reset',
    value: TimerType.NoReset
  }
]
const timerList = ref<TimerItem[]>([])
const { timerRules } = useTimerRules()
const model = reactive({
  list: [] as TableRow[]
})
const tipsMessage = () => {
  WuiMessage({
    message: 'success',
    type: 'success',
    offset: 90,
    grouping: true
  })
}
const createRow = (row_type: RowType) => ({
  name: '',
  equation: '',
  type: TimerType.Reset,
  flag: true,
  row_type
})
const { handleInputClick, cancelPermanentFocus, handleSelect, handleSelectParam } =
  usePermanentFocus<TableRow>()
const { handleRowMenu } = useTableCommonMenu(toRef(model, 'list'), (key, ...args) => {
  const { row, rowIndex } = args[0]
  switch (key) {
    case 'addKey':
      model.list.push(createRow('add'))
      break
    case 'insertKey':
      model.list.splice(rowIndex + 1, 0, createRow('insert'))
      break
    case 'deleteKey':
      handleOp('delete', row, rowIndex)
      break
    case 'modifyKey':
      handleOp('edit', row, rowIndex)
      break
    default:
      break
  }
})
const handleOp = (op: OpType, row: TableRow | undefined, index: number) => {
  if (!row) return
  switch (op) {
    case 'edit':
      row.flag = true
      break
    case 'cancel':
      handleCancel(row, index)
      break
    case 'delete':
      handleDelete(index)
      break
    case 'select':
      handleSubmit(row, index)
      break
    default:
      break
  }
}
const handleCancel = (row: TableRow, index: number) => {
  if (isAddOrInsertType(row.row_type)) {
    model.list.splice(index, 1)
    return
  }
  const timerItem = timerList.value[index]
  model.list[index] = {
    ...row,
    ...timerItem,
    flag: false
  }
}
const handleDelete = async (index: number) => {
  const res = await bizEngine.value?.removeTimer(index)
  model.list.splice(index, 1)
  if (!res) return
  tipsMessage()
}
const handleSubmit = async (row: TableRow, index: number) => {
  const keys = [`list.${index}.name`, `list.${index}.equation`]
  const valids = await timerFormRef.value?.validateField(keys)
  if (!valids) return
  let res: boolean | undefined
  const { flag, row_type, ...params } = row
  if (isAddOrInsertType(row_type)) {
    res = await bizEngine.value?.addTimer(params, index)
  } else {
    res = await bizEngine.value?.modifyTimer(index, params)
  }
  if (!res) return
  row.flag = false
  tipsMessage()
}
const readTimersOptions = async () => {
  let { list = [] } = (await bizEngine.value?.readTimersOptions()) || {}
  model.list = list.map(item => ({ ...item, flag: false, row_type: '*' }))
  timerList.value = list
}
useHandler(bizEngine, BizEngine.onTimersOptionsChanged, readTimersOptions)
onMounted(async () => {
  await readTimersOptions()
})
</script>
