.box {
  height: 100%;
  padding: 20px;
  display: flex;
  flex-direction: column;

  &_table {
    width: 100%;
    height: calc(100% - 40px);
    border: 1px solid #bbbbbb;
    background-color: #ffffff;

    &_btn {
      display: flex;
      justify-content: space-between;
      align-items: center;
      span {
        display: inline-block;
        width: 70px;
        height: 30px;
        line-height: 30px;
        text-align: center;
        cursor: pointer;
        border: 1px solid #d4d4d4;
        border-radius: 4px;
        transition: all 0.2s ease;

        &:hover {
          background-color: #f5f7fa;
          border-color: #409eff;
        }
      }
    }
  }
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 0 16px 0;
  margin-bottom: 16px;
  border-bottom: 1px solid #e4e7ed;

  &_left {
    display: flex;
    gap: 8px;
    align-items: center;
  }
}

.modelBox {
  &_item {
    display: flex;
    justify-content: space-between;
    align-items: baseline;
    margin-bottom: 5px;

    span {
      display: inline-block;
      width: 120px;
    }
  }
  &_title {
    margin: 20px 0 5px;
  }
  &_range {
    display: flex;
    gap: 5px;
  }
}
