import { WuiMessage } from '@wuk/wui'

export interface ValidationOptions {
  min: number
  max: number
  offset?: number
}

export class ValidationHelpers {
  private static showMessage(message: string, type: 'warning' | 'error', offset = 90): void {
    WuiMessage({
      message,
      type,
      offset
    })
  }

  static validateNumber(value: any): boolean {
    const stringValue = String(value)
    return stringValue !== '' && /^(\-|\+)?\d+(\.\d+)?$/.test(stringValue)
  }

  static validateRange(min: number, max: number, type: 'warning' | 'error' = 'error'): boolean {
    if (min >= max) {
      this.showMessage('Minimum value must be less than maximum value', type)
      return false
    }
    return true
  }

  static validateValueInRange(
    value: any,
    options: ValidationOptions,
    fieldName: string,
    type: 'warning' | 'error' = 'warning'
  ): boolean {
    const stringValue = String(value)

    if (stringValue === '' || stringValue === '0') return true

    if (!this.validateNumber(value)) {
      this.showMessage(`${fieldName} must be a valid number`, type, options.offset)
      return false
    }

    const numValue = Number(value)
    if (numValue < options.min || numValue > options.max) {
      this.showMessage(
        `${fieldName} (${numValue}) must be between ${options.min} and ${options.max}`,
        type,
        options.offset
      )
      return false
    }

    return true
  }

  static validateCoefficient(
    value: string,
    index: number,
    options: ValidationOptions,
    defaultValue: string,
    type: 'warning' | 'error' = 'warning'
  ): { isValid: boolean; shouldReset: boolean; resetValue?: string } {
    if (value === '') return { isValid: true, shouldReset: false }

    if (!this.validateNumber(value)) {
      this.showMessage('Coefficient must be a valid number', type, options.offset)
      return { isValid: false, shouldReset: true, resetValue: defaultValue }
    }

    const numValue = Number(value)
    if (numValue < options.min || numValue > options.max) {
      this.showMessage(
        `Coefficient value (${numValue}) must be between ${options.min} and ${options.max}`,
        type,
        options.offset
      )
      return { isValid: false, shouldReset: false }
    }

    return { isValid: true, shouldReset: false }
  }

  static validateAllCoefficients(
    coeffs: string[],
    degree: number,
    options: ValidationOptions,
    type: 'warning' | 'error' = 'error'
  ): boolean {
    for (let i = 0; i <= degree; i++) {
      const value = coeffs[i]

      if (value === '') continue

      if (!this.validateNumber(value)) {
        this.showMessage(
          `Coefficient at position ${i} must be a valid number`,
          type,
          options.offset
        )
        return false
      }

      const numValue = Number(value)
      if (numValue < options.min || numValue > options.max) {
        this.showMessage(
          `Coefficient at position ${i} (${numValue}) must be between ${options.min} and ${options.max}`,
          type,
          options.offset
        )
        return false
      }
    }
    return true
  }

  static validateTableData(
    data: Array<{ x: any; y: any }>,
    options: ValidationOptions,
    type: 'warning' | 'error' = 'error'
  ): boolean {
    if (data.length === 0) {
      this.showMessage('Please add at least one row of data', type, options.offset)
      return false
    }

    for (let i = 0; i < data.length; i++) {
      const row = data[i]

      const xValid = this.validateValueInRange(row.x, options, `Row ${i + 1} X value`, type)

      if (!xValid) return false

      const yValid = this.validateValueInRange(row.y, options, `Row ${i + 1} Y value`, type)

      if (!yValid) return false
    }

    return true
  }
}
