import { Tree } from '@/renderer/components/TreeContent'
import { SetupOptions } from '@wuk/cfg'
import { InjectionKey } from 'vue'

export type CurEditVibInfo = {
  label: string
  vibId: number
  index?: number
  groupNodeIndex?: number
  children?: VibTreeChild[]
}

export interface VibTreeChild extends Omit<Tree<number>, 'children'> {
  originData: SetupOptions
}

interface vibrationConfig {
  devicePtr: any
  curEditVibInfo: CurEditVibInfo
  changeTreeNode: (id: number) => void
}

export const contextKey: InjectionKey<vibrationConfig> = Symbol('vibrationConfig')
