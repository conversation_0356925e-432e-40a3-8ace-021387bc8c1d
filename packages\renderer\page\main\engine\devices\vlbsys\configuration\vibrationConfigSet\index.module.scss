$namespace: 'vibrationConfigSet';

.#{$namespace} {
  padding: 20px;
  box-sizing: border-box;
  height: 100%;
  display: flex;
  flex-direction: column;

  &_search-bar {
    &_wrapper {
      position: relative;
      display: inline-block;
    }

    &_icon {
      position: absolute;
      right: 10px;
      top: 50%;
      transform: translateY(-50%);
      pointer-events: none;

      img {
        width: 16px;
        height: 16px;
        opacity: 0.6;
      }
    }
  }

  &_layout {
    flex: 1;
    display: flex;
    height: 100%;
    gap: 20px;

    &_sidebar {
      width: 250px;
      min-width: 250px;
      border-radius: 8px;
      box-sizing: border-box;
      height: 100%;
      position: sticky;
      top: 0;

      &_navigation {
        background: rgb(245, 246, 248);
      }
    }

    &_main {
      flex: 1;
      display: flex;
      flex-direction: column;
      background: #fff;

      &_scroll-container {
        flex: 1;
        overflow-y: auto;
        padding-right: 10px;

        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* IE and Edge */

        &::-webkit-scrollbar {
          display: none; /* Chrome, Safari, Opera */
        }
      }

      &_section {
        padding: 20px;
        border-radius: 8px;
        background: #ffffff;

        &:last-child {
          margin-bottom: 0;
        }

        h2 {
          font-size: 18px;
          font-weight: 600;
          color: #333;
          padding-bottom: 12px;
        }
      }
    }
  }
}

.mb_5 {
  &:hover {
    background-color: #f8f9fa;
    border-radius: 6px;
    transition: background-color 0.2s ease;
  }
  h2 {
    margin: 0;
  }
  .extension {
    display: flex;
    align-items: center;
    min-height: 60px;
    border-radius: 6px;
    color: #1a1a1a;
    gap: 16px;

    h4 {
      margin: 0;
      font-weight: 600;
      color: #333;
      width: 270px;
      flex-shrink: 0;
      text-align: left;
    }
  }
}
