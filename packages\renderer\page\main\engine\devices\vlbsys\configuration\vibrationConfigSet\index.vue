<template>
  <div :class="b()">
    <div :class="e('search-bar')">
      <div :class="e('search-bar', 'wrapper')">
        <wui-input v-model="searchKeyword" placeholder="Search Setting" />
        <div :class="e('search-bar', 'icon')">
          <img src="/packages/renderer/assets/search.png" alt="" />
        </div>
      </div>
    </div>

    <div :class="e('layout')">
      <div :class="e('layout', 'sidebar')">
        <wui-anchor
          ref="anchorRef"
          :class="e('layout', 'sidebar', 'navigation')"
          :container="containerRef"
          :offset="30"
        >
          <wui-anchor-link
            v-for="section in sections"
            :key="section.id"
            :href="section.href"
            :title="section.title"
            @click="handleClick"
          />
        </wui-anchor>
      </div>

      <div :class="e('layout', 'main')">
        <div ref="containerRef" :class="e('layout', 'main', 'scroll-container')">
          <template v-for="section in sections" :key="section.id">
            <component
              :is="section.component"
              :id="section.id"
              :class="e('layout', 'main', 'section')"
              :current-table-index="currentTableIndex"
            />
            <wui-divider border-style="dashed" />
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, onMounted, nextTick, inject, watchEffect } from 'vue'
import $styles from './index.module.scss'
import VibrationSystem from './vibrationSystem/index.vue'
import VibrationInput from './vibrationInput/index.vue'
import BroadbandFilter from './broadbandFilter/index.vue'
import TachometerInput from './tachometerInput/index.vue'
import SpectrumAnalysis from './spectrumAnalysis/index.vue'
import TrackingFilter from './trackingFilter/index.vue'
import { contextKey } from '../consts'
import { useBem } from '@/renderer/hooks'

const containerRef = ref<HTMLElement | null>(null)
const searchKeyword = ref('')

const vibContext = inject(contextKey)

const { e, b } = useBem('vibrationConfigSet', $styles)

const currentTableIndex = ref<number>(0)

const handleClick = (e?: MouseEvent) => {
  if (e) {
    ;(e as MouseEvent).preventDefault()
  }
}

const sections = [
  { id: 'system', href: '#system', title: 'Vibration System Setup', component: VibrationSystem },
  { id: 'input', href: '#input', title: 'Vibration Input Setup', component: VibrationInput },
  {
    id: 'broadband',
    href: '#broadband',
    title: 'Broadband Filter Setup',
    component: BroadbandFilter
  },
  {
    id: 'tachometer',
    href: '#tachometer',
    title: 'Tachometer Input Setup',
    component: TachometerInput
  },
  {
    id: 'spectrum',
    href: '#spectrum',
    title: 'Spectrum Analysis Setup',
    component: SpectrumAnalysis
  },
  {
    id: 'tracking',
    href: '#tracking',
    title: 'Tracking Filter Setup',
    component: TrackingFilter
  }
]

const anchorRef = ref()
onMounted(() => {
  nextTick(() => {
    window.scrollTo(0, 0)
    anchorRef.value.scrollTo('#system')
  })
})

watch(
  () => searchKeyword?.value,
  newKeyword => {
    if (!newKeyword) return
    const matchedSection = sections.find(section =>
      section.title.toLowerCase().trim().includes(newKeyword.toLowerCase())
    )
    if (matchedSection && containerRef.value) {
      const targetElement = document.querySelector(`#${matchedSection.id}`) as HTMLElement
      if (targetElement) {
        containerRef.value.scrollTop = targetElement.offsetTop - 100
        const anchorLink = document.querySelector(`a[href="#${matchedSection.id}"]`)
        if (anchorLink) {
          handleClick()
        }
      }
    }
  }
)

const getTableIndex = () => {
  const { groupNodeIndex, children } = vibContext?.curEditVibInfo || {}
  if (groupNodeIndex === undefined || !children) return
  currentTableIndex.value = groupNodeIndex
}

watchEffect(getTableIndex)
</script>

<style lang="scss" scoped>
:deep(.wui-input-number .wui-input__inner) {
  text-align: left;
}
:deep(.wui-input-number) {
  width: 220px;
}
:deep(.wui-input) {
  width: 220px;
}
:deep(.wui-select) {
  width: 220px;
}
:deep(.wui-anchor__list) {
  background: rgb(245, 246, 248);
}
</style>
