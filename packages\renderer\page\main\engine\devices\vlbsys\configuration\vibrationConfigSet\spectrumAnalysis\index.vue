<template>
  <div>
    <h2>Spectrum Analysis Setup</h2>
    <div :class="styles.mb_5">
      <div :class="styles.extension">
        <h4>Spectrum Analysis</h4>
        <div style="position: relative">
          <wui-select v-model="currentIndex" placeholder="Select" @change="onSpectrumChange">
            <wui-option
              v-for="item in spectrumAnalysisOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </wui-select>
        </div>
      </div>
    </div>
    <div :class="styles.mb_5">
      <div :class="styles.extension">
        <h4>Output Units</h4>
        <div style="position: relative">
          <wui-select
            v-model="currentSpectrum.output_units"
            placeholder="Select"
            @change="onChange('output_units', $event)"
          >
            <wui-option
              v-for="item in outputUnitOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </wui-select>
          <SuccessIndicator :show="!!successStates.output_units" />
        </div>
      </div>
    </div>
    <div :class="styles.mb_5">
      <div :class="styles.extension">
        <h4>Detector Type</h4>
        <div style="position: relative">
          <wui-select
            v-model="currentSpectrum.detector_type"
            placeholder="Select"
            @change="onChange('detector_type', $event)"
          >
            <wui-option
              v-for="item in detectorTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </wui-select>
          <SuccessIndicator :show="!!successStates.detector_type" />
        </div>
      </div>
    </div>
    <div :class="styles.mb_5">
      <div :class="styles.extension">
        <h4>Start Frequency</h4>
        <div style="position: relative">
          <wui-input-number
            v-model="currentSpectrum.start_frequency"
            :min="0"
            :max="9999"
            clearable
            :controls="false"
            placeholder="Please input"
            @change="onChange('start_frequency', $event)"
          />
          <SuccessIndicator :show="!!successStates.start_frequency" />
        </div>
      </div>
    </div>
    <div :class="styles.mb_5">
      <div :class="styles.extension">
        <h4>End Frequency</h4>
        <div style="position: relative">
          <wui-input-number
            v-model="currentSpectrum.end_frequency"
            :min="1"
            :max="10000"
            clearable
            :controls="false"
            placeholder="Please input"
            @change="onChange('end_frequency', $event)"
          />
          <SuccessIndicator :show="!!successStates.end_frequency" />
        </div>
      </div>
    </div>
    <div :class="styles.mb_5">
      <div :class="styles.extension">
        <h4>Full Scale Units</h4>
        <div style="position: relative">
          <wui-input-number
            v-model="currentSpectrum.full_scale_units"
            :min="0.1"
            :max="150.0"
            clearable
            :precision="1"
            :controls="false"
            placeholder="Please input"
            @change="onChange('full_scale_units', $event)"
          />
          <SuccessIndicator :show="!!successStates.full_scale_units" />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, watchEffect } from 'vue'
import styles from '../index.module.scss'
import { BizEngine } from '@/renderer/logic'
import { useCore, useHandler, useSettingSuccess } from '@/renderer/hooks'
import { VibSpectrumOption } from '@wuk/cfg'
import SuccessIndicator from '@/renderer/components/SuccessIndicator/index.vue'

const homePtr = useCore<BizEngine>(BizEngine)
const { successStates, markSuccess } = useSettingSuccess()

const currentIndex = ref<number>(0)

const currentSpectrum = ref<VibSpectrumOption>({
  output_units: 0,
  start_frequency: 0,
  end_frequency: 1,
  detector_type: 0,
  full_scale_units: 0.1
})

const spectrumAnalysisList = ref<VibSpectrumOption[]>([])

const props = defineProps({
  currentTableIndex: {
    type: Number,
    default: -1
  }
})

const spectrumAnalysisOptions = computed(() => {
  return spectrumAnalysisList.value.map((_, index) => ({
    label: `${index + 1}`,
    value: index
  }))
})

const onSpectrumChange = (index: number) => {
  currentIndex.value = index
  if (spectrumAnalysisList.value[index]) {
    currentSpectrum.value = { ...spectrumAnalysisList.value[index] }
  }
}

watch(currentIndex, newIndex => {
  if (spectrumAnalysisList.value[newIndex]) {
    currentSpectrum.value = { ...spectrumAnalysisList.value[newIndex] }
  }
})

const outputUnitOptions = [
  {
    label: 'Disabled',
    value: 0
  },
  {
    label: `Acceleration(g's)`,
    value: 1
  },
  {
    label: 'Velocity(IPS)',
    value: 2
  },
  {
    label: 'Displacement(Mils)',
    value: 3
  },
  {
    label: 'Acceleration(m/sec2)',
    value: 4
  },
  {
    label: 'Velocity(mm/sec)',
    value: 5
  },
  {
    label: 'Displacement(um)',
    value: 6
  }
]
const detectorTypeOptions = [
  {
    label: 'Peak',
    value: 0
  },
  {
    label: 'Peak to Peak',
    value: 1
  },
  {
    label: 'RMS',
    value: 2
  },
  {
    label: 'Average',
    value: 3
  }
]
const onChange = async (key: string, value: string | number) => {
  const result = await homePtr.value?.writeVibSpectrum(
    props.currentTableIndex,
    currentIndex.value,
    { [key]: value }
  )
  if (!result) return

  markSuccess(key)
}

const getDataInfo = async () => {
  if (props.currentTableIndex === -1) return
  spectrumAnalysisList.value =
    (await homePtr.value?.readVibSpectrum(props.currentTableIndex)) || ([] as VibSpectrumOption[])
  currentSpectrum.value = spectrumAnalysisList.value[currentIndex.value] || {
    output_units: 0,
    start_frequency: 0,
    end_frequency: 1,
    detector_type: 0,
    full_scale_units: 0.1
  }
}

useHandler(homePtr, BizEngine.onSpectrumAnalysisSetupChanged, getDataInfo)

watchEffect(getDataInfo)
</script>
