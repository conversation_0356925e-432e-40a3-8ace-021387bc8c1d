<template>
  <div>
    <h2>Tachometer Input Setup</h2>
    <div :class="styles.mb_5">
      <div :class="styles.extension">
        <h4>Tachometer Input</h4>
        <div style="position: relative">
          <wui-select v-model="currentIndex" placeholder="Select" @change="onTachometerChange">
            <wui-option
              v-for="item in tachometerInputOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </wui-select>
        </div>
      </div>
    </div>
    <div :class="styles.mb_5">
      <div :class="styles.extension">
        <h4>Tachometer 1:100% Rotor Speed (RPM) (enter 0 to disable)</h4>
        <div style="position: relative">
          <wui-input-number
            v-model="currentTach.rotor_speed"
            :min="0"
            :max="100000"
            clearable
            :precision="1"
            :controls="false"
            placeholder="Please input"
            @change="onChange('rotor_speed', $event)"
          />
          <SuccessIndicator :show="!!successStates.rotor_speed" />
        </div>
      </div>
    </div>
    <div :class="styles.mb_5">
      <div :class="styles.extension">
        <h4>Tachometer 1:100% Frequency (Hz)</h4>
        <div style="position: relative">
          <wui-input-number
            v-model="currentTach.tach_freq"
            :min="1"
            :max="100000"
            :precision="1"
            clearable
            :controls="false"
            placeholder="Please input"
            @change="onChange('tach_freq', $event)"
          />
          <SuccessIndicator :show="!!successStates.tach_freq" />
        </div>
      </div>
    </div>
    <div :class="styles.mb_5">
      <div :class="styles.extension">
        <h4>Tachometer 1:Sensitivity (V)</h4>
        <div style="position: relative">
          <wui-input-number
            v-model="currentTach.sensitivity"
            :min="0.0"
            :max="1.1"
            clearable
            :precision="1"
            :controls="false"
            placeholder="Please input"
            @change="onChange('sensitivity', $event)"
          />
          <SuccessIndicator :show="!!successStates.sensitivity" />
        </div>
      </div>
    </div>
    <div :class="styles.mb_5">
      <div :class="styles.extension">
        <h4>Tachometer Processing</h4>
        <div style="position: relative">
          <wui-select
            v-model="currentTach.tach_processing"
            placeholder="Select"
            @change="onChange('tach_processing', $event)"
          >
            <wui-option
              v-for="item in tachProcessOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </wui-select>
          <SuccessIndicator :show="!!successStates.tach_processing" />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, watchEffect } from 'vue'
import styles from '../index.module.scss'
import { BizEngine } from '@/renderer/logic'
import { useCore, useHandler, useSettingSuccess } from '@/renderer/hooks'
import { VibTachInputOption } from '@wuk/cfg'
import SuccessIndicator from '@/renderer/components/SuccessIndicator/index.vue'

const homePtr = useCore<BizEngine>(BizEngine)
const { successStates, markSuccess } = useSettingSuccess()

const currentIndex = ref<number>(0)

const currentTach = ref<VibTachInputOption>({
  tach_freq: 1,
  rotor_speed: 0,
  sensitivity: 0.0,
  tach_processing: 0
})

const tachometerInputList = ref<VibTachInputOption[]>([])

const props = defineProps({
  currentTableIndex: {
    type: Number,
    default: -1
  }
})

const tachometerInputOptions = computed(() => {
  return tachometerInputList.value.map((_, index) => ({
    label: `${index + 1}`,
    value: index
  }))
})

const tachProcessOptions = [
  {
    label: 'Disabled',
    value: 0
  },
  {
    label: 'Encode High Pulse',
    value: 1
  },
  {
    label: 'Encode Low Pulse',
    value: 2
  },
  {
    label: 'Single Pulse or Strobe',
    value: 3
  }
]

const onTachometerChange = (index: number) => {
  currentIndex.value = index
  if (tachometerInputList.value[index]) {
    currentTach.value = { ...tachometerInputList.value[index] }
  }
}

watch(currentIndex, newIndex => {
  if (tachometerInputList.value[newIndex]) {
    currentTach.value = { ...tachometerInputList.value[newIndex] }
  }
})

const onChange = async (key: string, value: string | number) => {
  const result = await homePtr.value?.writeVibTachInput(
    props.currentTableIndex,
    currentIndex.value,
    { [key]: value }
  )
  if (!result) return

  markSuccess(key)
}

const getDataInfo = async () => {
  if (props.currentTableIndex === -1) return
  tachometerInputList.value =
    (await homePtr.value?.readVibTachInput(props.currentTableIndex)) || ([] as VibTachInputOption[])
  currentTach.value = tachometerInputList.value[currentIndex.value] || {
    tach_freq: 1,
    rotor_speed: 0,
    sensitivity: 0.0,
    tach_processing: 0
  }
}

useHandler(homePtr, BizEngine.onTachometerInputSetupChanged, getDataInfo)

watchEffect(getDataInfo)
</script>
