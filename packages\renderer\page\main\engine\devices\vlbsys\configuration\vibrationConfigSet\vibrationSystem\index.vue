<template>
  <div>
    <h2>Vibration System Setup</h2>
    <div :class="styles.mb_5">
      <div :class="styles.extension">
        <h4>Measurement Rate</h4>
        <div style="position: relative">
          <wui-select
            v-model="systemData.measurement_rate"
            placeholder="Select"
            @change="onChange('measurement_rate', $event)"
          >
            <wui-option
              v-for="item in measurementRateOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </wui-select>
          <SuccessIndicator :show="!!successStates.measurement_rate" />
        </div>
      </div>
    </div>
    <div :class="styles.mb_5">
      <div :class="styles.extension">
        <h4>Bandwidth</h4>
        <div style="position: relative">
          <wui-select
            v-model="systemData.band_width"
            placeholder="Select"
            @change="onChange('band_width', $event)"
          >
            <wui-option
              v-for="item in BandwidthOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </wui-select>
          <SuccessIndicator :show="!!successStates.band_width" />
        </div>
      </div>
    </div>
    <div :class="styles.mb_5">
      <div :class="styles.extension">
        <h4>FFT Lines</h4>
        <div style="position: relative">
          <wui-select
            v-model="systemData.fft_lines"
            placeholder="Select"
            @change="onChange('fft_lines', $event)"
          >
            <wui-option
              v-for="item in FFTLineOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </wui-select>
          <SuccessIndicator :show="!!successStates.fft_lines" />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watchEffect } from 'vue'
import styles from '../index.module.scss'
import { useBizEngine, useSettingSuccess } from '@/renderer/hooks'
import { VibSystemOption } from '@wuk/cfg'
import SuccessIndicator from '@/renderer/components/SuccessIndicator/index.vue'

const homePtr = useBizEngine()
const { successStates, markSuccess } = useSettingSuccess()
const systemData = ref<VibSystemOption>({
  measurement_rate: 0,
  band_width: 0,
  fft_lines: 0
})

const props = defineProps({
  currentTableIndex: {
    type: Number,
    default: -1
  }
})
const measurementRateOptions = [
  {
    label: '1 Hz',
    value: 0
  },
  {
    label: '2 Hz',
    value: 1
  },
  {
    label: '3 Hz',
    value: 2
  },
  {
    label: '4 Hz',
    value: 3
  },
  {
    label: '5 Hz',
    value: 4
  },
  {
    label: '6 Hz',
    value: 5
  },
  {
    label: '7 Hz',
    value: 6
  },
  {
    label: '8 Hz',
    value: 7
  },
  {
    label: '9 Hz',
    value: 8
  },
  {
    label: '10 Hz',
    value: 9
  }
]
const BandwidthOptions = [
  {
    label: '500 Hz',
    value: 0
  },
  {
    label: '1000 Hz',
    value: 1
  },
  {
    label: '2000 Hz',
    value: 2
  },
  {
    label: '5000 Hz',
    value: 3
  },
  {
    label: '10000 Hz',
    value: 4
  }
]
const FFTLineOptions = [
  {
    label: '200 lines',
    value: 0
  },
  {
    label: '400 lines',
    value: 1
  },
  {
    label: '800 lines',
    value: 2
  },
  {
    label: '1600 lines',
    value: 3
  }
]
const onChange = async (key: string, value: string | number) => {
  const result = await homePtr.value?.writeVibSystem(props.currentTableIndex, { [key]: value })
  if (!result) return
  markSuccess(key)
}
watchEffect(async () => {
  if (props.currentTableIndex === -1) return
  systemData.value = (await homePtr.value?.readVibSystem(props.currentTableIndex)) || {
    measurement_rate: 0,
    band_width: 0,
    fft_lines: 0
  }
})
</script>
