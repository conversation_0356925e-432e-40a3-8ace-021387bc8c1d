.box {
  height: 100%;
  padding: 20px;
  display: flex;
  flex-direction: column;

  &_table {
    width: 100%;
    height: calc(100% - 121px);
    border: 1px solid #bbbbbb;
    background-color: #ffffff;

    &_empty {
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    &_btn {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 0 16px 0;
  margin-bottom: 16px;
  border-bottom: 1px solid #e4e7ed;

  &_left {
    display: flex;
    gap: 8px;
    align-items: center;
  }
}
.modelBox {
  margin: 0 20px;

  &_item {
    display: flex;
    justify-content: space-between;
    align-items: baseline;
    margin-bottom: 5px;

    span {
      display: inline-block;
      width: 120px;
    }
  }
  &_title {
    margin: 20px 0 5px;
  }
}
