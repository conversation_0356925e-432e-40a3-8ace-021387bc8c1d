<template>
  <div :class="styles.box">
    <div :class="styles.toolbar">
      <div :class="styles.toolbar_left">
        <wui-button type="primary" @click="onAdd">
          <wui-icon><Plus /></wui-icon>
          Add
        </wui-button>
      </div>
    </div>

    <div :class="styles.box_table">
      <wui-table
        :data="signalsList"
        border
        height="100%"
        :header-cell-style="{
          background: '#EAF1FD',
          color: '#90AFE4',
          fontSize: '18px',
          fontWeight: 'bold'
        }"
        @row-contextmenu="handleRowMenu"
      >
        <wui-table-column
          prop="name"
          label="Sig Name"
          min-width="120px"
          align="center"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <wui-input
              v-if="row.flag"
              v-model="row.name"
              clearable
              placeholder="Please input"
              style="width: 100%; margin-left: 0; height: 32px"
            />
            <span v-else>{{ row.name }}</span>
          </template>
        </wui-table-column>
        <wui-table-column label="Filter Type" min-width="120px" align="center">
          <template #default="{ row }">
            <wui-select
              v-if="row.flag"
              v-model="row.filter_type"
              placeholder="Select"
              style="width: 100%; margin-left: 0"
              @change="onFilterTypeChange(row, $event)"
            >
              <wui-option
                v-for="item in getFilteredFilterTypeOptions(row)"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </wui-select>
            <span v-else>{{ convertType(options1, row.filter_type) }}</span>
          </template>
        </wui-table-column>
        <wui-table-column label="Sig Type" min-width="120px" align="center">
          <template #default="{ row }">
            <wui-select
              v-if="row.flag"
              v-model="row.sig_type"
              placeholder="Select"
              style="width: 100%; margin-left: 0"
            >
              <wui-option
                v-for="item in getFilteredSigTypeOptions(row.filter_type, row)"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </wui-select>
            <span v-else>{{ convertType(options2, row.sig_type) }}</span>
          </template>
        </wui-table-column>
        <wui-table-column prop="calibMode" label="Calib Mode" width="150px" align="center">
          <template #default="{ row }">
            <wui-select
              v-if="row.flag"
              v-model="row.calib_mode"
              placeholder="Select"
              style="width: 100%; margin-left: 0"
            >
              <wui-option
                v-for="item in options3"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </wui-select>
            <span v-else>{{ convertType(options3, row.calib_mode) }}</span>
          </template>
        </wui-table-column>
        <wui-table-column label="Op" fixed="right" width="100px" align="center">
          <template #default="{ row, $index }">
            <TableTool.Op :flag="row.flag" @op="handleOp($event, row, $index)" />
          </template>
        </wui-table-column>
        <template #empty>
          <TableTool.Empty />
        </template>
      </wui-table>
    </div>
  </div>
</template>

<script lang="ts" setup>
import styles from './index.module.scss'
import { ref, onMounted } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import { useBizEngine, useHandler, useTableCommonMenu } from '@/renderer/hooks'
import { BizEngine } from '@/renderer/logic'
import { WuiMessage } from '@wuk/wui'
import { VibSignalOption } from '@wuk/cfg'
import { convertType } from '@/renderer/utils/common'
import TableTool, { OpType, isAddOrInsertType, RowType } from '@/renderer/components/TableTool'

interface newVibSignalItem extends VibSignalOption {
  meta?: VibSignalOption
  flag: boolean
  row_type: RowType
}
const options1 = [
  {
    label: 'Broadband 1',
    value: 1
  },
  {
    label: 'Broadband 2',
    value: 2
  },
  {
    label: 'Broadband 3',
    value: 3
  },
  {
    label: 'Broadband 4',
    value: 4
  },
  {
    label: 'Tracking 1',
    value: 5
  },
  {
    label: 'Tracking 2',
    value: 6
  },
  {
    label: 'Tracking 3',
    value: 7
  },
  {
    label: 'Tracking 4',
    value: 8
  },
  {
    label: 'Tracking 5',
    value: 9
  },
  {
    label: 'Tracking 6',
    value: 10
  },
  {
    label: 'Tracking 7',
    value: 11
  },
  {
    label: 'Tracking 8',
    value: 12
  },
  {
    label: 'Tracking 9',
    value: 13
  },
  {
    label: 'Tracking 10',
    value: 14
  },
  {
    label: 'Tracking 11',
    value: 15
  },
  {
    label: 'Tracking 12',
    value: 16
  },
  {
    label: 'Tracking 13',
    value: 17
  },
  {
    label: 'Tracking 14',
    value: 18
  },
  {
    label: 'Tracking 15',
    value: 19
  },
  {
    label: 'Tracking 16',
    value: 20
  }
]
const options2 = [
  {
    label: 'Amplitude',
    value: 0
  },
  {
    label: 'Frequency',
    value: 1
  },
  {
    label: 'Phase',
    value: 2
  },
  {
    label: 'RPM',
    value: 3
  }
]
const options3 = [
  {
    label: 'None',
    value: 1
  },
  {
    label: 'VerifyOnly',
    value: 2
  }
]
const signalsPtr = useBizEngine()
const signalsList = ref<newVibSignalItem[]>([])
const createRow = (row_type: RowType): any => ({
  name: '',
  flag: true,
  row_type
})

const getUsedCombinations = (currentRow: newVibSignalItem) => {
  return signalsList.value
    .filter(row => row !== currentRow && !row.flag)
    .map(row => ({ filter_type: row.filter_type, sig_type: row.sig_type }))
}

const getUsedSigTypesForFilterType = (filterType: number, currentRow: newVibSignalItem) => {
  const usedCombinations = getUsedCombinations(currentRow)
  return usedCombinations
    .filter(combo => combo.filter_type === filterType)
    .map(combo => combo.sig_type)
}

const getFilteredFilterTypeOptions = (currentRow: newVibSignalItem) => {
  return options1
}

const getFilteredSigTypeOptions = (filterType: number, currentRow?: newVibSignalItem) => {
  let availableOptions = options2

  if (filterType >= 1 && filterType <= 4) {
    availableOptions = options2.filter(option => option.value === 0)
  }

  if (currentRow) {
    const usedSigTypesForThisFilterType = getUsedSigTypesForFilterType(filterType, currentRow)
    availableOptions = availableOptions.filter(
      option => !usedSigTypesForThisFilterType.includes(option.value)
    )
  }
  return availableOptions
}

const onFilterTypeChange = (row: newVibSignalItem, filterType: number) => {
  const availableSigTypes = getFilteredSigTypeOptions(filterType, row)
  const currentSigTypeAvailable = availableSigTypes.some(option => option.value === row.sig_type)
  if (!currentSigTypeAvailable && availableSigTypes.length > 0) {
    row.sig_type = availableSigTypes[0].value
  }

  if (filterType >= 1 && filterType <= 4) {
    row.sig_type = 0
  }
}
const tipsMessage = () => {
  WuiMessage({
    message: 'success',
    type: 'success',
    offset: 80
  })
}
const { handleRowMenu } = useTableCommonMenu(
  signalsList,
  async (key, ...args) => {
    const { row, rowIndex } = args[0]
    switch (key) {
      case 'addKey':
        const newRow = createRow('add')
        signalsList.value.push(newRow)
        break
      case 'insertKey':
        const insertRow = createRow('insert')
        signalsList.value.splice(rowIndex + 1, 0, insertRow)
        break
      case 'modifyKey':
        handleOp('edit', row, rowIndex)
        break
      case 'deleteKey':
        handleOp('delete', row, rowIndex)
        break
      default:
        break
    }
  },
  [1],
  [
    { key: 'addKey', label: 'add' },
    { key: 'insertKey', label: 'insert' },
    { key: 'modifyKey', label: 'modify' },
    { key: 'deleteKey', label: 'delete' }
  ]
)

const onAdd = () => {
  signalsList.value.push(createRow('add'))
}

// 操作处理函数
const handleOp = async (op: OpType, row: newVibSignalItem | undefined, index: number) => {
  if (!row) return
  switch (op) {
    case 'edit':
      if (row.flag) return
      row.meta = { ...row }
      row.flag = true
      break
    case 'delete':
      const removeResult = await signalsPtr.value?.removeVibSignal(index)
      if (!removeResult) return
      tipsMessage()
      break
    case 'select':
      await onConfirm(row, index)
      break
    case 'cancel':
      onClose(row, index)
      break
  }
}
// 取消事件
const onCancel = (item: newVibSignalItem, index: number) => {
  const { row_type, meta = {} } = item
  if (isAddOrInsertType(row_type)) {
    signalsList.value.splice(index, 1)
  } else if (meta) {
    // 恢复原始数据
    Object.assign(item, meta)
    item.flag = false
    delete item.meta
  }
}

// 关闭事件（保留用于右键菜单兼容性）
const onClose = (item: newVibSignalItem, index: number) => {
  onCancel(item, index)
}
const validateUniqueSelection = (item: newVibSignalItem, index: number) => {
  const otherRows = signalsList.value.filter((row, idx) => idx !== index && !row.flag)

  const duplicateCombination = otherRows.find(
    row => row.filter_type === item.filter_type && row.sig_type === item.sig_type
  )

  if (duplicateCombination) {
    const filterTypeLabel = options1.find(opt => opt.value === item.filter_type)?.label
    const sigTypeLabel = options2.find(opt => opt.value === item.sig_type)?.label
    WuiMessage({
      message: `The combination of "${filterTypeLabel}" and "${sigTypeLabel}" is already selected`,
      type: 'warning',
      offset: 80
    })
    return false
  }

  return true
}

// 提交事件
const onConfirm = async (item: newVibSignalItem, index: number) => {
  const { name, filter_type, sig_type, calib_mode, row_type } = item
  if (!name) {
    WuiMessage({
      message: 'Data cannot be empty',
      type: 'warning',
      offset: 80
    })
    return
  }

  // 验证唯一性
  if (!validateUniqueSelection(item, index)) {
    return
  }

  const data = { name, filter_type, sig_type, calib_mode }
  let editResult
  if (isAddOrInsertType(row_type)) {
    editResult = await signalsPtr.value?.addVibSignal(
      data,
      row_type === 'insert' ? index : undefined
    )
  } else {
    editResult = await signalsPtr.value?.modifyVibSignal(index, data)
  }
  if (!editResult) return
  item.row_type = '*'
  item.flag = false
  tipsMessage()
}
// 数据处理
const getDataInfo = async () => {
  const list = (await signalsPtr.value?.readVibSignals()) || []
  signalsList.value = list.map(item => {
    const meta = item
    const flag = false
    const row_type: RowType = '*'
    return { ...item, meta, flag, row_type }
  })
}

useHandler(signalsPtr, BizEngine.onVibrationSignalsChanged, getDataInfo)

onMounted(async () => {
  await getDataInfo()
})
</script>
