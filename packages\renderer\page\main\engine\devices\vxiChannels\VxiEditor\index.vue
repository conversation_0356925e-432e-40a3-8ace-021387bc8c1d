<template>
  <MyDialog v-model="isActive" title="PLC Calculation Editor" width="500px" @ok="handleSave">
    <div :class="e('body')">
      <div>Signal: {{ props.signalData.signal }}</div>
      <div>Source: Channel {{ props.signalData.source }}</div>

      <div style="display: flex; align-items: center; margin: 16px 0px">
        <span style="margin-right: 8px">X Range</span>
        <wui-input-number
          v-model="model.min"
          :controls="false"
          placeholder="Min"
          style="width: 140px; margin-right: 8px"
        />
        <wui-input-number
          v-model="model.max"
          :controls="false"
          placeholder="Max"
          style="width: 140px"
        />
      </div>
      <div class="cfg-setup_table">
        <wui-table
          show-overflow-tooltip
          :data="model.data"
          height="250px"
          border
          @row-contextmenu="handleRowMenu"
        >
          <wui-table-column prop="x" label="X" min-width="120px" align="center">
            <template #default="{ row, $index }">
              <wui-input
                v-if="row.flag"
                v-model="row.x"
                placeholder="X"
                @blur="validateInputValue(row, 'x', $index)"
              />
              <span v-else>{{ row.x }}</span>
            </template>
          </wui-table-column>
          <wui-table-column prop="y" label="Y" min-width="120px" align="center">
            <template #default="{ row, $index }">
              <wui-input
                v-if="row.flag"
                v-model="row.y"
                placeholder="Y"
                @blur="validateInputValue(row, 'y', $index)"
              />
              <span v-else>{{ row.y }}</span>
            </template>
          </wui-table-column>
          <wui-table-column label="Op" width="100px" align="center">
            <template #default="{ row, $index }">
              <TableTool.Op :flag="row.flag" @op="handleOperation($event, row, $index)" />
            </template>
          </wui-table-column>
          <template #empty>
            <TableTool.Empty />
          </template>
        </wui-table>
      </div>
    </div>
  </MyDialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, toRef, onMounted } from 'vue'
import MyDialog from '@/renderer/components/dialog/index.vue'
import TableTool, { isAddOrInsertType, OpType, RowType } from '@/renderer/components/TableTool'
import { useTableCommonMenu } from '@/renderer/hooks'
import $styles from '../index.module.scss'
import { useBem } from '@/renderer/hooks/bem'

const { e } = useBem('plc-signal-table', $styles)

const props = withDefaults(defineProps<TableDialogProps>(), {
  modelValue: false,
  calibData: () => ({
    type: CalibDataType.None,
    min: 0,
    max: 0,
    data: [],
    raw_type: CalibDataType.None
  }),
  signalData: () => ({ source: 0, signal: '' })
})

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  save: [model: TableDialogModel]
}>()

const isActive = ref(props.modelValue)

watch(isActive, value => emit('update:modelValue', value))

const model = reactive<TableDialogInternalModel>({
  data: [],
  min: 0,
  max: 0,
  type: CalibDataType.Table
})

const backupList = ref<TableDialogRow[]>([])

const createTableRow = (row_type: RowType): TableDialogRow => ({
  x: 0,
  y: 0,
  flag: true,
  row_type
})

const { handleRowMenu } = useTableCommonMenu(
  toRef(model, 'data'),
  (key, ...args) => {
    const { row, rowIndex } = args[0]

    switch (key) {
      case 'addKey':
        model.data.push(createTableRow('add'))
        break
      case 'insertKey':
        model.data.splice(rowIndex + 1, 0, createTableRow('insert'))
        break
      case 'deleteKey':
        handleOperation('delete', row as TableDialogRow, rowIndex)
        break
      case 'modifyKey':
        handleOperation('edit', row as TableDialogRow, rowIndex)
        break
      default:
        break
    }
  },
  [1],
  [
    { key: 'addKey', label: 'add' },
    { key: 'insertKey', label: 'insert' },
    { key: 'modifyKey', label: 'modify' },
    { key: 'deleteKey', label: 'delete' }
  ]
)

const handleOperation = (operation: OpType, row: TableDialogRow, index: number): void => {
  switch (operation) {
    case 'edit':
      row.flag = true
      break
    case 'cancel':
      handleCancelOperation(row, index)
      break
    case 'delete':
      model.data.splice(index, 1)
      break
    case 'select':
      row.flag = false
      break
    default:
      break
  }
}

const handleCancelOperation = (row: TableDialogRow, index: number): void => {
  if (isAddOrInsertType(row.row_type)) {
    model.data.splice(index, 1)
    return
  }

  const originalItem = backupList.value[index]
  if (originalItem) {
    Object.assign(row, originalItem, { flag: false })
  }
}

const initializeTableData = (): void => {
  const isTableData = props.calibData.raw_type === CalibDataType.Table
  model.min = props.calibData.min
  model.max = props.calibData.max
  model.type = props.calibData.type

  if (isTableData && props.calibData.data.length > 0) {
    const tableRows: TableDialogRow[] = []

    for (let i = 0; i < props.calibData.data.length; i += 2) {
      if (i + 1 < props.calibData.data.length) {
        tableRows.push({
          x: props.calibData.data[i],
          y: props.calibData.data[i + 1],
          flag: false,
          row_type: '*'
        })
      }
    }

    model.data = tableRows

    backupList.value = tableRows.map(row => ({ ...row }))
  } else {
    model.data = []
    backupList.value = []
  }
}

const validateInputValue = (row: TableDialogRow, field: 'x' | 'y', index: number): void => {
  const options = { min: model.min, max: model.max, offset: 90 }
  const fieldName = `Row ${index + 1} ${field.toUpperCase()} value`

  ValidationHelpers.validateValueInRange(row[field], options, fieldName, 'warning')
}

const validateRange = (): boolean => {
  return ValidationHelpers.validateRange(model.min, model.max, 'error')
}

const validateTableData = (): boolean => {
  const options = { min: model.min, max: model.max, offset: 90 }
  return ValidationHelpers.validateTableData(model.data, options, 'error')
}

const handleSave = (): void => {
  if (!validateRange() || !validateTableData()) {
    return
  }

  const flatData = model.data.flatMap(item => [Number(item.x), Number(item.y)])

  const saveModel: TableDialogModel = {
    ...model,
    data: flatData
  }

  emit('save', saveModel)
  isActive.value = false
}

onMounted(() => {
  initializeTableData()
})
</script>
