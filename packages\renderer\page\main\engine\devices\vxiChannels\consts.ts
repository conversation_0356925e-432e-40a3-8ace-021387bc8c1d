import { Tree } from '@/renderer/components/TreeContent'
import { InjectionKey } from 'vue'

export type VxiChannelData = {
  name: string
  deviceList: device[]
}

export type device = {
  name?: string
  deviceName: string
  slotNum: number
  addrNum: number
}

export type CurEditVxiInfo = {
  label: string
  vxiId: number
  index?: number
  groupNodeIndex?: number
  originData?: VxiChannelData
}

export interface VxiTreeChild extends Omit<Tree<number>, 'children'> {
  originData: VxiChannelData
}

interface vxiChannelsConfig {
  devicePtr: any
  curEditVxiInfo: CurEditVxiInfo
  changeTreeNode: (id: number) => void
}

export const contextKey: InjectionKey<vxiChannelsConfig> = Symbol('vxiChannelsConfig')
