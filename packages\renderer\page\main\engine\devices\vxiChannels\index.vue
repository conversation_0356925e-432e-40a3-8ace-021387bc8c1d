<template>
  <TreeContent
    ref="treeContentRef"
    v-model:default-expanded-keys="defaultExpandedKeys"
    :onTree-node-change="handleNodeChange"
    :tree-data="treeData"
    :tree-area-menu="[]"
    :show-right-menu="false"
    :right-content-padding="0"
  >
    <template #default>
      <VxiChannelsTable @db-click="dbClick" />
    </template>
  </TreeContent>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed, reactive, provide } from 'vue'
import { useBizEngine, useHandler } from '@/renderer/hooks'
import { Tree, TreeContent } from '@/renderer/components/TreeContent'
import VxiChannelsTable from './vxiChannelsTable/index.vue'
import { uuid } from '@wuk/cfg'
import { contextKey } from './consts.ts'
import { BizEngine } from '@/renderer/logic'

const treeContentRef = ref<InstanceType<typeof TreeContent>>()
const curEditVxiInfo = ref({
  label: '',
  vxiId: -1,
  index: 0,
  groupNodeIndex: -1,
  children: []
})

const devicePtr = useBizEngine()

const handleNodeChange = (data: any, node: any) => {
  const { level, parent } = node
  if (level === 2) {
    curEditVxiInfo.value = {
      ...data,
      vxiId: data.id,
      groupNodeIndex: data.index ?? -1
    }
  } else {
    curEditVxiInfo.value = {
      ...parent.data,
      vxiId: parent.data.id,
      groupNodeIndex: data.index ?? -1
    }
  }
}

const treeData = ref<any[]>([
  {
    label: 'VXI Channels',
    id: 0,
    index: 0,
    children: []
  }
])

const changeTreeNode = async (id: number) => {
  treeContentRef.value?.treeRef?.setCurrentKey(id)
}

const defaultExpandedKeys = ref<number[]>([0])

provide(
  contextKey,
  reactive({
    devicePtr,
    curEditVxiInfo,
    changeTreeNode
  })
)

const getDataInfo = async () => {
  const list = [
    {
      name: 'test',
      deviceList: [
        { deviceName: 'CENCO_TACH', slotNum: 5, addrNum: 113 },
        { deviceName: 'CONDOR_429', slotNum: 1, addrNum: 100 }
      ]
    },
    {
      name: '1212',
      deviceList: [
        { deviceName: 'CONDOR_429', slotNum: 55, addrNum: 113 },
        { deviceName: 'CENCO_TACH', slotNum: 67, addrNum: 100 }
      ]
    },
    {
      name: 'wahou',
      deviceList: [{ deviceName: 'CONDOR_429', slotNum: 1, addrNum: 100 }]
    }
  ]

  treeData.value[0].children = list.map((item, index) => ({
    label: item.name + ' VXI Channels',
    id: uuid(),
    index,
    originData: item
  }))
  curEditVxiInfo.value.children = treeData.value[0].children
}

const dbClick = (data: any) => {
  curEditVxiInfo.value = {
    ...data,
    vxiId: data.id,
    groupNodeIndex: data.index ?? -1
  }
  changeTreeNode(data.id)
}

onMounted(() => {
  getDataInfo()
})

// useHandler(devicePtr, BizEngine.onVxiChannelsChanged, getDataInfo)
</script>
