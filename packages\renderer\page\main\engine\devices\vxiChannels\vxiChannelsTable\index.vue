<template>
  <div :class="[b(), 'cfg-setup']">
    <div :class="['cfg-setup_table']">
      <wui-table show-overflow-tooltip border :data="model.list" @row-click="handleRowClick">
        <wui-table-column label="Device Name" align="center" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ row.deviceName }}</span>
          </template>
        </wui-table-column>
        <wui-table-column label="Slot Num" align="center" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ row.slotNum }}</span>
          </template>
        </wui-table-column>
        <wui-table-column label="Addr Num" align="center" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ row.addrNum }}</span>
          </template>
        </wui-table-column>
      </wui-table>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, inject, watchEffect, reactive, toRef } from 'vue'
import $styles from './index.module.scss'
import { useBem } from '@/renderer/hooks'

import { contextKey, device, VxiChannelData } from '../consts'

const { e, b } = useBem('vxi-channels-table', $styles)

const vxiContext = inject(contextKey)

const model = reactive<{ list: device[] }>({
  list: []
})

const handleRowClick = (row: device) => {
  console.log('row', row)
}

watchEffect(() => {
  const { originData = {} as VxiChannelData } = vxiContext?.curEditVxiInfo || {}
  model.list = originData.deviceList?.map((item, index) => ({
    ...item,
    name: originData.name
  }))
})
</script>
