<template>
  <MyDialog v-model="isActive" title="Attributes for EngName" @ok="onSubmit">
    <div :class="styles.modelBox">
      <div :class="styles.modelBox_item">
        <span>Units</span>
        <wui-input v-model="formData.units" />
      </div>
      <div :class="styles.modelBox_item">
        <span>Long Name</span>
        <wui-input v-model="formData.long_name" />
      </div>
      <div :class="styles.modelBox_title">Display Format</div>
      <div :class="styles.modelBox_item">
        <span>Width</span>
        <CustomSlider v-model:modelValue="formData.width" :max="9" />
      </div>
      <div :class="styles.modelBox_item">
        <span>Precision</span>
        <CustomSlider v-model:modelValue="formData.precision" :max="4" />
      </div>
    </div>
  </MyDialog>
</template>

<script lang="ts" setup>
import { ref, onMounted, defineProps, defineEmits, toRaw, computed } from 'vue'
import styles from '../index.module.scss'
import { useVModel } from '@vueuse/core'
import MyDialog from '@/renderer/components/dialog/index.vue'
import { AttributeItem } from '@wuk/cfg'
import { useCore } from '@/renderer/hooks'
import { BizEngine } from '@/renderer/logic'
import { WuiMessage } from '@wuk/wui'
import CustomSlider from '@/renderer/components/CustomSlider/index.vue'

const props = defineProps({
  params: {
    type: Object,
    default: {}
  },
  modelShow: {
    type: Boolean,
    default: false
  }
})
const emit = defineEmits(['update:modelShow'])
const isActive = useVModel(props, 'modelShow', emit)
const attributesPtr = useCore<BizEngine>(BizEngine)
const attributesIndex = ref(0)
const formData = ref<AttributeItem>({
  name: '',
  units: '',
  long_name: '',
  width: 0,
  precision: 0
})

const onSubmit = async () => {
  const { units, long_name } = formData.value
  if (!units || !long_name) {
    WuiMessage({
      message: 'Data cannot be empty',
      type: 'warning',
      offset: 90
    })
    return
  }
  const result = await attributesPtr.value?.modifyAttribute(
    attributesIndex.value,
    toRaw(formData.value)
  )
  if (!result) return
  isActive.value = false
  WuiMessage({
    message: 'success',
    type: 'success',
    offset: 70
  })
}

onMounted(() => {
  const { index, meta } = props.params
  attributesIndex.value = index
  formData.value = meta
})
</script>
