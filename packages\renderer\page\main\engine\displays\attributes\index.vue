<template>
  <div :class="styles.box">
    <div :class="styles.box_search">
      <div :class="styles.box_search_content">
        <span>Selection: </span>
        <wui-input v-model="filterInput" placeholder="Search Setting" clearable />
      </div>
    </div>
    <div :class="styles.box_table">
      <el-table-v2
        :columns="tableColumn"
        :data="filterTableData"
        :width="tableWidth"
        :height="tableheight"
        :class="styles.attributesTableV2"
      >
        <template #empty>
          <div
            style="
              width: 100%;
              height: 100%;
              display: flex;
              justify-content: center;
              align-items: center;
            "
          >
            <p>No Data</p>
          </div>
        </template>
      </el-table-v2>
    </div>
    <AttributesPopup v-if="aModelShow" v-model:modelShow="aModelShow" :params="attributesParam" />
  </div>
</template>

<script lang="ts" setup>
import styles from './index.module.scss'
import { ref, onMounted, computed, h, onBeforeUnmount } from 'vue'
import { EditPen } from '@element-plus/icons-vue'
import { useBizEngine, useHandler } from '@/renderer/hooks'
import { BizEngine } from '@/renderer/logic'
import { WuiIcon, WuiInput } from '@wuk/wui'
import { AttributeItem } from '@wuk/cfg'
import AttributesPopup from './attributesPopup/index.vue'

interface newAttributeItem extends AttributeItem {
  meta?: AttributeItem
  flag: boolean
  type: string
}
interface paramItem {
  index: number
  meta?: AttributeItem
}
const attributesPtr = useBizEngine()
const attributesList = ref<newAttributeItem[]>([])
const filterInput = ref('')
const aModelShow = ref(false)
const tableColumn = ref([
  {
    key: 'noIndex',
    dataKey: 'noIndex',
    maxWidth: 80,
    minWidth: 80,
    title: 'No.',
    align: 'center',
    cellRenderer: ({ rowIndex }: any) => {
      return h('span', { style: { fontSize: '18px', fontWeight: 'bold' } }, `${rowIndex + 1}`)
    }
  },
  {
    key: 'name',
    dataKey: 'name',
    minWidth: 400,
    title: 'Name',
    align: 'center',
    cellRenderer: (e: any) => {
      const { rowData } = e
      return h('div', { style: { width: '100%' } }, [
        h(
          'span',
          {
            style: { fontSize: '18px', fontWeight: 'bold' }
          },
          rowData.name
        )
      ])
    }
  },
  {
    key: 'actions',
    dataKey: 'actions',
    maxWidth: 100,
    minWidth: 100,
    title: 'Op',
    align: 'center',
    cellRenderer: (e: any) => {
      const { rowData, rowIndex } = e
      return h(
        WuiIcon,
        {
          onClick: () => openDialog(rowData, rowIndex),
          style: { fontSize: '18px', fontWeight: 'bold' }
        },
        () => h(EditPen)
      )
    }
  }
])
const filterTableData = computed(() =>
  attributesList.value.filter(data => {
    return !filterInput.value || data.name.toLowerCase().includes(filterInput.value.toLowerCase())
  })
)
const attributesParam = ref<paramItem>({
  index: 0
})
const tableWidth = ref(window.innerWidth - 290)
const tableheight = ref(window.innerHeight - 144)
const openDialog = (item: newAttributeItem, index: number) => {
  attributesParam.value = { index, meta: { ...(item.meta || ({} as AttributeItem)) } }
  aModelShow.value = true
}
// 数据处理
const getDataInfo = async () => {
  const { list = [] } = (await attributesPtr.value?.readAttributes()) || {}
  attributesList.value = list.map(item => {
    const meta = item
    const flag = false
    const type = ''
    return { ...item, meta, flag, type }
  })
}
const handleResize = () => {
  tableWidth.value = window.innerWidth - 290
  tableheight.value = window.innerHeight - 144
}

useHandler(attributesPtr, BizEngine.onAttributeOptionsChanged, getDataInfo)

onMounted(async () => {
  window.addEventListener('resize', handleResize)
  await getDataInfo()
})
onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
})
</script>
