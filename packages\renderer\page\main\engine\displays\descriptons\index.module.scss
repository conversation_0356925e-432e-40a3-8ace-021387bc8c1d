.box {
  height: 100%;
  padding: 5px 10px;

  &_search {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 30px;

    &_content {
      width: 260px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      &_btn {
        width: 125px;
        height: 30px;
        text-align: center;
        line-height: 30px;
        border: 1px solid #bbbbbb;
        border-radius: 4px;
        cursor: pointer;
        &:active {
          background-color: #dcdfe6;
        }
      }
      span {
        display: inline-block;
        font-size: 13px;
        font-weight: bold;
      }
    }
  }
  &_table {
    width: 100%;
    height: calc(100% - 50px);
    margin: 5px 0;
    border: 1px solid #bbbbbb;
    background-color: #ffffff;
  }
}
.modelBox {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
}
h5 {
  margin: 0 0 5px;
  font-size: 14px;
}
