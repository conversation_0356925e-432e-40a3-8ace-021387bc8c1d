<template>
  <div :class="styles.box">
    <div :class="styles.box_search">
      <div :class="styles.box_search_content">
        <span>Search: </span>
        <wui-input
          v-model="filterValue"
          placeholder="Search Setting"
          style="width: 200px; height: 30px"
        />
      </div>
      <div :class="styles.box_search_content">
        <span :class="styles.box_search_content_btn">Edit Include File</span>
        <span :class="styles.box_search_content_btn" @click="openModel">Parameters</span>
      </div>
    </div>
    <div :class="styles.box_table">
      <wui-input
        v-model="textareaValue"
        :autosize="{ minRows: 10 }"
        type="textarea"
        placeholder="Please input"
        style="height: 100%"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import styles from './index.module.scss'
import { useParameterDialog } from '@/renderer/utils/common'

const filterValue = ref('')
const textareaValue = ref('')
const textareaData = ref<string[]>([])

const openModel = async () => {
  const result = await useParameterDialog()
  textareaData.value.push(result)
  textareaValue.value = textareaData.value.join('')
}
</script>
