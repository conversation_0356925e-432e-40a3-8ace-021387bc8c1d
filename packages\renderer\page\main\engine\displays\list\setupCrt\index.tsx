import { computed, defineComponent, PropType, ref, toRef } from 'vue'
import { UpdateType } from '../type'
import { useBizEngine, useTableCommonMenu } from '@/renderer/hooks'
import { WuiForm, WuiMessage } from '@wuk/wui'
import { DisplayModeType } from '@wuk/cfg'
import { useBem } from '@/renderer/hooks/bem'
import styles from './index.module.scss'
import TableTool from '@/renderer/components/TableTool'
export const rules = {
  name: [
    {
      required: true,
      message: 'Please input the crt name',
      trigger: 'change'
    },
    {
      min: 1,
      max: 20,
      message: 'Length should be 1 to 20',
      trigger: 'change'
    }
  ]
}
export const SetupCrt = defineComponent({
  name: 'SetupCrt',
  props: {
    modelValue: {
      type: Object as PropType<UpdateType>,
      default: () => ({})
    }
  },
  emits: ['modify-quad', 'update:modelValue', 'add-crt'],
  setup(props, { emit }) {
    const { b, e } = useBem('setup-crt', styles)
    const tipsMessage = () => {
      WuiMessage({
        message: 'success',
        type: 'success',
        offset: 70,
        grouping: true
      })
    }
    /* 动态修改值 */
    const curEditCrtInfo = computed({
      get() {
        return props.modelValue
      },
      set(val) {
        emit('update:modelValue', val)
      }
    })
    const crtFormRef = ref<InstanceType<typeof WuiForm>>()
    const displayPtr = useBizEngine()
    const handleAddQuad = () => {
      const { crtIndex = -1, name, quadList } = curEditCrtInfo.value
      if (crtIndex === -1) {
        emit('add-crt')
        return
      }
      const quadIndex = quadList.length
      const quad_name = `Quad ${quadIndex + 1}`
      const data = {
        label: quad_name,
        index: quadIndex,
        id: `${name}-${quad_name}-${quadIndex}`
      }
      curEditCrtInfo.value.quadList.push(data)
      displayPtr.value?.addDisplayQuad(
        {
          position: quadIndex + 1,
          quad_name,
          change_display_mode: DisplayModeType.None,
          list: []
        },
        crtIndex
      )
    }
    const quadListRef = computed(() => curEditCrtInfo.value.quadList)
    const { handleRowMenu } = useTableCommonMenu(
      quadListRef,
      async (key, ...args) => {
        const { rowIndex, row } = args[0]
        if (key === 'addKey') {
          handleAddQuad()
        } else if (key === 'deleteKey') {
          const crtIndex = curEditCrtInfo.value.crtIndex ?? -1
          if (crtIndex === -1) return
          curEditCrtInfo.value.quadList.splice(rowIndex, 1)
          const removeResult = await displayPtr.value?.removeDisplayQuad(crtIndex, rowIndex)
          if (!removeResult) return
          tipsMessage()
        } else if (key === 'modifyKey') {
          row && emit('modify-quad', row.id)
        }
      },
      [1],
      [
        { key: 'addKey', label: 'Add' },
        { key: 'deleteKey', label: 'Delete' },
        { key: 'modifyKey', label: 'Modify' }
      ]
    )
    const handleEditCrt = async () => {
      const valid = await crtFormRef.value?.validateField('name')
      if (!valid) return
      const { name, crtIndex = 0 } = curEditCrtInfo.value
      const editResult = await displayPtr.value?.modifyDisplayCrt(crtIndex, name)
      if (!editResult) return
      tipsMessage()
    }
    return () => (
      <>
        <wui-form
          ref={crtFormRef}
          class={b()}
          label-width='230'
          label-position='left'
          validate-msg-position='right'
          validate-ellipsis='2'
          hide-required-asterisk
          status-icon
          rules={rules}
          model={curEditCrtInfo.value}>
          <wui-form-item prop='name'>
            {{
              label: () => <div class={e('label')}>Modify The Crt Name</div>,
              default: () => (
                <wui-input
                  v-model={curEditCrtInfo.value.name}
                  onChange={handleEditCrt}
                  type='text'
                  placeholder='Please input'
                />
              )
            }}
          </wui-form-item>
          <wui-form-item class={e('table')} label-position='top'>
            {{
              label: () => <div class={e('title')}>Quad List</div>,
              default: () => (
                <wui-table
                  border
                  height='100%'
                  data={curEditCrtInfo.value.quadList}
                  header-cell-style={{
                    background: '#EAF1FD',
                    color: '#90AFE4',
                    fontSize: '18px',
                    fontWeight: 'bold'
                  }}
                  onRow-contextmenu={handleRowMenu}>
                  {{
                    default: () => (
                      <>
                        <wui-table-column
                          fixed='left'
                          label='No.'
                          type='index'
                          width='80px'
                          align='center'
                        />
                        <wui-table-column label='Quad' prop='label' align='center' />
                      </>
                    ),
                    empty: () => <TableTool.Empty />
                  }}
                </wui-table>
              )
            }}
          </wui-form-item>
        </wui-form>
      </>
    )
  }
})
