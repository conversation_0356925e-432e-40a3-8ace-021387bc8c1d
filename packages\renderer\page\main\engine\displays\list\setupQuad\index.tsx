import { defineComponent, reactive, PropType, watchEffect, onMounted, ref, toRef } from 'vue'
import styles from './index.module.scss'
import { useBem } from '@/renderer/hooks/bem'
import { DisplayItem, DisplayModeType, DisplayQuadOption } from '@wuk/cfg'
import { useBizEngine, useTableCommonMenu } from '@/renderer/hooks'
import { WuiMessage, WuiMessageBox } from '@wuk/wui'
import TableTool, { BaseTableRow, isAddOrInsertType, OpType } from '@/renderer/components/TableTool'
import { convertDspName } from '@/renderer/utils/common'
type TableRow = BaseTableRow<{
  name: string
}>
export const SetupQuad = defineComponent({
  name: 'SetupQuad',
  props: {
    displayQuadOption: {
      type: Object as PropType<DisplayQuadOption>,
      default: () => ({})
    },
    crtIndex: {
      type: Number,
      default: -1
    },
    quadIndex: {
      type: Number,
      default: -1
    }
  },
  setup(props) {
    const { b, e } = useBem('setup-quad', styles)
    const displayPtr = useBizEngine()
    const options1 = [
      {
        label: 'None',
        value: 0
      },
      {
        label: 'PullDown',
        value: 1
      },
      {
        label: 'Increment',
        value: 2
      },
      {
        label: 'Menu',
        value: 3
      }
    ]
    const dspNames = ref<(DisplayItem & { label: string })[]>([])
    const model = reactive({
      change_display_mode: DisplayModeType.None,
      list: [] as TableRow[]
    })
    const { handleRowMenu } = useTableCommonMenu(toRef(model, 'list'), (key, ...args) => {
      const { row, rowIndex } = args[0]
      if (key === 'addKey') {
        handleAdd()
      } else if (key === 'insertKey') {
        model.list.splice(rowIndex + 1, 0, createRow('insert'))
      } else if (key === 'modifyKey') {
        handleOp('edit', row, rowIndex)
      } else if (key === 'deleteKey') {
        handleOp('delete', row, rowIndex)
      }
    })
    const tipsMessage = () => {
      WuiMessage({
        message: 'success',
        type: 'success',
        offset: 90
      })
    }
    /**
     * @description op callback
     * @param {OpType} op 操作类型
     * @param {DisplayQuadOption} row 操作行
     * @param  {number} [index] 操作索引(可选)
     */
    const handleOp = (op: OpType, row?: TableRow, index?: number) => {
      if (!row) return
      switch (op) {
        case 'edit':
          row.flag = true
          break
        case 'select':
          row.row_type = '*'
          row.flag = false
          break
        case 'delete':
          model.list.splice(index!, 1)
          break
        case 'cancel':
          if (isAddOrInsertType(row.row_type)) {
            model.list.splice(index!, 1)
            return
          }
          row.name = props.displayQuadOption.list[index!]
          row.flag = false
          break
        default:
          break
      }
    }
    /**
     * @description 获取dsp name 集合
     */
    const getDspNames = async () => {
      const { files = [] } = (await displayPtr.value?.readDisplayDefinedOptions()) || {}
      dspNames.value = files.map(item => ({ ...item, label: convertDspName(item.name) }))
    }
    function handleAdd() {
      model.list.push(createRow('add'))
    }
    function createRow(row_type: TableRow['row_type'], flag = true) {
      const name = dspNames.value[0].name ?? ''
      return { name, flag, row_type }
    }
    /**
     * @description reset 重置
     */
    const handleReset = () => {
      const { change_display_mode, list } = props.displayQuadOption
      model.change_display_mode = change_display_mode
      model.list = list.map(name => ({ name, flag: false, row_type: '*' }))
    }

    const handleSave = async () => {
      const { list, change_display_mode } = model
      const onSave = async () => {
        const {
          displayQuadOption: { quad_name, position },
          crtIndex,
          quadIndex
        } = props
        const data = {
          position,
          quad_name,
          change_display_mode,
          list: list.map(item => item.name)
        }
        const res = await displayPtr.value?.modifyDisplayQuad(crtIndex, data, quadIndex)
        if (!res) return
        tipsMessage()
      }
      const isNoSave = list.some(item => item.flag)
      if (isNoSave) {
        WuiMessageBox.confirm(
          'Data has not been fully saved. Do you want to continue submitting the saved data?',
          'Warning',
          {
            confirmButtonText: 'OK',
            cancelButtonText: 'Close',
            type: 'warning',
            draggable: true,
            showClose: false
          }
        )
          .then(async () => {
            await onSave()
          })
          .catch(() => {})
      } else {
        await onSave()
      }
    }

    onMounted(async () => {
      await getDspNames()
      watchEffect(handleReset)
    })
    return () => (
      <div class={[b(), 'cfg-setup']}>
        <div class={e('form')}>
          <div class={e('form', 'item')}>
            <span class={e('form', 'item', 'label')}>Change Display Mode</span>
            <wui-select
              v-model={model.change_display_mode}
              placeholder='Select'
              class={e('form', 'item', 'select')}>
              {options1.map(item => (
                <wui-option key={item.value} label={item.label} value={item.value} />
              ))}
            </wui-select>
          </div>
        </div>
        <div class='cfg-setup_table'>
          <wui-table
            border
            height='100%'
            data={model.list}
            onRow-contextmenu={handleRowMenu}
            header-cell-style={{
              background: '#EAF1FD',
              color: '#90AFE4',
              fontSize: '18px',
              fontWeight: 'bold'
            }}>
            {{
              default: () => (
                <>
                  <wui-table-column
                    fixed='left'
                    label='No.'
                    type='index'
                    width='80px'
                    align='center'
                  />
                  <wui-table-column prop='name' label='Display Name' align='center'>
                    {{
                      default: ({ row }: any) => (
                        <>
                          {row.flag ? (
                            <wui-select v-model={row.name} placeholder='Select' style='width: 100%'>
                              {dspNames.value.map((dsp, index) => (
                                <wui-option key={index} label={dsp.label} value={dsp.name} />
                              ))}
                            </wui-select>
                          ) : (
                            row.name
                          )}
                        </>
                      )
                    }}
                  </wui-table-column>
                  <wui-table-column label='Op' width='100px' align='center'>
                    {{
                      default: ({ row, $index }: any) => (
                        <TableTool.Op
                          flag={row.flag}
                          onOp={op => {
                            handleOp(op, row, $index)
                          }}
                        />
                      )
                    }}
                  </wui-table-column>
                </>
              ),
              empty: () => {
                return <TableTool.Empty />
              }
            }}
          </wui-table>
        </div>
        <div class='cfg-submit'>
          <wui-button type='info' onClick={handleReset}>
            Reset
          </wui-button>
          <wui-button type='primary' onClick={handleSave}>
            Ok
          </wui-button>
        </div>
      </div>
    )
  }
})
