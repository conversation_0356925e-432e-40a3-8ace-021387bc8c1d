<template>
  <div :class="styles.engine">
    <wui-select
      v-model="value"
      :placeholder="value"
      :disabled="true"
      :class="styles.engine_select"
      :placeholder-class="styles.engine_select_placeholder"
      :wrapper-class="styles.engine_select_wrapper"
    >
      <wui-option
        v-for="(it, index) in list"
        :key="index"
        :class="styles.engine_select_opt"
        :label="it.label"
        :value="it.value"
      />
    </wui-select>
  </div>
</template>

<script setup lang="ts" name="Customer">
import { computed, ref, watchEffect, defineProps } from 'vue'
import { engineProps } from './type'
import styles from './index.module.scss'

const value = ref('')

const props = defineProps(engineProps)
const list = computed(() => props.list)

watchEffect(() => {
  const [val] = props.list
  value.value = props.current || val?.value || ''
})
</script>
