import { buildProps, definePropType } from '@wuk/wui/dist/utils'
import { ExtractPropTypes } from 'vue'

export interface EngineItem {
  value: string
  label: string
}

export const engineProps = buildProps({
  list: {
    type: definePropType<EngineItem[]>(Array),
    default: []
  },
  current: {
    type: String,
    default: ''
  }
} as const)
export type CustomerProps = ExtractPropTypes<typeof engineProps>
