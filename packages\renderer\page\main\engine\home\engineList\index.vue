<template>
  <div :class="styles.egls">
    <h2>Engine Options</h2>
    <wui-form
      ref="egLsFormRef"
      label-width="230"
      label-position="left"
      validate-ellipsis="1"
      hide-required-asterisk
      inline-message
      status-icon
      validate-box-gap="3"
      validate-placement="bottom"
      :model="engineData"
      :rules="egLsRules"
      :show-validate-success="true"
      validate-success-tip="✓ Saved"
    >
      <wui-form-item label="Test Mode" prop="test_mode">
        <wui-select
          v-model="engineData.test_mode"
          placeholder="Select"
          @change="onEngineChange('test_mode', $event)"
        >
          <wui-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </wui-select>
      </wui-form-item>
      <wui-form-item label="Engine On" prop="crs_on_param">
        <div :class="styles.box_menu_options_button" @click="openModel('crs_on_param')">
          {{ engineData.crs_on_param || 'None' }}
        </div>
      </wui-form-item>
      <wui-form-item label="Run Limits Param" prop="run_limits_param">
        <div :class="styles.box_menu_options_button" @click="openModel('run_limits_param')">
          {{ engineData.run_limits_param || 'None' }}
        </div>
      </wui-form-item>
      <wui-form-item label="PLA Idle Default (degs)" prop="pla_idle_default">
        <wui-input
          v-model="engineData.pla_idle_default"
          placeholder="Please input"
          @change="onEngineChange('pla_idle_default', $event)"
        />
      </wui-form-item>
      <wui-form-item label="PLA Takeoff Default (degs)" prop="pla_takeoff_default">
        <wui-input
          v-model="engineData.pla_takeoff_default"
          placeholder="Please input"
          @change="onEngineChange('pla_takeoff_default', $event)"
        />
      </wui-form-item>
    </wui-form>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, nextTick } from 'vue'
import styles from '../index.module.scss'
import { BizEngine } from '@/renderer/logic'
import { useBizEngine, useHandler } from '@/renderer/hooks'
import { EngineOptions } from '@wuk/cfg'
import { useParameterDialog } from '@/renderer/utils/common'
import { useEgLsRules } from './rule'
import { WuiForm } from '@wuk/wui'

const { egLsRules, setEgLsError } = useEgLsRules()
const egLsFormRef = ref<InstanceType<typeof WuiForm>>()
const homePtr = useBizEngine()
const engineData = ref<EngineOptions>({
  test_mode: 0,
  crs_on_param: '',
  run_limits_param: '',
  pla_idle_default: '',
  pla_takeoff_default: ''
})
const options = [
  {
    label: 'Test With Hardware',
    value: 0
  },
  {
    label: 'No Hardware',
    value: 1
  }
]
const onEngineChange = async (key: string, value: string | number) => {
  setEgLsError(key)
  const valid = await egLsFormRef.value?.validateField(key)
  if (!valid) return
  const result = await homePtr.value?.writeEngineOptions({ [key]: value })
  if (!result) {
    setEgLsError(key, `faild to save`)
    egLsFormRef.value?.validateField(key)
    return
  }
}
const openModel = async (key: string) => {
  const name = await useParameterDialog()
  onEngineChange(key, name)
}
const getDataInfo = async () => {
  engineData.value = (await homePtr.value?.readEngineOptions()) || ({} as EngineOptions)
}

useHandler(homePtr, BizEngine.onEngineOptionsChanged, getDataInfo)

onMounted(async () => {
  await getDataInfo()
  nextTick(() => {
    egLsFormRef.value?.clearValidate()
  })
})
</script>
