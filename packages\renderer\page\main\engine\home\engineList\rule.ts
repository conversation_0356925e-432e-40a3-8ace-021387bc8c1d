import { ref } from 'vue'
import { FormItemRule, FormRules } from '@wuk/wui'
import { validApi, setRuleErrorCallback } from '@/renderer/utils/rules'
export const useEgLsRules = () => {
  const egLsErrors = ref<Record<string, string>>({})
  const commonRule = {
    trigger: 'change',
    validator: validApi(egLsErrors.value)
  }
  const plaRule: FormItemRule = {
    trigger: 'change',
    validator: (rule, value, callback) => {
      const reg = /[^\d.]/g
      if (reg.test(value)) {
        callback('Please enter a number')
      }
      callback()
    }
  }
  const egLsRules: FormRules = {
    test_mode: commonRule,
    crs_on_param: commonRule,
    run_limits_param: commonRule,
    pla_idle_default: [plaRule, commonRule],
    pla_takeoff_default: [plaRule, commonRule]
  }
  const setEgLsError = (key: string, value = '') => {
    setRuleErrorCallback(egLsErrors, key, value)
  }
  return {
    egLsRules,
    egLsErrors,
    setEgLsError
  }
}
