.box {
  &_options {
    height: 400px;
    margin-bottom: 30px;

    &_btn {
      display: flex;
      justify-content: space-between;
      align-items: center;
      span {
        display: inline-block;
        width: 70px;
        height: 30px;
        line-height: 30px;
        text-align: center;
        cursor: pointer;
        border: 1px solid #d4d4d4;
        border-radius: 4px;
      }
    }

    h2 {
      margin: 10px 0;
    }
    h4 {
      margin: 0 0 5px 0;
    }
  }
  &_modelBox {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;

    span {
      display: inline-block;
      font-size: 18px;
      font-weight: bold;
    }
  }
}
