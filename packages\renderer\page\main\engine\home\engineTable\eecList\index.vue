<template>
  <MyDialog v-model="isActive" title="EEC List" width="700px" @ok="onSubmit">
    <div :class="styles.box_modelBox">
      <span>EEC Prompt String </span>
      <wui-input
        v-model="eecList.eec_prompt_string"
        placeholder="Please input"
        style="width: 261px; height: 39px"
      />
    </div>
    <wui-table
      border
      :data="eecList.list"
      height="250px"
      style="width: 100%"
      :header-cell-style="{
        background: '#EAF1FD',
        color: '#90AFE4',
        fontSize: '18px',
        fontWeight: 'bold'
      }"
      @row-contextmenu="handleRowMenu"
    >
      <wui-table-column label="Valid EEC Types" min-width="200px" align="center">
        <template #default="{ row }">
          <wui-input
            v-if="row.flag"
            v-model="row.name"
            clearable
            placeholder="Please input"
            style="width: 100%; margin-left: 0; height: 32px"
          />
          <span v-else>{{ row.name }}</span>
        </template>
      </wui-table-column>
      <wui-table-column label="Op" fixed="right" width="100px" align="center">
        <template #default="{ row, $index }">
          <TableTool.Op :flag="row.flag" @op="handleOp($event, row, $index)" />
        </template>
      </wui-table-column>
      <template #empty>
        <TableTool.Empty />
      </template>
    </wui-table>
  </MyDialog>
</template>

<script lang="ts" setup>
import { ref, onMounted, defineProps, defineEmits, toRef, reactive } from 'vue'
import styles from './index.module.scss'
import MyDialog from '@/renderer/components/dialog/index.vue'
import { useCore, useTableCommonMenu } from '@/renderer/hooks'
import { BizEngine } from '@/renderer/logic'
import { useVModel } from '@vueuse/core'
import { WuiMessage } from '@wuk/wui'
import TableTool, { isAddOrInsertType, OpType, RowType } from '@/renderer/components/TableTool'

interface listItem {
  name: string
  flag: boolean
  row_type: RowType
  meta?: string[]
}

interface eecListItem {
  eec_prompt_string: string
  list: listItem[]
}

const props = defineProps({
  params: {
    type: Object,
    default: () => {}
  },
  modelShow: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelShow'])
const isActive = useVModel(props, 'modelShow', emit)
const eccPtr = useCore<BizEngine>(BizEngine)

const createRow = (row_type: listItem['row_type']) => ({
  row_type,
  name: '',
  meta: [],
  flag: true
})

let originalList = [] as any
const eccIndex = ref(0)
const eecList = reactive<eecListItem>({
  eec_prompt_string: '',
  list: []
})

const { handleRowMenu } = useTableCommonMenu(
  toRef(eecList, 'list'),
  (key, ...args) => {
    const { row, rowIndex } = args[0]
    switch (key) {
      case 'addKey':
        eecList.list.push(createRow('add'))
        break
      case 'deleteKey':
        handleOp('delete', row, rowIndex)
        break
      case 'modifyKey':
        handleOp('edit', row, rowIndex)
        break
      case 'insertKey':
        eecList.list.splice(rowIndex + 1, 0, createRow('insert'))
        break
    }
  },
  [1],
  [
    { key: 'addKey', label: 'add' },
    { key: 'insertKey', label: 'insert' },
    { key: 'modifyKey', label: 'modify' },
    { key: 'deleteKey', label: 'delete' }
  ]
)

const handleOp = (op: OpType, row: listItem | undefined, index: number) => {
  if (!row) return
  switch (op) {
    case 'edit':
      row.flag = true
      break
    case 'delete':
      handleDelete(index)
      break
    case 'select':
      handleSelect(row, index)
      break
    case 'cancel':
      handleOpCancel(row, index)
      break
  }
}

const handleDelete = async (index: number) => {
  eecList.list.splice(index, 1)
}

const handleSelect = async (row: listItem, _index: number) => {
  row.row_type = '*'
  row.flag = false
}

const handleOpCancel = (row: listItem, index: number) => {
  if (isAddOrInsertType(row.row_type)) {
    eecList.list.splice(index, 1)
    return
  }
  row.name = originalList[index].name
  row.flag = false
}

const onSubmit = async () => {
  const updatedOption = {
    eec_prompt_string: eecList.eec_prompt_string,
    list: eecList.list.map(item => item.name).filter(name => name.trim() !== '')
  }

  const res = await eccPtr.value?.modifyDash(eccIndex.value, { option: updatedOption })
  if (res) {
    WuiMessage({
      message: 'EEC List updated successfully',
      type: 'success',
      offset: 70
    })
    isActive.value = false
  } else {
    WuiMessage({
      message: 'Failed to update EEC List',
      type: 'error',
      offset: 70
    })
  }
}

onMounted(() => {
  const { index, meta } = props.params
  const { eec_prompt_string, list } = meta.option
  eccIndex.value = index
  eecList.eec_prompt_string = eec_prompt_string
  eecList.list = list.map((name: string) => ({
    name,
    row_type: '*',
    flag: false,
    meta: [name]
  }))
  originalList = list.map((name: string) => ({ name }))
})
</script>
