<template>
  <div>
    <h2>Valid Engine Dash Numbers</h2>
    <wui-table
      border
      :data="dashList"
      style="width: 500px; height: calc(100% - 42px)"
      :header-cell-style="{
        background: '#EAF1FD',
        color: '#90AFE4',
        fontSize: '18px',
        fontWeight: 'bold'
      }"
      @row-contextmenu="handleRowMenu"
      class="engineDashTable"
    >
      <wui-table-column label="Name" width="120px" align="center" show-overflow-tooltip>
        <template #default="{ row, $index }">
          <wui-input
            v-if="row.flag"
            v-model="row.name"
            clearable
            placeholder="Please input"
            style="width: 100%; margin-left: 0; height: 32px"
          />
          <span v-else style="cursor: pointer" @click="onUpName(row.name, $index)">{{
            row.name
          }}</span>
        </template>
      </wui-table-column>
      <wui-table-column label="EEC List" min-width="280px" align="center" show-overflow-tooltip>
        <template #default="{ row }">
          <span>
            {{ row.option.list?.join() || '--' }}
          </span>
        </template>
      </wui-table-column>
      <wui-table-column label="Op" fixed="right" width="100px" align="center">
        <template #default="{ row, $index }">
          <TableTool.Op :flag="row.flag" @op="handleOp($event, row, $index)">
            <template #pre-op="{ opStyle }">
              <wui-icon :style="opStyle" @click="onEdit(row, $index)">
                <Edit />
              </wui-icon>
            </template>
          </TableTool.Op>
        </template>
      </wui-table-column>
      <template #empty>
        <TableTool.Empty />
      </template>
    </wui-table>
    <EecList v-if="eecShow" v-model:modelShow="eecShow" :params="eccParam" />
    <MyDialog v-model="isActive" title="Modify The Name" width="500px" @ok="onSubmit">
      <div :class="styles.popupStyle">
        <span>Name：</span>
        <wui-input
          v-model="updateRow.name"
          clearable
          placeholder="Please input"
          style="width: 100%; margin-left: 0; height: 32px"
        />
      </div>
    </MyDialog>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, toRaw } from 'vue'
import styles from '../index.module.scss'
import { Edit } from '@element-plus/icons-vue'
import { DashNumberItem } from '@wuk/cfg'
import { useBizEngine, useHandler, useTableCommonMenu } from '@/renderer/hooks'
import { BizEngine } from '@/renderer/logic'
import { WuiMessage } from '@wuk/wui'
import EecList from './eecList/index.vue'
import MyDialog from '@/renderer/components/dialog/index.vue'
import TableTool, { OpType, isAddOrInsertType, RowType } from '@/renderer/components/TableTool'

interface newDashNumberItem extends DashNumberItem {
  meta?: DashNumberItem
  flag: boolean
  row_type: RowType
}
interface eecListItem {
  index: number
  meta?: DashNumberItem
}
interface updateType {
  name: string
  rowIndex: number
}
const homePtr = useBizEngine()
const dashList = ref<newDashNumberItem[]>([])
const createRow = (row_type: RowType): newDashNumberItem => ({
  name: '',
  eec_key: '',
  option: {
    eec_prompt_string: '',
    list: []
  },
  flag: true,
  row_type
})
const eecShow = ref(false)
const eccParam = ref<eecListItem>({
  index: 0
})
const updateRow = ref<Partial<updateType>>({})
const isActive = ref(false)

const tipsMessage = () => {
  WuiMessage({
    message: 'success',
    type: 'success',
    offset: 70
  })
}
const noTipsMessage = () => {
  WuiMessage({
    message: 'Data cannot be empty',
    type: 'warning',
    offset: 70
  })
}
const onUpName = (rowName: newDashNumberItem, rowIndex: number) => {
  updateRow.value = { name: rowName.name, rowIndex }
  isActive.value = true
}
const onSubmit = async () => {
  const { name, rowIndex = 0 } = updateRow.value
  if (!name) {
    noTipsMessage()
    return
  }
  const editResult = await homePtr.value?.modifyDash(rowIndex, { name })
  if (!editResult) return
  tipsMessage()
  isActive.value = false
}
// 使用标准的表格菜单钩子
const { handleRowMenu } = useTableCommonMenu(
  dashList,
  async (key, ...args) => {
    const { row, rowIndex } = args[0]
    switch (key) {
      case 'addKey':
        const newRow = createRow('add')
        dashList.value.push(newRow)
        break
      case 'insertKey':
        const insertRow = createRow('insert')
        dashList.value.splice(rowIndex + 1, 0, insertRow)
        break
      case 'modifyKey':
        row && onUpName(row, rowIndex)
        break
      case 'deleteKey':
        handleOp('delete', row, rowIndex)
        break
      case 'eecKey':
        row && onEdit(row, rowIndex)
        break
      default:
        break
    }
  },
  [1],
  [
    { key: 'addKey', label: 'add' },
    { key: 'insertKey', label: 'insert' },
    { key: 'modifyKey', label: 'modify' },
    { key: 'deleteKey', label: 'delete' },
    { key: 'eecKey', label: 'eec list' }
  ]
)

const onEdit = (item: newDashNumberItem, index: number) => {
  eccParam.value = { index, meta: item }
  eecShow.value = true
}

// 操作处理函数
const handleOp = async (op: OpType, row: newDashNumberItem | undefined, index: number) => {
  if (!row) return
  switch (op) {
    case 'edit':
      onUpName(row, index)
      break
    case 'delete':
      const removeResult = await homePtr.value?.removeDash(index)
      if (!removeResult) return
      tipsMessage()
      break
    case 'select':
      await onConfirm(row, index)
      break
    case 'cancel':
      onClose(row, index)
      break
  }
}
// 关闭事件
const onClose = (item: newDashNumberItem, index: number) => {
  const { row_type, meta = {} } = item
  const { name, option } = meta as DashNumberItem
  if (isAddOrInsertType(row_type)) {
    dashList.value.splice(index, 1)
  } else {
    item.name = name
    item.option = option
    item.flag = false
  }
}
// 提交事件
const onConfirm = async (item: newDashNumberItem, index: number) => {
  const { name, eec_key, option, row_type } = item
  if (!name) {
    noTipsMessage()
    return
  }
  const data = { name, eec_key, option: toRaw(option) }
  let editResult
  if (isAddOrInsertType(row_type)) {
    editResult = await homePtr.value?.addDash(data, row_type === 'insert' ? index : undefined)
  } else {
    editResult = await homePtr.value?.modifyDash(index, { name })
  }
  if (!editResult) return
  item.row_type = '*'
  item.flag = false
  eccParam.value = { index, meta: item }
  eecShow.value = true

  tipsMessage()
}

// 数据处理
const getDataInfo = async () => {
  const { list = [] } = (await homePtr.value?.readDashOptions()) || {}
  dashList.value = list.map(item => {
    const meta = item
    const flag = false
    const row_type: RowType = '*'
    return { ...item, meta, flag, row_type }
  })
}

useHandler(homePtr, BizEngine.onDashOptionsChanged, getDataInfo)

onMounted(async () => {
  await getDataInfo()
})
</script>

<style lang="scss" module>
.engineDashTable {
  .wui-scrollbar__view {
    height: 100%;
  }
  .wui-table__empty-text {
    display: inline-block;
    height: 100%;
  }
}
</style>
