// Mixins for reusable styles
@mixin flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

@mixin flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

@mixin flex-align {
  display: flex;
  align-items: center;
}

@mixin button-base {
  height: 30px;
  line-height: 30px;
  text-align: center;
  font-size: 14px;
  border-radius: 4px;
  border: 1px solid #d2d2d2;
  cursor: pointer;
}

@mixin button-small {
  @include flex-center;
  width: 70px;
  height: 30px;
  line-height: 30px;
  cursor: pointer;
  border: 1px solid #d4d4d4;
  border-radius: 4px;
}

@mixin custom-scrollbar {
  &::-webkit-scrollbar {
    width: 6px;
  }
  &::-webkit-scrollbar-thumb {
    background-color: #dee0e2;
    border-radius: 10px;
  }
  &::-webkit-scrollbar-thumb:hover {
    cursor: pointer;
    background-color: #cccccc;
  }
}

@mixin heading-reset {
  h2 {
    margin: 0 0 10px;
  }
  h4 {
    margin: 0 0 5px 0;
  }
}

// Variables
$border-color: #d3d3d3;
$border-light: #e5e6e8;
$border-medium: #d8d8d8;
$bg-white: #ffffff;
$bg-hover: #f8f8f8;
$bg-popup: #f5f6f8;

.box {
  height: 100%;
  background-color: $bg-white;
  padding: 20px;

  &_search {
    padding: 10px 0;
    @include flex-between;

    &_title {
      font-size: 21px;
      font-weight: bold;
    }

    &_searchImg {
      width: 260px;
      @include flex-align;

      &_image {
        width: 30px;
        height: 38px;
        @include flex-center;
        border: 1px solid $border-light;
        border-left: none;
        border-radius: 0 4px 4px 0;
        background-color: $bg-white;

        img {
          width: 25px;
          height: 25px;
        }
      }
    }
    &_searchImg :global(.wui-input) {
      height: 40px;
    }
    &_searchImg :global(.wui-input__wrapper) {
      box-shadow: none;
      border: 1px solid $border-light;
      border-right: none;
      border-radius: 4px 0 0 4px;
    }
  }

  &_menu {
    width: 100%;
    height: calc(100% - 95px);
    display: flex;
    gap: 20px;
    border: 1px solid $border-color;

    &_anchor {
      width: 265px;
      height: 100%;
      border-right: 1px solid $border-medium;
    }

    &_content {
      width: calc(100% - 300px);
      height: 100%;
      padding: 10px 0;
    }

    &_options {
      height: 280px;
      @include heading-reset;

      &_btn {
        @include flex-between;
        span {
          @include button-small;
          display: inline-block;
        }
      }

      &_button {
        @include button-base;
        width: 230px;
        line-height: normal;
        box-sizing: border-box;
        text-align: left;
        padding: 4px 12px;
      }
    }

    &_optionsTable {
      height: calc(100% - 345px);
      overflow-y: auto;
      @include custom-scrollbar;
      @include heading-reset;

      &_btn {
        @include flex-between;
        span {
          @include button-small;
          display: inline-block;
        }
      }
    }

    &_button {
      @include button-base;
      width: 300px;
    }
  }
}

.mb_13 {
  margin-bottom: 13px;
}

.mb_5 {
  margin-bottom: 5px;
  &:hover {
    background-color: $bg-hover;
  }
}

.egls {
  :global {
    .wui-form {
      &-item{
        padding: 10px;
        margin-bottom: 0;
        &:hover {
          background-color: $bg-hover;
        }
        &__label {
          font-weight: bold;
        }
      }
    }
  }
  padding-bottom: 10px;
}

.modelBox {
  @include flex-between;
}

.tableEmpty {
  width: 100%;
  height: 100%;
  @include flex-center;
}

.popupStyle {
  @include flex-center;
  gap: 5px;
  margin: 25px 0;
  font-size: 18px;
  font-weight: bold;
  background-color: $bg-popup;
}
