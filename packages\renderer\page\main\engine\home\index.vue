<template>
  <div :class="styles.box">
    <div :class="styles.box_search">
      <div :class="styles.box_search_title">Home</div>
      <div :class="styles.box_search_searchImg">
        <wui-input v-model="SearchInput" placeholder="Search Setting" clearable />
        <div :class="styles.box_search_searchImg_image">
          <img src="/packages/renderer/assets/search.png" alt="" />
        </div>
      </div>
    </div>
    <div :class="styles.box_menu">
      <div :class="styles.box_menu_anchor">
        <wui-anchor :container="containerRef" ref="anchorRef" :offset="150" @click="handleClick">
          <wui-anchor-link href="#engine" title="Engine Options" />
          <wui-anchor-link href="#valid" title="Valid Engine Dash Numbers" />
        </wui-anchor>
      </div>
      <div ref="containerRef" :class="styles.box_menu_content">
        <EngineList :class="styles.box_menu_options" id="engine" />
        <wui-divider border-style="dashed" />
        <EngineTable :class="styles.box_menu_optionsTable" id="valid" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import styles from './index.module.scss'
import { ref, onMounted, nextTick } from 'vue'
import EngineList from './engineList/index.vue'
import EngineTable from './engineTable/index.vue'

const SearchInput = ref('')
const containerRef = ref<HTMLElement | null>(null)
const handleClick = (e: MouseEvent) => {
  e.preventDefault()
}
const anchorRef = ref()
onMounted(() => {
  nextTick(() => {
    window.scrollTo(0, 0)
    anchorRef.value.scrollTo('#engine')
  })
})
</script>
