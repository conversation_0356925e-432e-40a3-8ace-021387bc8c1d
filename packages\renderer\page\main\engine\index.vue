<template>
  <main-layout :class="styles.engine">
    <template #header>
      <Engine :list="engines" :current="engine" />
    </template>
    <template #footer><div /></template>
    <MenuLayout :class="styles.engine_main">
      <router-view v-slot="{ Component }">
        <component :is="Component" />
      </router-view>
    </MenuLayout>
  </main-layout>
</template>

<script setup lang="ts" name="Engine">
import styles from './index.module.scss'
import { MainLayout } from '@/renderer/components'
import MenuLayout from './menu/index.vue'
import Engine from './engine/index.vue'
import { computed, onMounted, ref } from 'vue'
import { useBizMain, useHandler } from '@/renderer/hooks'
import { BizMain } from '@/renderer/logic'

const mainPtr = useBizMain()

const engine = ref('')
const engines = computed(() => {
  return [{ label: engine.value, value: engine.value }]
})

useHand<PERSON>(mainPtr, BizMain.onEnginesChanged, () => {
  engine.value = mainPtr.value?.engine?.name || ''
})

onMounted(async () => {
  engine.value = mainPtr.value?.engine?.name || ''
})
</script>
