.enginebar {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #ffffff;

  &_select {
    width: 100%;
    color: #000000;
    font-weight: 700;
    margin-bottom: 10px;
  }
}
.box {
  width: 100%;
  height: 100%;
  position: relative;

  --engine-menu: 250px;

  &_menu {
    position: absolute;
    left: 0px;
    top: 0px;
    width: var(--engine-menu);
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 10px;

    .wui-menu-item {
      font-size: 12px;
      font-weight: 400;
      font-family: Alata;
      gap: 10px;
      height: 44px !important;
      padding-left: 50px;
      --wui-menu-hover-bg-color: none !important;
    }
    .wui-menu-item.is-active {
      color: #3d3d3d;
    }
    .wui-menu--inline {
      .sublevelStyle {
        background-color: #eaf1fd;
      }
    }
    .wui-sub-menu {
      --wui-menu-hover-bg-color: none !important;
    }
    .wui-sub-menu__title {
      gap: 10px;
      height: 44px !important;
    }

    &::-webkit-scrollbar {
      width: 6px;
    }
    &::-webkit-scrollbar-thumb {
      background-color: #dee0e2;
      border-radius: 10px;
    }
    &::-webkit-scrollbar-thumb:hover {
      background-color: #cccccc;
      cursor: pointer;
    }

    .subMenuStyle {
      background-color: #6282c1;
    }
    .parentFont {
      font-weight: 900;
      color: #ffffff;
    }
    .defaultSubFont {
      position: absolute;
      left: 44px;
    }
    .defaultSubFont_active {
      position: absolute;
      left: 50px;
    }
    .sublevelFont {
      width: 81%;
      height: 30px;
      line-height: 30px;
      position: absolute;
      left: 1px;
      padding-left: 43px;
      font-weight: 900;
      color: #6282c1;
      background-color: #eaf1fd;
    }
    .childrenFont {
      width: 78%;
      height: 30px;
      line-height: 30px;
      position: absolute;
      left: 1px;
      padding-left: 48px;
      font-weight: 900;
      color: #6282c1;
      background-color: #f6f6f6;
    }
  }

  &_content {
    position: absolute;
    left: var(--engine-menu);
    width: calc(100% - var(--engine-menu));
    height: 100%;
  }
}
