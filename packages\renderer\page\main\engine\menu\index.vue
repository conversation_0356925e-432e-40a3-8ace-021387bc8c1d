<template>
  <div :class="styles.box">
    <wui-menu :class="styles.box_menu" default-active="1">
      <component
        :is="(item.items?.length && WuiSubMenu) || WuiMenuItem"
        v-for="item in kMenuItems"
        :key="item.key"
        :index="item.key"
        :style="{
          backgroundColor: `${(hasSelected(item.key) && '#6282c1') || '#ffffff'}`,
          color: `${(hasSelected(item.key) && '#ffffff') || '#000000'}`
        }"
        @click="item.router && handleOpen(item.router, item.key)"
      >
        <template #title>
          <img v-if="item.icon" :src="item.icon?.(hasSelected(item.key))" />
          <span :class="{ [styles.parentFont]: hasSelected(item.key) }" style="font-size: 14px">{{
            item.label
          }}</span>
        </template>
        <component
          :is="(sub.items?.length && WuiSubMenu) || WuiMenuItem"
          v-for="sub in item.items"
          :key="sub.key"
          :index="sub.key"
          :title-style="{
            backgroundColor:
              (sub.items?.length && hasSelected(sub.key) && 'rgba(98, 130, 193, 0.1)') || undefined
          }"
          @click="sub.router && handleOpen(sub.router, sub.key)"
        >
          <template #title>
            <img v-if="sub.icon" :src="sub.icon?.(hasSelected(sub.key))" />
            <span
              :class="{
                [styles.sublevelFont]: !sub.items?.length && hasSelected(sub.key),
                [styles.defaultSubFont]: !sub.items?.length && !hasSelected(sub.key)
              }"
              style="font-size: 14px"
            >
              {{ sub.label }}
            </span>
          </template>
          <wui-menu-item
            v-for="ssub in sub.items"
            :key="ssub.key"
            :index="ssub.key"
            @click="ssub.router && handleOpen(ssub.router, ssub.key)"
          >
            <div
              :class="{
                [styles.sublevelFont]: hasSelected(ssub.key),
                [styles.defaultSubFont]: !hasSelected(ssub.key)
              }"
              style="font-size: 13px"
            >
              {{ ssub.label }}
            </div>
          </wui-menu-item>
        </component>
      </component>
    </wui-menu>
    <div :class="styles.box_content">
      <slot />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import styles from './index.module.scss'
import { kMenuItems } from './consts'
import { WuiSubMenu, WuiMenuItem } from '@wuk/wui'

const router = useRouter()
const currentKey = ref<string>()

const handleOpen = (name: string, index: string) => {
  currentKey.value = index
  router.push({ name })
}

const hasSelected = (key: string) => {
  return !!currentKey.value?.startsWith(key)
}

onMounted(() => {
  handleOpen('engine_home', '1')
})
</script>

<style lang="scss" stylemodule>
.wui-menu-item {
  font-size: 12px;
  font-weight: 400;
  font-family: Alata;
  gap: 10px;
  height: 44px !important;
  padding-left: 50px;
  --wui-menu-hover-bg-color: none !important;
}
.wui-menu-item.is-active {
  color: #3d3d3d;
}
.wui-menu--inline {
  .sublevelStyle {
    background-color: #eaf1fd;
  }
}
.wui-sub-menu {
  --wui-menu-hover-bg-color: none !important;
}
.wui-sub-menu__title {
  gap: 10px;
  height: 44px !important;
}
</style>
