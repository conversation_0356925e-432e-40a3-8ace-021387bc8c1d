import { PageRouter } from '@/renderer/logic'
import { RouteRecordRaw } from 'vue-router'

const kRouter: RouteRecordRaw[] = [
  {
    path: '/home',
    name: PageRouter.PR_ENGINE_HONE,
    component: () => import('./home/<USER>')
  },
  {
    path: '/eEdito',
    name: 'engine_displays_edito',
    component: () => import('./displays/editor/index.tsx')
  },
  {
    path: '/list',
    name: 'engine_displays_list',
    component: () => import('./displays/list/index.vue')
  },
  {
    path: '/descriptons',
    name: 'engine_displays_descriptons',
    component: () => import('./displays/descriptons/index.vue')
  },
  {
    path: '/attributes',
    name: 'engine_displays_attributes',
    component: () => import('./displays/attributes/index.vue')
  },
  {
    path: '/configuration',
    name: 'engine_devices_configuration',
    component: () => import('./devices/vlbsys/configuration/index.vue')
  },
  {
    path: '/signals',
    name: 'engine_devices_signals',
    component: () => import('./devices/vlbsys/signals/index.vue')
  },
  {
    path: '/vxi-channels',
    name: 'engine_devices_vxi_channels',
    component: () => import('./devices/vxiChannels/index.vue')
  },
  {
    path: '/plc',
    name: 'engine_devices_plc',
    component: () => import('./devices/plc/index.vue')
  },
  {
    path: '/initial',
    name: 'engine_calculations_initial',
    component: () => import('./calculations/calcs/initial/index.tsx')
  },
  {
    path: '/final',
    name: 'engine_calculations_final',
    component: () => import('./calculations/calcs/final/index.tsx')
  },
  {
    path: '/virtual',
    name: 'engine_calculations_virtual',
    component: () => import('./calculations/calcs/virtual/index.tsx')
  },
  {
    path: '/tables',
    name: 'engine_calculations_tables',
    component: () => import('./calculations/tables/index.vue')
  },
  {
    path: '/timers',
    name: 'engine_calculations_timers',
    component: () => import('./calculations/timers/index.vue')
  },
  {
    path: '/declaraions',
    name: 'engine_calculations_declaraions',
    component: () => import('./calculations/declaraions/index.vue')
  },
  {
    path: '/limits',
    name: 'engine_calculations_limits',
    component: () => import('./calculations/limits/index.vue')
  },
  {
    path: '/',
    redirect: '/home'
  }
]

export default kRouter
