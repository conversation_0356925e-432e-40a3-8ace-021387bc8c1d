<template>
  <div :class="styles.customer">
    <wui-select
      v-model="value"
      :placeholder="value"
      :class="styles.customer_select"
      :placeholder-class="styles.customer_select_placeholder"
      :wrapper-class="styles.customer_select_wrapper"
      @change="handleChanged"
    >
      <wui-option
        v-for="(it, index) in list"
        :key="index"
        :class="styles.customer_select_opt"
        :label="it.label"
        :value="it.value"
      />
    </wui-select>
  </div>
</template>

<script setup lang="ts" name="Customer">
import { computed, ref, defineProps, defineEmits, watchEffect } from 'vue'
import { customerProps, customerEmits } from './type'

import styles from './index.module.scss'

const value = ref('')

const props = defineProps(customerProps)
const emit = defineEmits(customerEmits)
const list = computed(() => props.list)

const handleChanged = (val: string) => {
  emit('change', val)
}

watchEffect(() => {
  const [val] = props.list
  value.value = props.current || val?.value || ''
})
</script>
