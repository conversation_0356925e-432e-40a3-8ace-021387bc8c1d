import { buildProps, definePropType } from '@wuk/wui/dist/utils'
import { ExtractPropTypes } from 'vue'

export interface CustomerItem {
  value: string
  label: string
}

export const customerProps = buildProps({
  list: {
    type: definePropType<CustomerItem[]>(Array),
    default: []
  },
  current: {
    type: String,
    default: ''
  }
} as const)
export type CustomerProps = ExtractPropTypes<typeof customerProps>

export const customerEmits = {
  change: (val: string) => undefined
}
export type CustomerEmits = typeof customerEmits
