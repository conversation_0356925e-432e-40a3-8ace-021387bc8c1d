@import '@/renderer/assets/variables';

.home {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  user-select: none;
  background-color: $app-primary-bg-color;
  color: $app-main-text-color;
  position: relative;

  &_main {
    display: flex;
    width: 100%;
    height: calc(100% - 58px);
  }

  &_view {
    width: calc(100% - 137px);
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    &_tab {
      display: flex;
      flex-direction: column;
      flex: 1;
      overflow: hidden;
      padding-bottom: 20px;
      box-sizing: border-box;
      height: 100%;
    }
    &_message {
      width: 100%;
      height: 100%;
      flex-shrink: 0;
      border-bottom: 1px solid #a2a6b0;
      border-right: 1px solid #a2a6b0;
      display: flex;

      &_box {
        width: 98.5%;
        height: 85%;
        font-size: 14px;
        font-weight: 500;
        margin: 15px 0;
        color: #ffffff;
        overflow-y: hidden;
        transition: overflow-y 0.3s ease;
        display: flex;
        flex-direction: column;
        gap: 3px;
        user-select: text !important;
        -webkit-user-select: text !important;
        -moz-user-select: text !important;
        -ms-user-select: text !important;

        &_content {
          width: 95.3%;
          height: 20px;
          padding-left: 20px;
          transition: all 0.1s ease;
          line-height: 20px;

          &:hover {
            // cursor: pointer;
            // padding: 5px 15px;
            // margin-left: 20px;
            background-color: #252525;
          }
        }

        &:hover {
          overflow-y: auto;
        }
        /* 滚动条宽度 */
        &::-webkit-scrollbar {
          width: 6px;
          height: 22px;
        }
        /* 滚动条的轨道 */
        &::-webkit-scrollbar-track {
          // background-color: #f1f1f1;
          border-radius: 10px;
        }
        /* 滚动条的滑块 */
        &::-webkit-scrollbar-thumb {
          background-color: #dee0e2;
          border-radius: 10px;
        }
        /* 滚动条滑块悬浮时的样式 */
        &::-webkit-scrollbar-thumb:hover {
          cursor: pointer;
          background-color: #cccccc;
        }
      }
    }
    &_resize_handle {
      position: relative;
      z-index: 2;
      transform: translateZ(1px);
      pointer-events: all;
      &:hover {
        &::before {
          background: #ccc;
          opacity: 0.5;
        }
      }
      &::after,&::before {
        position: absolute;
        content: "";
        width: 100%;
        height: 10px;
        top: 0;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 1;
      }
      &::after {
        width: 1px;
      }
    }
  }

  &_status {
    display: flex;
    width: 100%;
    height: 21px;
    min-height: 21px;
    border: 1px solid rgb(224, 227, 232);
    background-color: rgb(245, 246, 248);
  }
}
