<template>
  <MyDialog
    v-model="createShow"
    title="Create New Engine"
    @close="operationContext?.handleResetType"
    @ok="createSubmit"
  >
    <div :class="e('body')">
      <wui-form
        ref="createEngineFormRef"
        hide-required-asterisk
        :model="createEngineModel"
        label-suffix=":"
        :class="e('form')"
      >
        <wui-form-item
          label="Name"
          prop="engineName"
          :rules="toolRules.engineName"
          label-suffix=":"
          :error="createEngineError"
        >
          <wui-input
            v-model="createEngineModel.engineName"
            placeholder="Please input"
            style="width: 240px"
          />
        </wui-form-item>
      </wui-form>
    </div>
  </MyDialog>
</template>

<script lang="ts" setup>
import { reactive, ref, inject } from 'vue'
import MyDialog from '@/renderer/components/dialog/index.vue'
import styles from './index.module.scss'
import { useCore } from '@/renderer/hooks'
import { BizMain } from '@/renderer/logic'
import { FormInstance, WuiMessage } from '@wuk/wui'
import { useBem } from '@/renderer/hooks/bem'
import { operationContextKey } from '../constants'
const operationContext = inject(operationContextKey)
const createShow = ref(false)
const createEngineFormRef = ref<FormInstance>()
const mainPtr = useCore<BizMain>(BizMain)
const { e } = useBem('home_create', styles)
const createEngineModel = reactive({
  engineName: ''
})
const createEngineError = ref('')
const toolRules = {
  engineName: [
    { required: true, message: 'Please input engine name', trigger: 'blur' },
    { min: 1, max: 20, message: 'Length should be between 1 and 20', trigger: 'blur' },
    {
      pattern: /^[a-zA-Z0-9_]+$/,
      message: 'Only letters, numbers and underscores are allowed',
      trigger: 'blur'
    }
  ]
}
const tipsMessage = () => {
  WuiMessage({
    message: 'success',
    type: 'success',
    offset: 90
  })
}
const createSubmit = async () => {
  createEngineError.value = ''
  await createEngineFormRef.value?.validate(async valid => {
    if (!valid) return
    const result = await mainPtr.value?.createEngine(createEngineModel.engineName)
    if (!result) {
      createEngineError.value = 'The engine name cannot be repeated!'
      return
    }
    createShow.value = false
    operationContext?.handleResetType()
    tipsMessage()
  })
}

const handleCreateInit = () => {
  createEngineModel.engineName = ''
  createEngineError.value = ''
  createShow.value = true
}

defineExpose({
  handleCreateInit
})
</script>
