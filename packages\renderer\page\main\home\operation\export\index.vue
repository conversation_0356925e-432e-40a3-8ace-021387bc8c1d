<template>
  <MyDialog
    v-model="exportShow"
    title="Export Version"
    @close="operationContext?.handleResetType"
    @ok="onExport"
  >
    <div :class="e('body')">
      <wui-form hide-required-asterisk :model="exportModel" label-suffix=":" :class="e('form')">
        <wui-form-item label="Message" prop="exportName" label-suffix=":">
          <wui-input
            v-model="exportModel.exportName"
            placeholder="Please input"
            style="width: 240px"
          />
        </wui-form-item>
      </wui-form>
    </div>
  </MyDialog>
</template>

<script setup lang="ts">
import { inject, reactive, ref } from 'vue'
import MyDialog from '@/renderer/components/dialog/index.vue'
import styles from './index.module.scss'
import { useCore } from '@/renderer/hooks'
import { BizMain } from '@/renderer/logic'
import { WuiMessage } from '@wuk/wui'
import { useBem } from '@/renderer/hooks/bem'
import { operationContextKey } from '../constants'
const operationContext = inject(operationContextKey)
const { e } = useBem('home_export', styles)
const mainPtr = useCore<BizMain>(BizMain)
const exportShow = ref(false)
const exportModel = reactive({
  exportName: ''
})
const tipsMessage = () => {
  WuiMessage({
    message: 'success',
    type: 'success',
    offset: 90
  })
}
const onExport = async () => {
  const result = await mainPtr.value?.exportVersion(`${exportModel.exportName}`)
  if (!result) return
  exportShow.value = false
  operationContext?.handleResetType()
  tipsMessage()
}
const handleExportInit = () => {
  exportModel.exportName = ''
  exportShow.value = true
}
defineExpose({
  handleExportInit
})
</script>
