$namespace: 'home_import';
.#{$namespace} {
  &_overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
  }
  &_popup {
    width: 433px;
    height: 439px;
    position: absolute;
    left: 170px;
    top: 125px;
    padding: 20px 0;
    z-index: 2003;
    background-color: #efefef;
    text-align: center;
    border-radius: 7px;
    border: 1px solid #a1a5af;
    opacity: 1;
    transform: translateX(-100%);
    animation: popupAnimation 0.5s ease-out forwards;

    &_box {
      width: 393px;
      height: calc(100% - 55px);
      background-color: #000000;
      margin: 15px auto;
      padding-top: 1px;

      &_list {
        width: 96%;
        height: 93%;
        font-size: 13px;
        font-weight: bold;
        overflow-x: hidden;
        overflow-y: auto;
        margin: 15px 0 10px 0;
        color: #e5e5e5;

        /* 滚动条宽度 */
        &::-webkit-scrollbar {
          width: 6px;
        }
        /* 滚动条的滑块 */
        &::-webkit-scrollbar-thumb {
          height: 20px;
          background-color: #dee0e2;
          border-radius: 10px;
        }
        /* 滚动条滑块悬浮时的样式 */
        &::-webkit-scrollbar-thumb:hover {
          height: 20px;
          cursor: pointer;
          background-color: #cccccc;
        }

        &_content {
          width: 330px;
          height: 33px;
          line-height: 33px;
          cursor: pointer;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          border-radius: 4px;
          margin: 0 20px 5px 13px;

          &:hover {
            background-color: #17be08;
            padding: 0 10px;
          }

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }

    &::before {
      content: '';
      position: absolute;
      left: -19px;
      top: 40px;
      width: 0;
      height: 0;
      border: 10px solid transparent;
      border-right-color: #efefef;
    }
  }
  @keyframes popupAnimation {
    0% {
      transform: translateX(-100%);
      opacity: 0;
    }
    100% {
      transform: translateX(0);
      opacity: 1;
    }
  }
}
