<template>
  <div v-if="importShow" :class="e('overlay')" @click="onClose" />
  <div v-if="importShow" :class="e('popup')" @click.stop>
    <wui-input
      v-model="importInput"
      placeholder="Please input"
      style="width: 393px; height: 40px"
    />
    <div :class="e('popup', 'box')">
      <div :class="e('popup', 'box', 'list')">
        <div
          v-for="(item, index) in filterData"
          :key="index"
          :class="e('popup', 'box', 'list', 'content')"
          @click="onImport(item)"
        >
          <span :title="`${item.name} ${item.md5} ${item.comments}`">
            {{ `${item.name} ${item.md5} ${item.comments}` }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import styles from './index.module.scss'
import { CfgVersion } from '@wuk/cfg'
import { useCore } from '@/renderer/hooks'
import { BizMain } from '@/renderer/logic'
import { WuiMessage } from '@wuk/wui'
import { useBem } from '@/renderer/hooks/bem'
import { operationContextKey } from '../constants'
import { inject } from 'vue'
const operationContext = inject(operationContextKey)
const { e } = useBem('home_import', styles)
const mainPtr = useCore<BizMain>(BizMain)
const importShow = ref(false)
const importInput = ref('')
const versionsData = ref<CfgVersion[]>([])

const filterData = computed(() => {
  const queryValue = importInput.value.toLowerCase()
  return versionsData.value.filter(item => {
    return (
      item.name.toLowerCase().includes(queryValue) ||
      item.md5.toLowerCase().includes(queryValue) ||
      item.comments.toLowerCase().includes(queryValue)
    )
  })
})
const tipsMessage = () => {
  WuiMessage({
    message: 'success',
    type: 'success',
    offset: 90
  })
}
const onImport = async (item: CfgVersion) => {
  const result = await mainPtr.value?.importVersion(item.name)
  console.log('result', result)
  if (!result) return
  importShow.value = false
  operationContext?.handleResetType()
  tipsMessage()
}
const onClose = () => {
  importShow.value = false
  operationContext?.handleResetType()
}
const loadVersions = async () => {
  const result = (await mainPtr.value?.getVersions()) || []
  versionsData.value = result
}

const handleImportInit = () => {
  importInput.value = ''
  loadVersions()
  importShow.value = true
}

defineExpose({
  handleImportInit
})
</script>
