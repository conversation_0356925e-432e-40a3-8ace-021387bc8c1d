<template>
  <Create ref="createRef" />
  <Import ref="importRef" />
  <Export ref="exportRef" />
</template>

<script setup lang="ts">
import { defineAsyncComponent, ref, provide, watchPostEffect } from 'vue'
import { MainToolType } from '../tools'
import { operationContextKey } from './constants'
const Create = defineAsyncComponent(() => import('./create/index.vue'))
const Import = defineAsyncComponent(() => import('./import/index.vue'))
const Export = defineAsyncComponent(() => import('./export/index.vue'))
const createRef = ref<typeof Create>()
const importRef = ref<typeof Import>()
const exportRef = ref<typeof Export>()
const emits = defineEmits(['reset:type'])
const props = withDefaults(
  defineProps<{
    type: MainToolType | ''
  }>(),
  {
    type: ''
  }
)

watchPostEffect(() => {
  switch (props.type) {
    case 'Create':
      createRef.value?.handleCreateInit()
      break
    case 'Import':
      importRef.value?.handleImportInit()
      break
    case 'Export':
      exportRef.value?.handleExportInit()
      break
  }
})
const handleResetType = () => {
  emits('reset:type')
  console.log('reset type')
}
provide(operationContextKey, {
  handleResetType
})
</script>
