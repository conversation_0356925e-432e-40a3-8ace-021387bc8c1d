<template>
  <wui-dialog
    v-if="createShowRef"
    v-model="createShowRef"
    title="Create New Engine"
    width="380"
    align-center
    draggable
    :class="[styles.popuplayer, styles.popuplayer_engine]"
  >
    <wui-form ref="formRef" :model="engineData" :rules="createRules">
      <wui-form-item prop="name" label="name">
        <wui-input v-model="engineData.name" placeholder="Please enter engine name" />
      </wui-form-item>
    </wui-form>
    <template #footer>
      <div class="dialog-footer">
        <wui-button type="primary" @click="handleCreate(formRef)">Confirm</wui-button>
      </div>
    </template>
  </wui-dialog>

  <wui-dialog
    v-if="copyShowRef"
    v-model="copyShowRef"
    title="Copy Engine"
    width="380"
    align-center
    draggable
    :class="[styles.popuplayer, styles.popuplayer_engine]"
  >
    <wui-form ref="formRef" :model="engineData" :rules="createRules">
      <wui-form-item prop="name" label="name">
        <wui-input v-model="engineData.name" placeholder="Please enter new name" />
      </wui-form-item>
    </wui-form>
    <template #footer>
      <div class="dialog-footer">
        <wui-button type="primary" @click="handleCopy(formRef)">Confirm</wui-button>
      </div>
    </template>
  </wui-dialog>
</template>

<script setup lang="ts" name="PopupLayer">
import styles from './index.module.scss'
import {
  WuiButton,
  WuiDialog,
  WuiForm,
  WuiFormItem,
  WuiInput,
  FormInstance,
  WuiMessageBox
} from '@wuk/wui'
import { BizEngine, BizInvoke } from '@/renderer/logic'
import { onMounted, reactive, ref } from 'vue'
import { useBizEngine, useBizInvoke, useHandler } from '@/renderer/hooks'

const formRef = ref<FormInstance>()
const createShowRef = ref<boolean>()
const copyShowRef = ref<boolean>()
const currentRef = ref<string>()
const engineData = reactive<{ name: string }>({
  name: ''
})
const createRules = reactive({
  name: [{ required: true, message: 'Please enter engine name', trigger: 'blur' }]
})

const invokePtr = useBizInvoke()
const enginePtr = useBizEngine()

useHandler(invokePtr, BizInvoke.onCreateEngine, () => {
  createShowRef.value = true
})

useHandler(invokePtr, BizInvoke.onCopyEngine, () => {
  engineData.name = currentRef.value || ''
  copyShowRef.value = true
})

onMounted(() => {
  currentRef.value = enginePtr.value?.current
})

const handleCreate = (form?: FormInstance) => {
  form?.validate(async valid => {
    if (!valid || !enginePtr.value) return
    createShowRef.value = false
    enginePtr.value?.create(engineData.name)
  })
}

const handleCopy = (form?: FormInstance) => {
  form?.validate(async valid => {
    if (!valid || !enginePtr.value) {
      return
    }
    copyShowRef.value = false
    enginePtr.value?.copy(engineData.name)
  })
}
</script>
