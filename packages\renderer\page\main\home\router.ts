import { RouteRecordRaw } from 'vue-router'
import { PageRouter } from '@/renderer/logic'

const kRouter: RouteRecordRaw[] = [
  {
    path: '/welcome',
    name: PageRouter.PR_MAIN_HOME,
    component: () => import('./welcome/index.vue')
  },
  {
    path: '/setting',
    name: 'main_setting',
    component: () => import('./setting/index.vue')
  },
  {
    path: '/',
    redirect: '/welcome'
  }
]

export default kRouter
