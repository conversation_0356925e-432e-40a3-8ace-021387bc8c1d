import { ref } from 'vue'
import { FormRules } from '@wuk/wui'
import { validRange, setRuleErrorCallback } from '@/renderer/utils/rules'
export const useAudioRules = () => {
  const audioErrors = ref<Record<string, string>>({})
  const audioRules: FormRules = {
    hostAddress: { required: true, message: 'Please input hostAddress', trigger: 'change' },
    name: [{ required: true, message: 'Please input sound name', trigger: 'change' }],
    file: {
      required: true,
      message: 'Please select sound file',
      trigger: 'change'
    },
    repeat: [
      { required: true, message: 'Please input repeat', trigger: 'change' },
      { trigger: 'change', validator: validRange(0) }
    ]
  }
  const setAudioError = (key: string, value = '') => {
    setRuleErrorCallback(audioErrors, key, value)
  }
  return {
    audioErrors,
    setAudioError,
    audioRules
  }
}
