import { ref } from 'vue'
import { FormRules } from '@wuk/wui'
import { validRange, setRuleErrorCallback } from '@/renderer/utils/rules'
export const useColorRules = () => {
  const colorErrors = ref<Record<string, string>>({})
  const commonRuleCallback = (key: string) => [
    { required: true, message: `Please input ${key}`, trigger: 'change' },
    { trigger: 'change', validator: validRange(0, 255) }
  ]
  const colorRules: FormRules = {
    name: [{ required: true, message: 'Please input color name', trigger: 'change' }],
    r: commonRuleCallback('red'),
    g: commonRuleCallback('green'),
    b: commonRuleCallback('blue'),
    a: commonRuleCallback('alpha')
  }
  const setColorError = (key: string, value = '') => {
    setRuleErrorCallback(colorErrors, key, value)
  }
  return {
    colorErrors,
    colorRules,
    setColorError
  }
}
