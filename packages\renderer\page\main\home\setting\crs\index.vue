<template>
  <div>
    <h2>CRS Options</h2>
    <wui-form
      ref="crsFormRef"
      label-width="230"
      label-position="left"
      validate-msg-position="right"
      validate-ellipsis="2"
      hide-required-asterisk
      status-icon
      :rules="crsRules"
      :model="crsList"
      :show-validate-success="true"
      validate-success-tip="✓ Saved"
    >
      <wui-form-item label="Auto Backup" prop="auto_backup">
        <div :class="styles.ext">
          <wui-switch
            v-bind="setWuiSwitchAttrs()"
            v-model="crsList.auto_backup"
            @change="onCrsChange('auto_backup')"
          />
        </div>
      </wui-form-item>
      <wui-form-item label="Recording Control" prop="recording_control">
        <div :class="styles.ext">
          <wui-switch
            v-bind="setWuiSwitchAttrs()"
            v-model="crsList.recording_control"
            @change="onCrsChange('recording_control')"
          />
        </div>
      </wui-form-item>
      <wui-form-item label="Maximum number of tests" prop="maximum_number_of_tests">
        <div :class="styles.ext">
          <wui-input
            type="number"
            v-model="crsList.maximum_number_of_tests"
            @change="onCrsChange('maximum_number_of_tests')"
            placeholder="Please input"
          />
        </div>
      </wui-form-item>
      <wui-form-item label="Disk space to leave free (Mb)" prop="disk_space_to_leave_free">
        <div :class="styles.ext">
          <wui-input
            v-model="crsList.disk_space_to_leave_free"
            @change="onCrsChange('disk_space_to_leave_free')"
            placeholder="Please input"
          />
        </div>
      </wui-form-item>
      <wui-form-item label="File System" prop="file_system">
        <div :class="styles.ext">
          <wui-input
            v-model="crsList.file_system"
            @change="onCrsChange('file_system')"
            placeholder="Please input"
          />
        </div>
      </wui-form-item>
      <wui-form-item label="Continuous Recording" prop="continuous_recording.is_on">
        <div :class="styles.ext">
          <wui-switch
            v-bind="setWuiSwitchAttrs()"
            v-model="crsList.continuous_recording.is_on"
            @change="onCrsChange('continuous_recording.is_on')"
          />
        </div>
      </wui-form-item>
      <wui-form-item label="Number of mins" prop="continuous_recording.number_of_mins">
        <div :class="styles.ext">
          <wui-input
            v-model="crsList.continuous_recording.number_of_mins"
            @change="onCrsChange('continuous_recording.number_of_mins')"
            placeholder="Please input"
          />
        </div>
      </wui-form-item>
      <wui-form-item
        label="Continuous Division of scan rate"
        :class="styles.slider"
        prop="continuous_recording.division_of_scan_rate"
      >
        <div :class="styles.ext">
          <CustomSlider
            v-model:modelValue="crsList.continuous_recording.division_of_scan_rate"
            @change="onCrsChange('continuous_recording.division_of_scan_rate')"
          />
        </div>
      </wui-form-item>
      <wui-form-item label="Cyclic Recording" prop="cyclic_recording.is_on">
        <div :class="styles.ext">
          <wui-switch
            v-bind="setWuiSwitchAttrs()"
            v-model="crsList.cyclic_recording.is_on"
            @change="onCrsChange('cyclic_recording.is_on')"
          />
        </div>
      </wui-form-item>
      <wui-form-item label="Number of mins" prop="cyclic_recording.number_of_mins">
        <div :class="styles.ext">
          <wui-input
            v-model="crsList.cyclic_recording.number_of_mins"
            @change="onCrsChange('cyclic_recording.number_of_mins')"
            placeholder="Please input"
          />
        </div>
      </wui-form-item>
      <wui-form-item
        label="Cyclic Division of scan rate"
        :class="styles.slider"
        prop="cyclic_recording.division_of_scan_rate"
      >
        <div :class="styles.ext">
          <CustomSlider
            v-model:modelValue="crsList.cyclic_recording.division_of_scan_rate"
            @change="onCrsChange('cyclic_recording.division_of_scan_rate')"
          />
        </div>
      </wui-form-item>
    </wui-form>
  </div>
</template>

<script lang="ts" setup>
import { nextTick, onMounted, ref, toRaw } from 'vue'
import styles from '../index.module.scss'
import { useCore } from '@/renderer/hooks'
import { BizMain } from '@/renderer/logic'
import { CRSOptions } from '@wuk/cfg'
import { WuiForm, WuiFormItem } from '@wuk/wui'
import CustomSlider from '@/renderer/components/CustomSlider/index.vue'
import { setWuiSwitchAttrs } from '@/renderer/utils'
import { useCrsRules } from './rule'

const mainPtr = useCore<BizMain>(BizMain)
const crsFormRef = ref<InstanceType<typeof WuiForm>>()
const { crsRules, setCrsError } = useCrsRules()
const crsList = ref<CRSOptions>({
  auto_backup: false,
  recording_control: false,
  maximum_number_of_tests: '',
  disk_space_to_leave_free: '',
  file_system: '',
  continuous_recording: {
    is_on: false,
    number_of_mins: 0,
    division_of_scan_rate: 0
  },
  cyclic_recording: {
    is_on: false,
    number_of_mins: 0,
    division_of_scan_rate: 0
  }
})

const onCrsChange = async (key: string) => {
  setCrsError(key)
  const valid = await crsFormRef.value?.validateField(key)
  if (!valid) return
  const result = await mainPtr.value?.writeCRSOptions(toRaw(crsList.value))
  if (!result) {
    setCrsError(key, `${key} faild to save`)
    crsFormRef.value?.validateField(key)
    return
  }
}

onMounted(async () => {
  crsList.value = (await mainPtr.value?.readCRSOptions()) || ({} as CRSOptions)
  nextTick(() => {
    crsFormRef.value?.clearValidate()
  })
})
</script>
