import { ref } from 'vue'
import { FormRules } from '@wuk/wui'
import {
  validStrNumIntOrNumInt,
  validRange,
  validApi,
  setRuleErrorCallback
} from '@/renderer/utils/rules'
export const useCrsRules = () => {
  const crsErrors = ref<Record<string, string>>({})
  const crsRules: FormRules = {
    auto_backup: {
      validator: validApi(crsErrors.value),
      trigger: 'change'
    },
    recording_control: {
      trigger: 'change',
      validator: validApi(crsErrors.value)
    },
    maximum_number_of_tests: [
      {
        trigger: 'change',
        validator: validRange(0)
      },
      {
        trigger: 'change',
        validator: validStrNumIntOrNumInt
      },
      {
        trigger: 'change',
        validator: validApi(crsErrors.value)
      }
    ],
    disk_space_to_leave_free: [
      {
        trigger: 'change',
        validator: validRange(0)
      },
      {
        trigger: 'change',
        validator: validApi(crsErrors.value)
      }
    ],
    'continuous_recording.number_of_mins': [
      {
        trigger: 'change',
        validator: validRange(0)
      },
      {
        trigger: 'change',
        validator: validApi(crsErrors.value)
      }
    ],
    file_system: {
      trigger: 'change',
      validator: validApi(crsErrors.value)
    },
    'continuous_recording.is_on': {
      trigger: 'change',
      validator: validApi(crsErrors.value)
    },
    'continuous_recording.division_of_scan_rate': [
      {
        trigger: 'change',
        validator: validRange(0)
      },
      {
        trigger: 'change',
        validator: validApi(crsErrors.value)
      }
    ],
    'cyclic_recording.is_on': {
      trigger: 'change',
      validator: validApi(crsErrors.value)
    },
    'cyclic_recording.number_of_mins': [
      {
        trigger: 'change',
        validator: validRange(0)
      },
      {
        trigger: 'change',
        validator: validApi(crsErrors.value)
      }
    ],
    'cyclic_recording.division_of_scan_rate': [
      {
        trigger: 'change',
        validator: validRange(0)
      },
      {
        trigger: 'change',
        validator: validApi(crsErrors.value)
      }
    ]
  }
  const setCrsError = (key: string, value = '') => {
    setRuleErrorCallback(crsErrors, key, value)
  }
  return {
    crsRules,
    crsErrors,
    setCrsError
  }
}
