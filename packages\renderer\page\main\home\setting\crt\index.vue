<template>
  <div>
    <h2>Crts Options</h2>
    <wui-form
      ref="crtFormRef"
      label-width="230"
      label-position="left"
      validate-ellipsis="1"
      hide-required-asterisk
      inline-message
      status-icon
      validate-box-gap="3"
      validate-placement="bottom"
      :model="crtModel"
      :rules="crtRules"
      :show-validate-success="true"
      validate-success-tip="✓ Saved"
    >
      <wui-form-item label="Display Update Rate" prop="crtRate">
        <div :class="styles.ext">
          <wui-select
            v-model="crtModel.crtRate"
            placeholder="Select"
            @change="onCrtChange('crtRate')"
          >
            <wui-option
              v-for="item in options1"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </wui-select>
        </div>
      </wui-form-item>
      <h3>Crts List</h3>
      <wui-table
        border
        :class="styles.grid"
        :data="crtModel.crtList"
        style="width: 1000px"
        :header-cell-style="{
          background: '#EAF1FD',
          color: '#90AFE4',
          fontSize: '18px',
          fontWeight: 'bold'
        }"
        @row-contextmenu="handleRowMenu"
      >
        <wui-table-column label="Name" min-width="160px" align="center" show-overflow-tooltip>
          <template #default="{ row, $index }">
            <wui-form-item
              label-width="0"
              :prop="`crtList.${$index}.name`"
              :rules="crtRules.name"
              :show-validate-success="false"
              v-if="row.flag"
            >
              <wui-input v-model="row.name" clearable placeholder="Please input" />
            </wui-form-item>
            <span v-else>{{ row.name }}</span>
          </template>
        </wui-table-column>
        <wui-table-column label="X-Address" min-width="160px" align="center" show-overflow-tooltip>
          <template #default="{ row, $index }">
            <wui-form-item
              label-width="0"
              :prop="`crtList.${$index}.x_address`"
              :rules="crtRules.x_address"
              :show-validate-success="false"
              v-if="row.flag"
            >
              <wui-input
                v-model="row.x_address"
                clearable
                placeholder="Please input"
                style="width: 130px; margin-left: 0; height: 32px"
              />
            </wui-form-item>
            <span v-else>{{ row.x_address }}</span>
          </template>
        </wui-table-column>
        <wui-table-column label="Status" min-width="160px" align="center">
          <template #default="{ row }">
            <wui-select
              v-if="row.flag"
              v-model="row.status"
              placeholder="Select"
              style="width: 130px; margin-left: 0"
            >
              <wui-option
                v-for="item in options2"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </wui-select>
            <span v-else>{{ convertType(options2, row.status) }}</span>
          </template>
        </wui-table-column>
        <wui-table-column label="Resolution" min-width="160px" align="center">
          <template #default="{ row }">
            <wui-select
              v-if="row.flag"
              v-model="row.resolution"
              placeholder="Select"
              style="width: 130px; margin-left: 0"
            >
              <wui-option
                v-for="item in resolutions"
                :key="item.label"
                :label="item.label"
                :value="item.value"
              />
            </wui-select>
            <span v-else>{{ convertResolutions(row.resolution) }}</span>
          </template>
        </wui-table-column>
        <wui-table-column label="Control" min-width="160px" align="center">
          <template #default="{ row }">
            <wui-select
              v-if="row.flag"
              v-model="row.control"
              placeholder="Select"
              style="width: 130px; margin-left: 0"
            >
              <wui-option
                v-for="item in options4"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </wui-select>
            <span v-else>{{ convertType(options4, row.control) }}</span>
          </template>
        </wui-table-column>
        <wui-table-column label="Op" fixed="right" width="100px" align="center">
          <template #default="{ row, $index }">
            <TableTool.Op :flag="row.flag" @op="handleOp($event, row, $index)" />
          </template>
        </wui-table-column>
        <template #empty>
          <TableTool.Empty />
        </template>
      </wui-table>
    </wui-form>
  </div>
</template>

<script lang="ts" setup>
import { computed, onMounted, reactive, ref, nextTick, toRef } from 'vue'
import styles from '../index.module.scss'
import { useTableCommonMenu, useHandler, useBizMain } from '@/renderer/hooks'
import { BizMain } from '@/renderer/logic'
import { CrtOptions, CrtItem } from '@wuk/cfg'
import { WuiMessage, WuiForm, WuiFormItem } from '@wuk/wui'
import { convertType } from '@/renderer/utils/common'
import { useCrtRules } from './rule'
import TableTool, { OpType } from '@/renderer/components/TableTool'

interface newCrtItem extends CrtItem {
  meta?: CrtItem
  flag: boolean
  type: string
}
const crtFormRef = ref<InstanceType<typeof WuiForm>>()
const { crtRules, setCrtError } = useCrtRules()
const mainPtr = useBizMain()
// const crtRate = ref(0)
// const crtList = ref<newCrtItem[]>([])
const crtModel = reactive({
  crtRate: 0,
  crtList: [] as newCrtItem[]
})
const createRow = (): newCrtItem => ({
  name: '',
  x_address: '',
  status: 0,
  resolution: '',
  control: 0,
  flag: true,
  type: 'addType'
})
const options1 = [
  {
    label: '5 Hz',
    value: 5
  },
  {
    label: '10 Hz',
    value: 10
  }
]
const options2 = [
  {
    label: 'Active',
    value: 0
  },
  {
    label: 'Inactive',
    value: 1
  },
  {
    label: 'Control',
    value: 2
  }
]

const resolutions = computed(
  () =>
    mainPtr.value?.resolutions?.list().map(label => {
      return {
        label,
        value: mainPtr.value?.resolutions?.forKey(label)
      }
    }) || []
)

const convertResolutions = (val: string) => mainPtr.value?.resolutions?.forValue(val) || val

const options4 = [
  {
    label: 'None',
    value: 0
  },
  {
    label: 'Touchscreen',
    value: 1
  },
  {
    label: 'Nouse',
    value: 2
  },
  {
    label: 'Keyboard',
    value: 3
  }
]
const tipsMessage = () => {
  WuiMessage({
    message: 'success',
    type: 'success',
    offset: 90
  })
}
const onCrtChange = async (key: string) => {
  setCrtError(key)
  const valid = await crtFormRef.value?.validateField(key)
  if (!valid) return
  const result = await mainPtr.value?.setCrtRate(crtModel.crtRate)
  if (!result) {
    setCrtError(key, `faild to save`)
    crtFormRef.value?.validateField(key)
    return
  }
}

const handleOp = (op: OpType, row: newCrtItem, index: number) => {
  switch (op) {
    case 'edit':
      onEdit(row)
      break
    case 'cancel':
      onClose(row, index)
      break
    case 'select':
      onConfirm(row, index)
      break
    default:
      break
  }
}
// 新增事件
const onAdd = () => {
  crtModel.crtList.push(createRow())
}
// 编辑事件
const onEdit = (item: newCrtItem) => {
  if (item.flag) return
  item.flag = true
}

// 右键菜单
const { handleRowMenu } = useTableCommonMenu(
  toRef(crtModel, 'crtList'),
  async (key, ...args) => {
    const { row, rowIndex } = args[0]
    switch (key) {
      case 'addKey':
        onAdd()
        break
      case 'insertKey':
        crtModel.crtList.splice(rowIndex + 1, 0, { ...createRow(), type: 'insertType' })
        break
      case 'modifyKey':
        row && onEdit(row)
        break
      case 'deleteKey':
        {
          const removeResult = (await mainPtr.value?.removeCrt(rowIndex)) || false
          if (!removeResult) return
          tipsMessage()
        }
        break
      default:
        break
    }
  },
  [1],
  [
    { key: 'addKey', label: 'add' },
    { key: 'insertKey', label: 'insert' },
    { key: 'modifyKey', label: 'modify' },
    { key: 'deleteKey', label: 'delete' }
  ]
)

// 关闭事件
const onClose = (item: newCrtItem, index: number) => {
  const { type, meta = {} } = item
  const { name, x_address, status, resolution, control } = meta as CrtItem
  if (type === 'addType' || type === 'insertType') {
    crtModel.crtList.splice(index, 1)
  } else {
    item.name = name
    item.x_address = x_address
    item.status = status
    item.resolution = resolution
    item.control = control
    item.flag = false
  }
}
// 提交事件
const onConfirm = async (item: newCrtItem, index: number) => {
  const { name, x_address, status, resolution, control, type } = item
  const valids = await crtFormRef.value?.validateField([
    `crtList.${index}.name`,
    `crtList.${index}.x_address`
  ])
  if (!valids) return
  const data = { name, x_address, status, resolution, control }
  let editResult
  if (type === 'addType' || type === 'insertType') {
    editResult = await mainPtr.value?.addCrt(data, type === 'insertType' ? index : undefined)
  } else {
    editResult = await mainPtr.value?.modifyCrt(index, data)
  }
  if (!editResult) return
  item.flag = false
}
// 数据处理
const getDataInfo = async () => {
  const { rate, list = [] } = (await mainPtr.value?.readCrtOptions()) || ({} as CrtOptions)
  crtModel.crtRate = rate
  crtModel.crtList = list.map(item => {
    const meta = item
    const flag = false
    const type = ''
    return { ...item, meta, flag, type }
  })
}

useHandler(mainPtr, BizMain.onCrtOptionChanged, getDataInfo)

onMounted(async () => {
  await getDataInfo()
  nextTick(() => {
    crtFormRef.value?.clearValidate()
  })
})
</script>
