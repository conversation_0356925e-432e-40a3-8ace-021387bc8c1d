import { ref } from 'vue'
import { FormRules } from '@wuk/wui'
import {
  validStrNumIntOrNumInt,
  validRange,
  validApi,
  setRuleErrorCallback
} from '@/renderer/utils/rules'
export const useCrtRules = () => {
  const crtErrors = ref<Record<string, string>>({})
  const crtRules: FormRules = {
    crtRate: {
      trigger: 'change',
      validator: validApi(crtErrors.value)
    },
    name: [
      { required: true, message: 'Please input name', trigger: 'change' },
      { trigger: 'change', validator: validApi(crtErrors.value) }
    ],
    x_address: [
      { required: true, message: 'Please input x_address', trigger: 'change' },
      { trigger: 'change', validator: validApi(crtErrors.value) }
    ]
  }
  const setCrtError = (key: string, value = '') => {
    setRuleErrorCallback(crtErrors, key, value)
  }
  return {
    crtRules,
    crtErrors,
    setCrtError
  }
}
