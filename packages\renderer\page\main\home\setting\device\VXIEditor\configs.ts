import type { DialogConfig, NestedDialogContext } from '@/renderer/components/DynamicDIalog/types'

// CONDOR_429 设备的静态配置
export const CONDOR_429_CONFIG: Omit<DialogConfig, 'form' | 'table'> = {
  title: (context?: NestedDialogContext) => {
    const mainData = context?.mainData
    const currentDevice = context?.currentDevice
    return `${currentDevice?.name} Device Editor - Slot: ${mainData?.slot} Address: ${mainData?.address} EEC: ALL`
  },
  width: '900px',
  key: 'condor429',
  formTemplate: {
    columns: [
      {
        type: 'input',
        name: 'ctd',
        label: 'Clock Tic Duration(micro sec)',
        placeholder: 'Please Input'
      }
    ]
  },
  defaultValues: {
    form: (context?: NestedDialogContext) => {
      return {
        ctd: 250
      }
    },
    table: (context?: NestedDialogContext) => {
      return []
    }
  },
  tableTemplate: {
    columns: [
      {
        label: 'Channels',
        name: 'channels',
        type: 'input'
      },
      {
        label: 'Type',
        name: 'type',
        type: 'select',
        options: (context?: NestedDialogContext) => {
          return [
            { label: 'Type1', value: 'Type1' },
            { label: 'Type2', value: 'Type2' }
          ]
        }
      },
      {
        label: 'Bus Status',
        name: 'bus_status',
        type: 'input'
      },
      {
        label: 'Speed',
        name: 'speed',
        type: 'select',
        options: [
          { label: 'FAST', value: 'FAST' },
          { label: 'SLOW', value: 'SLOW' }
        ]
      },
      {
        label: 'Parity',
        name: 'parity',
        type: 'select',
        options: [
          { label: 'NONE', value: 'NONE' },
          { label: 'ODD', value: 'ODD' },
          { label: 'EVEN', value: 'EVEN' }
        ]
      },
      {
        label: 'Active',
        name: 'active',
        type: 'select',
        options: [
          { label: 'INACTIVE', value: '0' },
          { label: 'ACTIVE', value: '1' }
        ]
      },
      {
        label: 'Channel ID',
        name: 'channel_id',
        type: 'select',
        options: [
          { label: 'NONE', value: 'NONE' },
          { label: 'A', value: 'A' },
          { label: 'B', value: 'B' }
        ]
      }
    ]
  }
}

// CENCO_TACH 设备的静态配置
export const CENCO_TACH_CONFIG: Omit<DialogConfig, 'form' | 'table'> = {
  title: (context?: NestedDialogContext) => {
    const mainData = context?.mainData
    const currentDevice = context?.currentDevice
    return `${currentDevice?.name} CENCO_TACH Device Editor - Slot: ${mainData?.slot} Address: ${mainData?.address}`
  },
  key: 'cencoTach',
  tableTemplate: {
    columns: [
      {
        label: 'Channel',
        name: 'channel',
        type: 'input',
        width: '100px'
      },
      {
        label: 'Signal Name',
        name: 'signal_name',
        type: 'input'
      },
      {
        label: 'Calib Mode',
        name: 'calib_mode',
        type: 'select',
        options: [
          { label: 'VerifyOnly', value: 'VerifyOnly' },
          { label: 'None', value: 'None' }
        ]
      }
    ]
  },
  defaultValues: {
    table: (context?: NestedDialogContext) => {
      const currentDevice = context?.currentDevice
      const mainData = context?.mainData

      const result = Array.from({ length: 7 }, (_, index) => {
        const number = index.toString().padStart(3, '0')
        return {
          channel: index,
          signal_name: `${currentDevice?.name || ''}_TACH${mainData?.address || ''}_${number}`,
          calib_mode: 'VerifyOnly'
        }
      })
      return result
    }
  },
  nestedDialogs: {
    signalEditor: {
      title: (context?: NestedDialogContext) => {
        const currentDevice = context?.currentDevice
        const mainData = context?.mainData
        const index = context?.selectedRowIndex || 0
        const number = index.toString().padStart(3, '0')
        return `${currentDevice.name}_TACH${mainData?.address}_${number} Signal Editor`
      },
      key: 'cencoTach_nested1',
      defaultValues: {
        form: (context?: NestedDialogContext) => {
          const mainData = context?.mainData
          const channel = context?.selectedRow?.channel
          return {
            source: `Slot ${mainData?.slot},Address ${mainData?.address} CENCO_TACH Channel ${channel}, Frequency`,
            conversionType: 'none',
            signal_range: {
              minimum: '22',
              maximum: '33',
              units: '44'
            },
            scale_range: {
              minimum: '22',
              maximum: '33',
              units: '44'
            }
          }
        }
      },
      formTemplate: {
        columns: [
          {
            type: 'span',
            name: 'source',
            label: 'source'
          },
          {
            type: 'range',
            label: 'Signal Range',
            name: 'signal_range',
            labelWidth: '100'
          },
          {
            type: 'range',
            label: 'Scale Range',
            name: 'scale_range',
            labelWidth: '100',
            showLabel: false
          },
          {
            type: 'select',
            label: 'EU Conversion',
            name: 'conversionType',
            options: [
              { label: 'None', value: 'none' },
              { label: 'Polynomial', value: 'polynomial' }
            ]
          },
          {
            type: 'button',
            label: 'Edit EU Conversion',
            buttonType: 'primary',
            dynamicNestedDialogs: {
              dependsOn: 'conversionType',
              mapping: {
                polynomial: 'polynomialEditor'
              }
            }
          }
        ]
      },
      nestedDialogs: {
        polynomialEditor: {
          title: 'Polynomial Editor111',
          key: 'polynomialEditor',
          formTemplate: {
            columns: [
              {
                type: 'input',
                name: 'coefficient0',
                label: 'Coefficient 0',
                placeholder: 'Enter coefficient 0'
              }
            ]
          },
          defaultValues: {
            form: (context?: NestedDialogContext) => {
              return {
                coefficient0: 250
              }
            }
          }
        }
      }
    }
  }
}

export const DEVICE_CONFIGS = {
  CONDOR_429: CONDOR_429_CONFIG,
  CENCO_TACH: CENCO_TACH_CONFIG
  // 其他设备类型...
} as const
