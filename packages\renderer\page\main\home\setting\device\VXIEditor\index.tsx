import { computed, defineComponent, onMounted, reactive, ref, toRef, useModel } from 'vue'
import MyDialog from '@/renderer/components/dialog/index.vue'
import { useBem, useBizEngine, useHand<PERSON>, useTableCommonMenu } from '@/renderer/hooks'
import $styles from './index.module.scss'
import TableTool, { isAddOrInsertType, OpType } from '@/renderer/components/TableTool'
import { SetupDeviceRow } from '../type.ts'
import DynamicDIalog from '@/renderer/components/DynamicDIalog/index.tsx'
import { DEVICE_CONFIGS } from './configs'
import type { NestedDialogContext } from '@/renderer/components/DynamicDIalog/types'
import { convertType } from '@/renderer/utils/common.ts'
import {
  createConfigFromInterface,
  getResolvedConfigDefaultValues
} from '@/renderer/components/DynamicDIalog/utils.ts'
import { BizEngine } from '@/renderer/logic/index.ts'

export default defineComponent({
  name: 'VXIEditor',
  props: {
    showVXIEditor: {
      type: Boolean,
      default: false
    },
    currentDevice: {
      type: Object,
      default: () => {}
    }
  },
  emits: ['update:showVXIEditor'],
  setup(props) {
    const engPtr = useBizEngine()
    const showVXIEditor = useModel(props, 'showVXIEditor')
    let originList = [] as SetupDeviceRow[]
    const { b, e, m } = useBem('vxi-editor', $styles)
    const currentRow = ref()
    const model = reactive({
      list: [] as SetupDeviceRow[]
    })
    const createRow = (row_type: SetupDeviceRow['row_type']) => ({
      name: props.currentDevice.name,
      // slot: 0,
      card_type: '',
      card_address: '',
      no_hardware: false,
      flag: true,
      row_type
    })
    const { handleRowMenu } = useTableCommonMenu(
      toRef(model, 'list'),
      (key, ...args) => {
        const { row, rowIndex } = args[0]
        switch (key) {
          case 'addKey':
            model.list.push(createRow('add'))
            break
          case 'deleteKey':
            handleOp('delete', row, rowIndex)
            break
          case 'modifyKey':
            handleOp('edit', row, rowIndex)
            break
          case 'insertKey':
            model.list.splice(rowIndex + 1, 0, createRow('insert'))
            break
          case 'deviceEditor':
            if (!currentRow.value?.card) return
            dynamicShow.value = true
        }
      },
      [1],
      [
        { key: 'addKey', label: 'add' },
        { key: 'insertKey', label: 'insert' },
        { key: 'modifyKey', label: 'modify' },
        { key: 'deleteKey', label: 'delete' },
        { key: 'deviceEditor', label: 'Device Editor' }
      ]
    )
    const handleOp = (op: OpType, row: SetupDeviceRow | undefined, index: number) => {
      if (!row) return
      currentRow.value = row
      switch (op) {
        case 'edit':
          row.flag = true
          break
        case 'delete':
          handleDelete(index)
          break
        case 'select':
          handleSelect(row, index)
          row.row_type = '*'
          row.flag = false
          break
        case 'cancel':
          handleOpCancel(row, index)
          break
      }
    }
    const handleOpCancel = (row: SetupDeviceRow, index: number) => {
      if (isAddOrInsertType(row.row_type)) {
        model.list.splice(index, 1)
        return
      }
      model.list.splice(index, 1, { ...originList[index], flag: false, row_type: '*' })
    }
    const handleSelect = async (row: SetupDeviceRow, index: number) => {
      let res: boolean | undefined
      const { row_type, flag, ...params } = row
      const str = params.name + '.' + params.card_type + '.' + params.card_address
      if (row.row_type === 'add') {
        res = await engPtr.value?.addVxiCards(params)
      } else {
        res = await engPtr.value?.modifyVxiCards(str, params)
      }
      if (!res) return
      originList = JSON.parse(JSON.stringify(model.list))
    }
    const handleDelete = async (index: number) => {
      // const res = await engPtr.value?.removeVxiCards(index)
      // if (!res) return
      model.list.splice(index, 1)
    }
    const handleOk = async () => {
      showVXIEditor.value = false
    }

    const cardOption = [
      {
        label: 'CONDOR_429',
        value: 'CONDOR_429'
      },
      {
        label: 'CENCO_TACH',
        value: 'CENCO_TACH'
      }
    ]

    const testModeOption = [
      {
        label: 'With Hardware',
        value: 'With Hardware'
      },
      {
        label: 'No Hardware',
        value: 'No Hardware'
      }
    ]

    const dynamicShow = ref(false)

    const deviceConfigData = reactive<Record<string, any>>({})

    const getDeviceConfigData = (deviceType: string, context?: NestedDialogContext) => {
      if (!deviceConfigData[deviceType]) {
        const resolvedDefaults = getResolvedConfigDefaultValues(deviceType, DEVICE_CONFIGS, context)
        deviceConfigData[deviceType] = {
          formData: resolvedDefaults?.form || {},
          tableData: resolvedDefaults?.table || [],
          nestedData: {}
        }
      }
      return deviceConfigData[deviceType]
    }

    const dialogConfig = computed(() => {
      if (!currentRow.value?.card) return undefined

      const deviceType = currentRow.value.card

      // 传入自己所需要的上下文数据
      const context: NestedDialogContext = {
        currentDevice: props.currentDevice,
        mainData: currentRow.value
      }

      const configData = getDeviceConfigData(deviceType, context)

      return createConfigFromInterface(deviceType, DEVICE_CONFIGS, configData, context)
    })

    // API回调处理函数
    const createApiCallbacks = () => {
      return {
        onTableRowAdd: async (rowData: any, context?: NestedDialogContext) => {
          if (context?.nestedPath) {
            const pathInfo = context.nestedPath.map(node => ({
              dialogKey: node.dialogKey,
              selectedIndex: node.selectedRowIndex
            }))
          }

          return true
        },

        onTableRowUpdate: async (rowData: any, index: number, context?: NestedDialogContext) => {
          if (context?.nestedPath) {
            const pathInfo = context.nestedPath.map(node => ({
              dialogKey: node.dialogKey,
              selectedIndex: node.selectedRowIndex
            }))
            // await api.updateRowAtPath(rowData, index, context.nestedPath)
          }

          return true
        },

        onTableRowDelete: async (index: number, context?: NestedDialogContext) => {
          if (context?.nestedPath) {
            const pathInfo = context.nestedPath.map(node => ({
              dialogKey: node.dialogKey,
              selectedIndex: node.selectedRowIndex
            }))
            // await api.deleteRowAtPath(index, context.nestedPath)
          }

          return true
        },

        onDataSave: async (allData: any, context?: NestedDialogContext) => {
          if (!currentRow.value?.card) return false

          const deviceType = currentRow.value.card
          const configData = getDeviceConfigData(deviceType)

          return true
        },

        onDataUpdate: async (allData: any, context?: NestedDialogContext) => {
          if (!currentRow.value?.card) return false

          const deviceType = currentRow.value.card
          const configData = getDeviceConfigData(deviceType)

          return true
        },

        onNestedUpdate: async (updateData: any, nestedPath: any, context?: NestedDialogContext) => {
          if (!currentRow.value?.card) return false

          const deviceType = currentRow.value.card
          const configData = getDeviceConfigData(deviceType)

          const pathInfo = nestedPath.map((node: any) => ({
            dialogKey: node.dialogKey,
            selectedIndex: node.selectedRowIndex
          }))

          switch (updateData.type) {
            case 'form':
              if (nestedPath.length === 1) {
                configData.formData = { ...configData.formData, ...updateData.data }
              } else {
                const nestedKey = nestedPath[nestedPath.length - 1].dialogKey
                if (!configData.nestedData[nestedKey]) {
                  configData.nestedData[nestedKey] = { formData: {}, tableData: [] }
                }
                configData.nestedData[nestedKey].formData = {
                  ...configData.nestedData[nestedKey].formData,
                  ...updateData.data
                }
              }
              break
            case 'tableRow':
              if (nestedPath.length === 1) {
                if (configData.tableData[updateData.index]) {
                  configData.tableData[updateData.index] = { ...updateData.data }
                }
              } else {
                // 嵌套表格
                const nestedKey = nestedPath[nestedPath.length - 1].dialogKey
                if (configData.nestedData[nestedKey]?.tableData[updateData.index]) {
                  configData.nestedData[nestedKey].tableData[updateData.index] = {
                    ...updateData.data
                  }
                }
              }
              break
            case 'tableAdd':
              if (nestedPath.length === 1) {
                configData.tableData.push({ ...updateData.data })
              } else {
                const nestedKey = nestedPath[nestedPath.length - 1].dialogKey
                if (!configData.nestedData[nestedKey]) {
                  configData.nestedData[nestedKey] = { formData: {}, tableData: [] }
                }
                configData.nestedData[nestedKey].tableData.push({ ...updateData.data })
              }
              break
            case 'tableDelete':
              if (nestedPath.length === 1) {
                configData.tableData.splice(updateData.index, 1)
              } else {
                const nestedKey = nestedPath[nestedPath.length - 1].dialogKey
                if (configData.nestedData[nestedKey]?.tableData) {
                  configData.nestedData[nestedKey].tableData.splice(updateData.index, 1)
                }
              }
              break
          }
          return true
        },

        onBulkOperation: async (operation: any, context?: NestedDialogContext) => {
          // await api.bulkSave({
          //   operation: operation.type,
          //   data: operation.allDialogData
          // })

          return true
        },

        onFormSubmit: async (formData: Record<string, any>, context?: NestedDialogContext) => {
          if (context?.nestedPath) {
            const pathInfo = context.nestedPath.map(node => ({
              dialogKey: node.dialogKey,
              selectedIndex: node.selectedRowIndex
            }))
            // await api.submitFormAtPath(formData, context.nestedPath)
          }

          return true
        }
      }
    }

    const getDataInfo = async () => {
      const list = (await engPtr.value?.readVxiCardsOptions()) || []
      model.list = list.map(item => {
        const meta = item
        const flag = false
        const row_type = '*'
        return { ...item, meta, flag, row_type }
      })
      originList = JSON.parse(JSON.stringify(model.list))
    }

    onMounted(async () => {
      await getDataInfo()
    })

    useHandler(engPtr, BizEngine.onVxiCardsOptionsChanged, getDataInfo)

    return () => {
      return (
        <>
          <MyDialog
            width='800px'
            v-model={showVXIEditor.value}
            title={props.currentDevice.name + ' VXI Editor'}
            onOk={handleOk}>
            <div class={[e('body'), 'cfg-setup']}>
              <div class={['cfg-setup_table', e('body', 'table')]}>
                <wui-table onRow-contextmenu={handleRowMenu} border height='100%' data={model.list}>
                  {{
                    default: () => (
                      <>
                        <wui-table-column prop='name' label='Slot #' align='center'>
                          {{
                            default: ({ row }: any) => (
                              <>
                                <wui-slider
                                  v-model={row.slot}
                                  disabled={!row.flag}
                                  max={26}
                                  min={0}
                                  style={{ width: '100%' }}
                                />
                              </>
                            )
                          }}
                        </wui-table-column>
                        <wui-table-column prop='card_type' label='Card' align='center'>
                          {{
                            default: ({ row }: any) => (
                              <>
                                {row.flag ? (
                                  <wui-select v-model={row.card_type} placeholder='Select'>
                                    {cardOption.map(({ label, value }, index) => (
                                      <wui-option key={index} label={label} value={value} />
                                    ))}
                                  </wui-select>
                                ) : (
                                  convertType(cardOption, row.card_type)
                                )}
                              </>
                            )
                          }}
                        </wui-table-column>
                        <wui-table-column prop='card_address' label='Address' align='center'>
                          {{
                            default: ({ row }: any) => (
                              <>
                                {row.flag ? (
                                  <wui-input-number
                                    v-model={row.card_address}
                                    clearable
                                    controls={false}
                                    placeholder='Please Input'
                                  />
                                ) : (
                                  row.card_address
                                )}
                              </>
                            )
                          }}
                        </wui-table-column>
                        <wui-table-column prop='no_hardware' label='Test Mode' align='center'>
                          {{
                            default: ({ row }: any) => (
                              <>
                                {row.flag ? (
                                  <wui-select v-model={row.no_hardware} placeholder='Select'>
                                    {testModeOption.map(({ label, value }, index) => (
                                      <wui-option key={index} label={label} value={value} />
                                    ))}
                                  </wui-select>
                                ) : (
                                  convertType(testModeOption, row.no_hardware)
                                )}
                              </>
                            )
                          }}
                        </wui-table-column>
                        <wui-table-column width='100px' align='center'>
                          {{
                            default: ({ row, $index }: any) => (
                              <TableTool.Op
                                flag={row.flag}
                                onOp={op => handleOp(op, row, $index)}
                              />
                            )
                          }}
                        </wui-table-column>
                      </>
                    ),
                    empty: () => <TableTool.Empty />
                  }}
                </wui-table>
              </div>
            </div>
          </MyDialog>
          {dynamicShow.value && (
            <DynamicDIalog
              v-model:visible={dynamicShow.value}
              config={dialogConfig.value}
              apiCallbacks={createApiCallbacks()}
              onForm-change={(formData: Record<string, any>) => {
                // 实时保存表单数据变化
                if (currentRow.value?.card) {
                  const deviceType = currentRow.value.card
                  const configData = getDeviceConfigData(deviceType)
                  configData.formData = { ...configData.formData, ...formData }
                  console.log('实时保存表单数据:', configData.formData)
                }
              }}
              onTable-change={(tableData: any[]) => {
                // 实时保存表格数据变化
                if (currentRow.value?.card) {
                  const deviceType = currentRow.value.card
                  const configData = getDeviceConfigData(deviceType)
                  configData.tableData = [...tableData]
                  console.log('实时保存表格数据:', configData.tableData)
                }
              }}
            />
          )}
        </>
      )
    }
  }
})
