<template>
  <div>
    <h2>Device Options</h2>
    <wui-form
      ref="deviceFormRef"
      label-width="0"
      label-position="left"
      validate-ellipsis="2"
      hide-required-asterisk
      inline-message
      status-icon
      validate-box-gap="3"
      validate-placement="bottom"
      :model="deviceModel"
      :rules="deviceRules"
    >
      <h3>Device List</h3>
      <wui-table
        border
        :class="styles.grid"
        :data="deviceModel.deviceList"
        :header-cell-style="{
          background: '#EAF1FD',
          color: '#90AFE4',
          fontSize: '18px',
          fontWeight: 'bold'
        }"
        @row-contextmenu="handleRowMenu"
      >
        <wui-table-column label="Type" width="130px" align="center">
          <template #default="{ row, $index }">
            <wui-form-item
              v-if="row.flag"
              :prop="`deviceList.${$index}.type`"
              :rules="deviceRules.type"
            >
              <wui-input
                v-model="row.type"
                clearable
                placeholder="Please input"
                style="width: 100%; margin-left: 0; height: 32px"
              />
            </wui-form-item>
            <span v-else>{{ row.id }}</span>
          </template>
        </wui-table-column>
        <wui-table-column label="Name" width="120px" align="center" show-overflow-tooltip>
          <template #default="{ row, $index }">
            <wui-form-item
              v-if="row.flag"
              :prop="`deviceList.${$index}.name`"
              :rules="deviceRules.name"
            >
              <wui-input
                v-model="row.name"
                clearable
                placeholder="Please input"
                style="width: 100%; margin-left: 0; height: 32px"
              />
            </wui-form-item>
            <span v-else>{{ row.name }}</span>
          </template>
        </wui-table-column>
        <wui-table-column label="Interface" min-width="100px" align="center">
          <template #default="{ row }">
            <wui-select
              v-if="row.flag"
              v-model="row.inter"
              placeholder="Select"
              style="width: 100%; margin-left: 0"
            >
              <wui-option
                v-for="item in options12"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </wui-select>
            <span v-else>{{ convertType(options12, row.inter) }}</span>
          </template>
        </wui-table-column>
        <wui-table-column label="Scan Rate" min-width="100px" align="center" show-overflow-tooltip>
          <template #default="{ row, $index }">
            <wui-form-item
              v-if="row.flag"
              :prop="`deviceList.${$index}.scan_rate`"
              :rules="deviceRules.scan_rate"
            >
              <wui-input-number
                v-model="row.scan_rate"
                clearable
                :min="1"
                :max="200"
                :step="1"
                :controls="false"
                placeholder="Please input"
                style="width: 100%; margin-left: 0; height: 32px"
              />
            </wui-form-item>
            <span v-else>{{ row.scan_rate }}</span>
          </template>
        </wui-table-column>
        <wui-table-column label="Test Mode" min-width="250px" align="center">
          <template #default="{ row }">
            <wui-select
              v-if="row.flag"
              v-model="row.inst_addr"
              placeholder="Select"
              style="width: 100%; margin-left: 0"
            >
              <wui-option
                v-for="item in options13"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </wui-select>
            <span v-else>{{ convertType(options13, row.inst_addr) }}</span>
          </template>
        </wui-table-column>
        <wui-table-column label="Op" fixed="right" width="100px" align="center">
          <template #default="{ row, $index }">
            <TableTool.Op :flag="row.flag" @op="handleOp($event, row, $index)" />
          </template>
        </wui-table-column>
        <template #empty>
          <TableTool.Empty />
        </template>
      </wui-table>
    </wui-form>
    <VXIEditor
      v-if="showVXIEditor"
      v-model:showVXIEditor="showVXIEditor"
      :current-device="currentDevice"
    />
  </div>
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref, shallowRef, toRef, triggerRef } from 'vue'
import styles from '../index.module.scss'
import { useHandler, useBizMain, useTableCommonMenu } from '@/renderer/hooks'
import { BizMain } from '@/renderer/logic'
import { HardwareInfo } from '@wuk/cfg'
import { WuiMessage, WuiForm } from '@wuk/wui'
import { convertType } from '@/renderer/utils/common'
import TableTool, { OpType } from '@/renderer/components/TableTool'
import VXIEditor from './VXIEditor'

import { useDeviceRules } from './rule'
interface newHardware extends HardwareInfo {
  meta?: HardwareInfo
  flag: boolean
  table_type: string
}
const deviceFormRef = ref<InstanceType<typeof WuiForm>>()
const { deviceRules } = useDeviceRules()
const mainPtr = useBizMain()
const createRow = (): newHardware => ({
  name: '',
  inter: '',
  scan_rate: 0,
  inst_addr: 0,
  flag: true,
  table_type: 'addType',
  type: 'VXI'
})
const deviceModel = reactive({
  deviceList: [] as newHardware[]
})

const showVXIEditor = ref(false)

const currentDevice = ref()

const options12 = [
  {
    label: 'LAN',
    value: 'LAN'
  },
  {
    label: 'GPIB',
    value: 'GPIB'
  },
  {
    label: 'Serial',
    value: 'Serial'
  },
  {
    label: 'Parallel',
    value: 'Parallel'
  }
]
const options13 = [
  {
    label: 'Test With Device Hardware',
    value: 0
  },
  {
    label: 'No Device Hardware',
    value: 1
  }
]
const tipsMessage = () => {
  WuiMessage({
    message: 'success',
    type: 'success',
    offset: 90
  })
}

// 新增事件
const onAdd = () => {
  deviceModel.deviceList.push(createRow())
}
// 编辑事件
const onEdit = (item: newHardware) => {
  if (item.flag) return
  item.flag = true
}

const handleOp = (op: OpType, row: newHardware, index: number) => {
  switch (op) {
    case 'edit':
      onEdit(row)
      break
    case 'cancel':
      onClose(row, index)
      break
    case 'select':
      onConfirm(row, index)
      break
    default:
      break
  }
}

const { handleRowMenu } = useTableCommonMenu(
  toRef(deviceModel, 'deviceList'),
  async (key, ...args) => {
    const { row, rowIndex } = args[0]
    switch (key) {
      case 'addKey':
        onAdd()
        break
      case 'insertKey':
        deviceModel.deviceList.splice(rowIndex + 1, 0, { ...createRow(), table_type: 'insertType' })
        break
      case 'modifyKey':
        row && onEdit(row)
        break
      case 'deleteKey':
        const removeResult = await mainPtr.value?.removeHardware(rowIndex)
        if (!removeResult) return
        deviceModel.deviceList.splice(rowIndex, 1)
        tipsMessage()
        break
      case 'deviceEditor':
        row && onDeviceEditor(row)
        break
      default:
        break
    }
  },
  [1],
  [
    { key: 'addKey', label: 'add' },
    { key: 'insertKey', label: 'insert' },
    { key: 'modifyKey', label: 'modify' },
    { key: 'deleteKey', label: 'delete' },
    { key: 'deviceEditor', label: 'Device Editor' }
  ]
)
// 关闭事件
const onClose = (item: newHardware, index: number) => {
  const { table_type, meta = {} } = item
  const { name, inter, scan_rate, inst_addr } = meta as HardwareInfo
  if (table_type === 'addType' || table_type === 'insertType') {
    deviceModel.deviceList.splice(index, 1)
  } else {
    item.name = name
    item.inter = inter
    item.scan_rate = scan_rate
    item.inst_addr = inst_addr
    item.flag = false
  }
}
// 提交事件
const onConfirm = async (item: newHardware, index: number) => {
  const { name, inter, scan_rate, inst_addr, type, table_type } = item
  const valids = await deviceFormRef.value?.validateField([
    `deviceList.${index}.name`,
    `deviceList.${index}.scan_rate`,
    `deviceList.${index}.type`
  ])
  if (!valids) return
  const data = { name, inter, scan_rate, inst_addr, type }
  let editResult
  if (table_type === 'addType' || table_type === 'insertType') {
    editResult = await mainPtr.value?.addHardware(
      data,
      table_type === 'insertType' ? index : undefined
    )
  } else {
    editResult = await mainPtr.value?.modifyHardware(index, data)
  }
  if (!editResult) return
  item.flag = false
}

// 数据处理
const getDataInfo = async () => {
  const list = (await mainPtr.value?.readHardwares()) || ([] as HardwareInfo[])
  deviceModel.deviceList = list.map(item => {
    const meta = item
    const flag = false
    const table_type = ''
    return { ...item, meta, flag, table_type }
  })
}

useHandler(mainPtr, BizMain.onDeviceOptionChanged, getDataInfo)

const onDeviceEditor = (row: newHardware) => {
  currentDevice.value = row
  switch (row.type) {
    case 'VXI':
      showVXIEditor.value = true
      break
    default:
      break
  }
}

onMounted(async () => {
  await getDataInfo()
})
</script>
