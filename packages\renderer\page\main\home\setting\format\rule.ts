import { ref } from 'vue'
import { FormRules } from '@wuk/wui'
import {
  validStrNumIntOrNumInt,
  validRange,
  validApi,
  setRuleErrorCallback
} from '@/renderer/utils/rules'
export const useFormatRules = () => {
  const formatErrors = ref<Record<string, string>>({})
  const commonRule = {
    trigger: 'change',
    validator: validApi(formatErrors.value)
  }
  const formatRules: FormRules = {
    time_format: commonRule,
    date_format: commonRule,
    param_color: commonRule
  }
  const setFormatError = (key: string, value = '') => {
    setRuleErrorCallback(formatErrors, key, value)
  }
  return {
    formatRules,
    formatErrors,
    setFormatError
  }
}
