.setting {
  width: 100%;
  height: 100%;
  overflow: hidden;
  border-top: 1px solid #a1a6af;
  border-left: 1px solid #a1a6af;
  border-right: 1px solid #a1a6af;
  border-radius: 4px 4px 0 0;

  .search {
    position: absolute;
    width: 385px;
    height: 32px;
    padding: 5px 0;
    left: 70%;
    top: -42px;
  }

  .box {
    width: 100%;
    height: 100%;
    display: flex;
    gap: 20px;
    background-color: #ffffff;

    &_anchor {
      width: 265px;
      height: 100%;
      overflow-y: hidden;
      transition: all 0.3s ease;
      border-right: 1px solid #dbdbdb;

      &:hover {
        overflow-y: auto;
      }
      /* 滚动条宽度 */
      &::-webkit-scrollbar {
        width: 6px;
      }
      /* 滚动条的轨道 */
      &::-webkit-scrollbar-track {
        background-color: #f1f1f1;
        border-radius: 10px;
      }
      /* 滚动条的滑块 */
      &::-webkit-scrollbar-thumb {
        background-color: #dee0e2;
        border-radius: 10px;
      }
      /* 滚动条滑块悬浮时的样式 */
      &::-webkit-scrollbar-thumb:hover {
        cursor: pointer;
        background-color: #cccccc;
      }
    }
    &_content {
      width: calc(100% - 295px);
      height: 100%;
      overflow-y: auto;

      /* 滚动条宽度 */
      &::-webkit-scrollbar {
        width: 6px;
      }

      /* 滚动条的滑块 */
      &::-webkit-scrollbar-thumb {
        background-color: #dee0e2;
        border-radius: 10px;
      }
      /* 滚动条滑块悬浮时的样式 */
      &::-webkit-scrollbar-thumb:hover {
        cursor: pointer;
        background-color: #cccccc;
      }
    }
    &_options {
      width: 100%;
      margin-bottom: 30px;

      &_item {
        margin-bottom: 13px;
      }
      &_checkedBox {
        width: 175px;
        margin-left: 20px;
      }

      &_btn {
        display: flex;
        justify-content: space-around;
        align-items: center;
        span {
          display: inline-block;
          width: 70px;
          height: 30px;
          line-height: 30px;
          text-align: center;
          cursor: pointer;
          border: 1px solid #d4d4d4;
          border-radius: 4px;
        }
      }

      h2 {
        margin: 10px 0;
      }
      h3 {
        margin: 0 0 15px;
      }
      h4 {
        margin: 0 0 5px 0;
        font-size: 14px;
        font-weight: 500;
      }
    }
    &_button {
      width: 300px;
      height: 30px;
      line-height: 30px;
      text-align: center;
      font-size: 14px;
      border-radius: 4px;
      border: 1px solid #d2d2d2;
      cursor: pointer;
    }
  }
  .mb_5 {
    margin-bottom: 5px;
    &:hover {
      background-color: #f8f8f8;
    }
  }
  :global {
    .wui-form-item {
      padding: 5px 0;
      &:hover {
        background-color: #f8f8f8;
      }
    }
  }
  .ext {
    width: 275px;
    display: flex;
    justify-content: end;
    position: relative;
  }
  .slider {
    margin-top: 35px;
  }

  .checkbox_wrapper {
    position: relative;
    display: inline-block;
    min-width: 200px;
  }
}
.modelBox {
  display: flex;
  gap: 1px;

  &_btn {
    width: 98px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    cursor: pointer;

    &:last-child {
      margin-left: 6px;
    }
    &:active {
      background-color: #cecece;
      transform: scale(0.98);
      transition: transform 0.1s ease-in-out;
    }
  }
}
.tableBg {
  padding: 5px;
  background-color: #ffffff;
}
.form {
  &_box {
    display: flex;
    margin-bottom: 10px;
    &_content {
      width: 200px;
      display: flex;
      align-items: baseline;
      span {
        display: inline-block;
      }
    }
    &_change {
      width: auto;
      align-items: center;
    }
  }
}
.grid {
  height: 300px;
  margin-right: 50px;
  margin-bottom: 50px;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  font-size: 14px;
  overflow: hidden;
  :global {
    .wui-input {
      margin-left: 0;
    }
    .wui-form-item {
      padding: 0;
      margin-bottom: 0;
      white-space: wrap;
      &__error {
        text-align: left;
      }
      &:hover {
        background-color: transparent;
      }
    }
  }
}
.tables,
.calcs {
  height: 600px;
  :global {
    .wui-form-item {
      padding: 0;
      &:hover {
        background-color: transparent;
      }
    }
  }
}
.pb_100 {
  margin-bottom: 100px;
}
