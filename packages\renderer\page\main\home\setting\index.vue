<template>
  <div :class="styles.setting">
    <div :class="styles.box">
      <div :class="styles.box_anchor" ref="anchorBoxRef">
        <wui-anchor ref="anchorRef" :container="containerRef" :offset="30" @click="handleClick">
          <wui-anchor-link href="#system" title="System Options" />
          <wui-anchor-link href="#print" title="Print Modes" />
          <wui-anchor-link href="#data" title="Data Formats" />
          <wui-anchor-link href="#crs" title="CRS Options" />
          <wui-anchor-link href="#crts" title="Crts Options" />
          <wui-anchor-link href="#audio" title="Audio Options" />
          <wui-anchor-link href="#device" title="Device Options" />
          <wui-anchor-link href="#color" title="Color Options" />
          <wui-anchor-link href="#calcs" title="System Calcs" />
          <wui-anchor-link href="#tables" title="System Tables" />
        </wui-anchor>
      </div>
      <div ref="containerRef" @scroll="handleScroll" :class="styles.box_content">
        <System id="system" :class="styles.box_options" />
        <Print id="print" :class="styles.box_options" />
        <Format id="data" :class="styles.box_options" />
        <Crs id="crs" :class="styles.box_options" />
        <Crt id="crts" :class="styles.box_options" />
        <Audio id="audio" :class="styles.box_options" />
        <Device id="device" :class="styles.box_options" />
        <Color id="color" :class="styles.box_options" />
        <Calcs id="calcs" :class="styles.box_options" />
        <Tables id="tables" :class="styles.box_options" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, inject, watch, onMounted, nextTick } from 'vue'
import styles from './index.module.scss'
import System from './system/index.vue'
import Print from './print/index.vue'
import Format from './format/index.vue'
import Crs from './crs/index.vue'
import Crt from './crt/index.vue'
import Audio from './audio/index.vue'
import Device from './device/index.vue'
import Color from './color/index.vue'
import Calcs from './calcs/index.tsx'
import Tables from './tables/index.tsx'
import { homeContextKey, type HomeContext } from '../index.vue'
import { useDebounceFn, useThrottleFn } from '@vueuse/core'
const anchorBoxRef = ref<HTMLElement | null>(null)
const containerRef = ref<HTMLElement | null>(null)
const homeContext = inject<HomeContext>(homeContextKey)
const handleClick = (e?: MouseEvent) => {
  e?.preventDefault()
  homeContext?.changeSearchKeyword('')
}

const sections: Record<string, string> = {
  system: '#system',
  print: '#print',
  data: '#data',
  crs: '#crs',
  crts: '#crts',
  audio: '#audio',
  device: '#device',
  color: '#color',
  calcs: '#calcs',
  tables: '#tables'
}
const anchorRef = ref()
onMounted(() => {
  nextTick(() => {
    window.scrollTo(0, 0)
    anchorRef.value.scrollTo('#system')
  })
})

watch(
  () => homeContext?.searchKeyword.value,
  newKeyword => {
    if (!newKeyword) return
    for (const key in sections) {
      if (!key.toLowerCase().includes(newKeyword.toLowerCase())) continue
      const href = sections[key]
      anchorRef.value.scrollTo(href)
      break
    }
  }
)
let precScrollTop = 0
const handleScroll = useThrottleFn((ev: Event) => {
  const target = ev.target as HTMLElement
  const { scrollTop } = target
  const delta = scrollTop - precScrollTop > 0 ? 5 : -5
  if (anchorBoxRef.value) {
    anchorBoxRef.value.scrollTop += delta
  }
  precScrollTop = scrollTop
}, 100)
</script>

<style lang="scss" stylemodule>
._tabview_content_vreep_47 {
  position: absolute;
}
.wui-anchor {
  margin-top: 40px;
}
.wui-anchor__item {
  height: 50px;
}
.wui-anchor__link {
  font-size: 14px !important;
  font-weight: normal;
  line-height: 41px;
  padding-left: 20px;
  color: #3d3d3d;
}
.wui-anchor__link:focus {
  color: #12151a;
}
.wui-anchor__link:hover {
  color: #12151a;
  background-color: #e6efff;
}
.wui-anchor__link.is-active {
  background-color: #b0cdff;
  color: #12151a;
  font-size: 18px !important;
  font-weight: 500;
  height: 100%;
  line-height: 41px;
  padding-left: 20px;
}
.wui-input__inner {
  height: 40px;
}
.wui-anchor__marker {
  display: none;
}
.wui-anchor.wui-anchor--vertical .wui-anchor__list {
  padding-left: 0;
}
.wui-switch {
  margin-left: 15px;
}
.wui-select {
  width: 230px;
  height: 32px;
}
.wui-select__wrapper {
  &:hover {
    box-shadow: 0 0 0 1px #b0cdff !important;
  }
}
.wui-input {
  width: 230px;
  height: 32px;
}
.wui-slider {
  width: 230px;
}
.wui-icon {
  cursor: pointer;
  &:hover {
    color: #0c65ff;
  }
}
.wui-popconfirm__action {
  display: flex;
  justify-content: center;
  .wui-button:nth-child(1) {
    display: block !important;
    background-color: #f0f0f0 !important;
    border: 1px solid #b6bece !important;
    color: #3d3d3d !important;
    &:hover {
      background-color: #909399 !important;
      color: #ffffff !important;
    }
  }
  .wui-button:nth-child(2) {
    background-color: #6282c1 !important;
    border: 1px solid #3c5992 !important;
    &:hover {
      background-color: rgba(98, 130, 193, 0.7) !important;
    }
  }
}
.wui-table {
  color: #3d3d3d;
  font-size: 18px;
  font-weight: bold;
  --wui-table-row-hover-bg-color: #b0cdff;
}
.wui-table__empty-text {
  width: 100%;
}
</style>
<style lang="scss" scoped>
.tabs {
  :deep(.wui-button) {
    color: #181010;
    --wui-button-hover-border-color: #d3d3d3;
    --wui-button-hover-bg-color: rgba(250, 250, 250, 0.1);
    --wui-button-hover-text-color: #181010;
    --wui-button-active-border-color: #dcdfe6;
  }
}
</style>
