<template>
  <div>
    <h2>Print Modes</h2>
    <wui-form
      ref="printFormRef"
      label-width="0"
      label-position="left"
      validate-msg-position="right"
      hide-required-asterisk
      status-icon
      :model="printList"
      :rules="printRules"
      :show-validate-success="true"
      validate-success-tip="✓ Saved"
    >
      <wui-form-item prop="print_messages">
        <div :class="styles.checkbox_wrapper">
          <wui-checkbox
            v-model="printList.print_messages"
            label="Print Messages"
            @change="onPrintChange('print_messages', $event)"
          />
        </div>
      </wui-form-item>
      <wui-form-item prop="print_stored_scans">
        <div :class="styles.checkbox_wrapper">
          <wui-checkbox
            v-model="printList.print_stored_scans"
            label="Print Stored Scans"
            @change="onPrintChange('print_stored_scans', $event)"
          />
        </div>
      </wui-form-item>
      <wui-form-item prop="print_stored_comments">
        <div :class="styles.checkbox_wrapper">
          <wui-checkbox
            v-model="printList.print_stored_comments"
            label="Print Stored Comment"
            @change="onPrintChange('print_stored_comments', $event)"
          />
        </div>
      </wui-form-item>
      <wui-form-item prop="print_stored_displays">
        <div :class="styles.checkbox_wrapper">
          <wui-checkbox
            v-model="printList.print_stored_displays"
            label="Print Stored Displays"
            @change="onPrintChange('print_stored_displays', $event)"
          />
        </div>
      </wui-form-item>
    </wui-form>
  </div>
</template>

<script lang="ts" setup>
import { nextTick, onMounted, ref } from 'vue'
import { useCore } from '@/renderer/hooks'
import { BizMain } from '@/renderer/logic'
import { PrintModeOptions } from '@wuk/cfg'
import styles from '../index.module.scss'
import { usePrintRules } from './rule'
import { WuiForm } from '@wuk/wui'
const printFormRef = ref<InstanceType<typeof WuiForm>>()
const mainPtr = useCore<BizMain>(BizMain)
const printList = ref<PrintModeOptions>({
  print_messages: false,
  print_stored_scans: false,
  print_stored_comments: false,
  print_stored_displays: false
})
const { printRules, setPrintError } = usePrintRules()

const onPrintChange = async (key: string, value: string | number | boolean) => {
  setPrintError(key)
  const valid = await printFormRef.value?.validateField(key)
  if (!valid) return
  const result = await mainPtr.value?.writePrintModes({
    [key]: value
  })

  if (!result) {
    setPrintError(key, `faild to save`)
    printFormRef.value?.validateField(key)
    return
  }
}

onMounted(async () => {
  printList.value = (await mainPtr.value?.readPrintModes()) || ({} as PrintModeOptions)
  nextTick(() => {
    printFormRef.value?.clearValidate()
  })
})
</script>
