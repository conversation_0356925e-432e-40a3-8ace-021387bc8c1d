import { ref } from 'vue'
import { FormRules } from '@wuk/wui'
import { validApi, setRuleErrorCallback } from '@/renderer/utils/rules'
export const usePrintRules = () => {
  const printErrors = ref<Record<string, string>>({})
  const commonRule = {
    trigger: 'change',
    validator: validApi(printErrors.value)
  }
  const printRules: FormRules = {
    print_messages: commonRule,
    print_stored_scans: commonRule,
    print_stored_comments: commonRule,
    print_stored_displays: commonRule
  }
  const setPrintError = (key: string, value = '') => {
    setRuleErrorCallback(printErrors, key, value)
  }
  return {
    printRules,
    printErrors,
    setPrintError
  }
}
