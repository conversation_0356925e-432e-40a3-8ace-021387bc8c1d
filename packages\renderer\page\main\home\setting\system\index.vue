<template>
  <div>
    <h2>System Options</h2>
    <wui-form
      ref="sysFormRef"
      label-width="230"
      label-position="left"
      validate-msg-position="right"
      validate-ellipsis="2"
      hide-required-asterisk
      status-icon
      :rules="sysRules"
      :model="systemList"
      :show-validate-success="true"
      validate-success-tip="✓ Saved"
    >
      <wui-form-item label="Initial Alarm State" prop="initial_alarm_state">
        <div :class="styles.ext">
          <wui-switch
            v-bind="setWuiSwitchAttrs()"
            v-model="systemList.initial_alarm_state"
            @change="onChange('initial_alarm_state', $event)"
          />
        </div>
      </wui-form-item>
      <wui-form-item label="Input Param Order" prop="input_param_order">
        <div :class="styles.ext">
          <wui-select
            v-model="systemList.input_param_order"
            placeholder="Select"
            @change="onChange('input_param_order', $event)"
          >
            <wui-option
              v-for="item in options1"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </wui-select>
        </div>
      </wui-form-item>
      <wui-form-item label="Reference Test" prop="reference_test">
        <div :class="styles.ext">
          <wui-switch
            v-model="systemList.reference_test"
            @change="onChange('reference_test', $event)"
            v-bind="setWuiSwitchAttrs()"
          />
        </div>
      </wui-form-item>
      <wui-form-item label="Automatic Open On Startup" prop="automatic_open_on_startup">
        <div :class="styles.ext">
          <wui-switch
            v-model="systemList.automatic_open_on_startup"
            @change="onChange('automatic_open_on_startup', $event)"
            v-bind="setWuiSwitchAttrs()"
          />
        </div>
      </wui-form-item>
      <wui-form-item label="Store Messages To Database" prop="store_messages_to_database">
        <div :class="styles.ext">
          <wui-switch
            v-model="systemList.store_messages_to_database"
            @change="onChange('store_messages_to_database', $event)"
            v-bind="setWuiSwitchAttrs()"
          />
        </div>
      </wui-form-item>
      <wui-form-item label="Rcs Control" prop="rcs_control">
        <div :class="styles.ext">
          <wui-switch
            v-model="systemList.rcs_control"
            @change="onChange('rcs_control', $event)"
            v-bind="setWuiSwitchAttrs()"
          />
        </div>
      </wui-form-item>
      <wui-form-item label="Test History" prop="test_history">
        <div :class="styles.ext">
          <wui-select
            v-model="systemList.test_history"
            placeholder="Select"
            @change="onChange('test_history', $event)"
          >
            <wui-option
              v-for="item in options2"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </wui-select>
        </div>
      </wui-form-item>
      <wui-form-item label="Invalid Value" prop="invalid_value">
        <div :class="styles.ext">
          <wui-input
            v-model="systemList.invalid_value"
            placeholder="Please input"
            @change="onChange('invalid_value', $event)"
          />
        </div>
      </wui-form-item>
      <wui-form-item label="Test ID Prompt" prop="test_id_prompt">
        <div :class="styles.ext">
          <wui-input
            v-model="systemList.test_id_prompt"
            placeholder="Please input"
            @change="onChange('test_id_prompt', $event)"
          />
        </div>
      </wui-form-item>
      <wui-form-item label="System Scan Rate" prop="system_scan_rate">
        <div :class="styles.ext">
          <wui-input
            v-model="systemList.system_scan_rate"
            placeholder="Please input"
            @change="onChange('system_scan_rate', $event)"
          />
        </div>
      </wui-form-item>
      <wui-form-item label="Trigger Control parameter" prop="trigger_control_paramter">
        <div :class="styles.ext">
          <wui-input
            v-model="systemList.trigger_control_paramter"
            placeholder="Please input"
            @change="onChange('trigger_control_paramter', $event)"
          />
        </div>
      </wui-form-item>
    </wui-form>
  </div>
</template>

<script lang="ts" setup>
import { nextTick, onMounted, ref } from 'vue'
import styles from '../index.module.scss'
import { useCore } from '@/renderer/hooks'
import { BizMain } from '@/renderer/logic'
import { SystemOptions } from '@wuk/cfg'
import { WuiForm } from '@wuk/wui'
import { useSysRules } from './rule'
import { setWuiSwitchAttrs } from '@/renderer/utils'
const { sysRules, setSysError } = useSysRules()
const sysFormRef = ref<InstanceType<typeof WuiForm>>()
const mainPtr = useCore<BizMain>(BizMain)
const systemList = ref<SystemOptions>({
  initial_alarm_state: true,
  input_param_order: 0,
  reference_test: true,
  automatic_open_on_startup: true,
  store_messages_to_database: true,
  rcs_control: true,
  test_history: 1,
  invalid_value: '',
  test_id_prompt: '',
  system_scan_rate: '',
  trigger_control_paramter: ''
})

const options1 = [
  {
    label: 'As Listed',
    value: 0
  },
  {
    label: 'Alphabetical',
    value: 1
  },
  {
    label: '1st/3rd',
    value: 2
  }
]
const options2 = [
  {
    label: 'Standard',
    value: 1
  },
  {
    label: 'No History Data',
    value: 2
  }
]

const onChange = async (key: string, value: string | number) => {
  setSysError(key)
  const valid = await sysFormRef.value?.validateField(key)
  if (!valid) return
  const result = await mainPtr.value?.writeSystemOptions({
    [key]: value
  })
  if (!result) {
    setSysError(key, `faild to save`)
    sysFormRef.value?.validateField(key)
    return
  }
}

onMounted(async () => {
  systemList.value = (await mainPtr.value?.readSystemOptions()) || ({} as SystemOptions)
  nextTick(() => {
    sysFormRef.value?.clearValidate()
  })
})
</script>
