import { ref } from 'vue'
import { FormRules } from '@wuk/wui'
import {
  validStrNumIntOrNumInt,
  validRange,
  validApi,
  setRuleErrorCallback
} from '@/renderer/utils/rules'
export const useSysRules = () => {
  const sysErrors = ref<Record<string, string>>({})
  const commonRule = {
    trigger: 'change',
    validator: validApi(sysErrors.value)
  }
  const sysRules: FormRules = {
    initial_alarm_state: commonRule,
    input_param_order: commonRule,
    reference_test: commonRule,
    automatic_open_on_startup: commonRule,
    store_messages_to_database: commonRule,
    rcs_control: commonRule,
    test_history: commonRule,
    invalid_value: [
      {
        trigger: 'change',
        validator: validStrNumIntOrNumInt
      },
      commonRule
    ],
    test_id_prompt: commonRule,
    system_scan_rate: [
      {
        trigger: 'change',
        validator: validRange(0)
      },
      commonRule
    ],
    trigger_control_paramter: commonRule
  }
  const setSysError = (key: string, value = '') => {
    setRuleErrorCallback(sysErrors, key, value)
  }
  return {
    sysRules,
    sysErrors,
    setSysError
  }
}
