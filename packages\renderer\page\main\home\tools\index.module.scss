@import '../../../../assets/variables';

.tools {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  font-size: 14px;

  &_item {
    &_iconStyle {
      width: 44px;
      height: 40px;
      font-size: 12px;
      font-weight: bold;
      margin-top: 5px;
      cursor: pointer;
    }

    &_imageStyle {
      width: 87.3px;
      height: 56.4px;
      border-radius: 3.6px;
      border: 1px solid #c8ceda;
      cursor: pointer;
      padding: 0;

      img {
        position: relative;
        top: 10px;
      }
      span {
        position: relative;
        bottom: 8px;
        font-size: 14px;
        font-weight: bold;
      }

      &:hover {
        background-color: #e6efff;
      }
    }
  }

  &_top {
    display: flex;
    width: 100%;
    flex-direction: column;
    align-items: center;
    margin-top: 20px;
    gap: 10px;
  }

  &_bottom {
    display: flex;
    width: 100%;
    flex-direction: column;
    align-items: center;
    margin-bottom: 20px;
  }
}
