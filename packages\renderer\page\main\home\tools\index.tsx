import { defineComponent } from 'vue'
import styles from './index.module.scss'

import { ToolBtn } from '@/renderer/components'
import CreateImg from './image/create.png'
import ImportImg from './image/import.png'
import ExportImg from './image/export.png'
import SettingImg from './image/setting.png'

export type MainToolType = 'Create' | 'Import' | 'Export' | 'Setting'

interface ToolItemProps {
  type: MainToolType
  text: string
  icon?: string
  image?: string
  width?: string
  height?: string
}

const kTopBtns: ToolItemProps[] = [
  {
    type: 'Create',
    text: 'Create',
    icon: 'DocumentAdd',
    image: CreateImg,
    width: '22px',
    height: '21px'
  },
  {
    type: 'Import',
    text: 'Import',
    icon: 'Download',
    image: ImportImg,
    width: '25px',
    height: '17px'
  },
  {
    type: 'Export',
    text: 'Export',
    icon: 'Upload',
    image: ExportImg,
    width: '25px',
    height: '17pxpx'
  }
]
const kBottomBtns: ToolItemProps[] = [
  {
    type: 'Setting',
    text: 'Setting',
    icon: 'Setting',
    image: SettingImg,
    width: '21px',
    height: '21px'
  }
]

export const toolsEmits = {
  toolClick: (type: MainToolType) => undefined
}
export type ToolEmits = typeof toolsEmits

export default defineComponent({
  name: 'MainTools',
  emits: toolsEmits,
  setup(props, { slots, emit }) {
    const handleToolClick = (type: MainToolType) => {
      emit('toolClick', type)
    }
    return () => (
      <div class={styles.tools}>
        <div class={styles.tools_top}>
          {kTopBtns.map((it, index) => (
            <ToolBtn
              text={it.text}
              class={styles.tools_item_imageStyle}
              key={`${index}-${it.text}`}
              image={it.image}
              imgWidth={it.width}
              imgHeight={it.height}
              onClick={() => handleToolClick(it.type)}
            />
          ))}
        </div>
        <div class={styles.tools_bottom}>
          {kBottomBtns.map((it, index) => (
            <ToolBtn
              text={it.text}
              class={styles.tools_item_imageStyle}
              key={`${index}-${it.text}`}
              image={it.image}
              imgWidth={it.width}
              imgHeight={it.height}
              onClick={() => handleToolClick(it.type)}
            />
          ))}
        </div>
      </div>
    )
  }
})
