@use '@wuk/wui/dist/styles/mixins/mixins' as *;
@import '@/renderer/assets/variables';

.welcome {
  width: 100%;

  &_version {
    width: 100%;
    height: 85px;
    margin-left: 4px;

    &_title {
      font-size: 28px;
      font-weight: bold;
      color: #3d3d3d;
    }

    &_content {
      display: flex;
      flex-direction: column;
      font-size: 15px;
      font-weight: normal;
      margin-top: 10px;
    }
  }

  &_engines {
    margin-top: 30px;
    height: calc(100% - 120px);

    &_title {
      margin-left: 4px;
      font-size: 24px;
      font-weight: bold;
      color: #223354;
    }

    &_content {
      width: 99%;
      height: 87%;
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(145px, 1fr));
      align-content: flex-start;
      gap: 20px;
      padding-right: 15px;
      margin-top: 20px;
      overflow-y: auto;

      /* 滚动条宽度 */
      &::-webkit-scrollbar {
        width: 6px;
      }
      /* 滚动条的轨道 */
      &::-webkit-scrollbar-track {
        background-color: #f1f1f1;
        border-radius: 10px;
      }
      /* 滚动条的滑块 */
      &::-webkit-scrollbar-thumb {
        background-color: #dee0e2;
        border-radius: 10px;
      }
      /* 滚动条滑块悬浮时的样式 */
      &::-webkit-scrollbar-thumb:hover {
        cursor: pointer;
        background-color: #cccccc;
      }
    }

    &_item {
      height: 190px;
      font-size: 17px;
      font-weight: bold;
      border-radius: 6px;
      text-align: center;
      cursor: pointer;
      color: #223354;
      background-color: #ffffff;
      box-shadow: 0px 9px 16px 0px rgba(159, 162, 191, 0.18),
        0px 2px 2px 0px rgba(159, 162, 191, 0.32);

      &_text {
        font-size: 21px;
        font-weight: bold;
      }

      img {
        width: 100px;
        height: 100px;
        margin: 20px 0 10px 0;
      }

      &:hover {
        color: #ffffff;
        background-color: #6282c1;
      }
    }
  }
}
