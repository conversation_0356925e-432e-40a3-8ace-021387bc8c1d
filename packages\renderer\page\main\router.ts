import { RouteRecordRaw, createRouter, createWebHashHistory } from 'vue-router'
import homeRounter from './home/<USER>'
import engineRounter from './engine/router'
import { PageRouter } from '@/renderer/logic'

const kRouter: RouteRecordRaw[] = [
  {
    path: '/main',
    name: PageRouter.PR_MAIN,
    component: () => import('./home/<USER>'),
    meta: {
      title: 'main'
    },
    children: homeRounter
  },
  {
    path: '/engine',
    name: PageRouter.PR_ENGINE,
    component: () => import('./engine/index.vue'),
    meta: {
      title: 'engine'
    },
    children: engineRounter
  },
  {
    path: '/',
    redirect: '/main'
  }
]

const router = createRouter({
  history: createWebHashHistory(),
  routes: kRouter,
  strict: false,
  scrollBehavior: () => ({ left: 0, top: 0 })
})
/**
 * @description 路由拦截 beforeEach
 * */
router.beforeEach(async (to, from, next) => {
  // 10.正常访问页面
  next()
})

/**
 * @description 路由跳转错误
 * */
router.onError(error => {
  console.warn('路由错误', error.message)
})

/**
 * @description 路由跳转结束
 * */
router.afterEach(() => {})

export default router
