import {
  inject,
  provide,
  InjectionKey,
  ref,
  onUnmounted,
  defineComponent,
  Ref,
  watch,
  onMounted,
  isRef
} from 'vue'
import EventEmitter from 'eventemitter3'

export interface StoreRootValue {
  map: Map<StoreState<any>, StoreValue<any>>
  get<T>(state: StoreState<T>): T
  set<T>(state: StoreState<T>, value: T): boolean
}

export interface StoreValue<T> {
  value: NonNullable<T>
  update(value: T): boolean
  isUpdate(value: T): boolean
  updateComputed(): void
  subscribe(callback: () => void): void
  unsubscribe(callback?: () => void): void
}

export type StoreCompute = <T>(state: StoreState<T>) => T

export interface StoreState<T> {
  module: string
  id: number
  defaultValue: T
  compute?(e: { get: StoreCompute }): T
}

export type StoreStateType<T extends StoreState<any>> = T extends StoreState<infer U> ? U : never

const StoreRootKey = Symbol() as InjectionKey<StoreRootValue>

let id = 0

function clone(value: any) {
  return JSON.parse(JSON.stringify(value))
}

function getStoreValue(root: StoreRootValue, state: StoreState<any>, hooksDefaultValue?: any) {
  const defaultValue = state.defaultValue

  if (!root.map.has(state)) {
    const emitter = new EventEmitter()
    let cachedValue = hooksDefaultValue
    if (hooksDefaultValue && isRef(hooksDefaultValue)) {
      cachedValue = hooksDefaultValue.value

      onMounted(() => {
        update(hooksDefaultValue.value)
      })

      watch(hooksDefaultValue, newVal => {
        update(newVal)
      })
    }
    cachedValue = cachedValue ?? (defaultValue == null ? defaultValue : clone(state.defaultValue))
    let unsubscribes: Array<() => void> = []

    const isUpdate = (value: any) => {
      return value !== cachedValue
    }
    const update = (value: any) => {
      if (!isUpdate(value)) {
        return false
      }
      cachedValue = value
      emitter.emit('update')
      return true
    }
    const updateComputed = () => {
      if (state.compute) {
        unsubscribes.forEach(unsubscribe => unsubscribe())
        unsubscribes = []
        update(state.compute({ get }))
      }
    }
    const get = (childState: StoreState<any>) => {
      const store = getStoreValue(root, childState)
      const onUpdate = () => {
        updateComputed()
      }

      unsubscribes.push(() => {
        store.unsubscribe(onUpdate)
      })

      store.subscribe(onUpdate)
      return store.value
    }

    if (state.compute) {
      cachedValue = state.compute({ get })
    }
    root.map.set(state, {
      get value() {
        return cachedValue
      },
      set value(nextValue: any) {
        cachedValue = nextValue
      },
      updateComputed,
      update,
      isUpdate,
      subscribe(callback: () => void) {
        emitter.on('update', callback)
      },
      unsubscribe(callback: () => void) {
        emitter.off('update', callback)
      }
    })
  }
  const result = root.map.get(state)!

  if (hooksDefaultValue) {
    result.value = hooksDefaultValue
  }
  return result
}

export function createAtom(module: string) {
  return <T>(defaultValue: T) => {
    const value: StoreState<T> = {
      module,
      id: ++id,
      defaultValue
    }
    return value
  }
}

export function createCompute(module: string) {
  return <T>(callback: (e: { get: StoreCompute }) => T) => {
    const value: StoreState<T> = {
      module,
      id: ++id,
      defaultValue: null as any,
      compute: callback
    }
    return value
  }
}

export function useStoreRoot() {
  return inject(StoreRootKey)!
}

export function createModuleRoot(module: string) {
  const clearStates = () => {
    const root = useStoreRoot()
    if (!root) {
      return
    }
    const states = Array.from(root.map.keys()).filter(key => key.module === module)
    states.forEach(state => root.map.delete(state))
  }
  const atom = createAtom(module)
  return { clearStates, atom }
}

export function useStoreValue<T>(
  state: StoreState<T>,
  hooksDefaultValue?: NonNullable<T> | Ref<T>
): StoreValue<T> {
  const root = useStoreRoot()
  return getStoreValue(root, state, hooksDefaultValue)
}

export function useStoreState<T>(state: StoreState<T>) {
  const value = useStoreStateValueRef(state) as Ref<T>
  const setValue = useStoreStateSetValue(state)

  return [value, setValue] as const
}

export function useStoreStateSetValue<T>(state: StoreState<T>) {
  const storeValue = useStoreValue(state)

  return (value: T) => {
    storeValue.update(value)
  }
}

export function useStoreStateSetPromise<T>(state: StoreState<T>): (value: T) => Promise<boolean> {
  const value = useStoreStateValue(state)
  const storeValue = useStoreValue(state)
  const queue = ref<Array<(value: boolean) => void>>([])

  watch(value, () => {
    queue.value.forEach(resolve => {
      resolve(true)
    })
    queue.value.length = 0
  })

  return (value: T) => {
    if (storeValue.isUpdate(value)) {
      return new Promise(resolve => {
        queue.value.push(resolve)
        storeValue.update(value)
      })
    }
    return Promise.resolve(false)
  }
}

export function useStoreStateValue<T>(state: StoreState<T>) {
  return useStoreStateValueRef(state) as Ref<T>
}

function useStoreStateValueRef<T>(state: StoreState<T>) {
  const storeValue = useStoreValue(state)
  // const value = (isRef(storeValue.value) && storeValue.value) || ref<NonNullable<T>>(storeValue.value)
  const value = ref<NonNullable<T>>(storeValue.value)

  // if (isRef(storeValue.value)) {
  //   watch(storeValue.value, () => {
  //     value.value = storeValue.value
  //   })
  // }

  storeValue.subscribe(() => {
    value.value = storeValue.value
  })

  onUnmounted(() => {
    storeValue.unsubscribe()
  })

  return value
}

// StoreRoot 组件
export const StoreRoot = defineComponent({
  name: 'StoreRoot',
  setup(_, { slots }) {
    const rootValue: StoreRootValue = {
      map: new Map(),
      get<T>(state: StoreState<T>): T {
        const storeValue = getStoreValue(rootValue, state)
        return storeValue.value
      },
      set<T>(state: StoreState<T>, value: T): boolean {
        const storeValue = getStoreValue(rootValue, state)
        return storeValue.update(value)
      }
    }
    provide(StoreRootKey, rootValue)

    return () => slots.default?.()
  }
})
