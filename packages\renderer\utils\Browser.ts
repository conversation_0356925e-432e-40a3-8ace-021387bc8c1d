export default class Browser {
  private static _canvas: HTMLCanvasElement | null = null
  private static _dpi = 0

  static get canvas(): HTMLCanvasElement {
    return Browser._canvas || (Browser._canvas = Browser.createElement('canvas'))
  }

  static addChild(child: any) {
    document.body.appendChild(child.element)
  }

  static get context() {
    const context = Browser.canvas.getContext('2d')
    // context.clearRect(0, 0, canvas.width, canvas.height);
    return context
  }

  static dataURLtoBlob(dataURL: string): Blob {
    // convert base64 to raw binary data held in a string
    // doesn't handle URLEncoded DataURIs - see SO answer #6850276 for code that does this
    const byteString = atob(dataURL.split(',')[1])

    // separate out the mime component
    const mimeString = dataURL.split(',')[0].split(':')[1].split(';')[0]

    // write the bytes of the string to an ArrayBuffer
    const ab = new ArrayBuffer(byteString.length)

    // create a view into the buffer
    const ia = new Uint8Array(ab)

    // set the bytes of the buffer to the correct values
    for (let i = 0; i < byteString.length; i++) {
      ia[i] = byteString.charCodeAt(i)
    }

    // write the ArrayBuffer to a blob, and you're done
    return new Blob([ab], { type: mimeString })
  }

  static toDataURL(
    source: CanvasImageSource,
    x?: number,
    y?: number,
    w?: number,
    h?: number,
    type?: string
  ): string {
    Browser.canvas.width = w || 0
    Browser.canvas.height = h || 0
    Browser.context?.drawImage(source, x || 0, y || 0, w || 0, h || 0, 0, 0, w || 0, h || 0)
    return Browser.canvas.toDataURL(type || 'image/png')
  }

  static drawGrid(
    context: CanvasRenderingContext2D,
    source: CanvasImageSource,
    rect: { left: number; top: number; width: number; height: number },
    left?: number,
    top?: number,
    right?: number,
    bottom?: number,
    width?: number,
    height?: number
  ) {
    if (!context || !width || !height) return
    const x = rect.left
    const y = rect.top
    const w = rect.width
    const h = rect.height

    left = left || 0
    top = top || 0
    right = right || 0
    bottom = bottom || 0

    left && top && context.drawImage(source, x, y, left, top, 0, 0, left, top)
    top &&
      context.drawImage(
        source,
        x + left,
        y,
        w - left - right,
        top,
        left,
        0,
        width - left - right,
        top
      )
    right &&
      top &&
      context.drawImage(source, x + w - right, y, right, top, width - right, 0, right, top)
    left &&
      context.drawImage(
        source,
        x,
        y + top,
        left,
        h - bottom - top,
        0,
        top,
        left,
        height - bottom - top
      )
    context.drawImage(
      source,
      x + left,
      y + top,
      w - right - left,
      h - bottom - top,
      left,
      top,
      width - right - left,
      height - bottom - top
    )
    right &&
      context.drawImage(
        source,
        x + w - right,
        y + top,
        right,
        h - bottom - top,
        width - right,
        top,
        right,
        height - bottom - top
      )
    left &&
      bottom &&
      context.drawImage(source, x, y + h - bottom, left, bottom, 0, height - bottom, left, bottom)
    bottom &&
      context.drawImage(
        source,
        x + left,
        y + h - bottom,
        w - left - right,
        bottom,
        left,
        height - bottom,
        width - right - left,
        bottom
      )
    right &&
      bottom &&
      context.drawImage(
        source,
        x + w - right,
        y + h - bottom,
        right,
        bottom,
        width - right,
        height - bottom,
        right,
        bottom
      )
  }

  static fromGrid(
    source: CanvasImageSource,
    rect: { left: number; top: number; width: number; height: number },
    left?: number,
    top?: number,
    right?: number,
    bottom?: number,
    width?: number,
    height?: number,
    type?: string
  ): string {
    Browser.canvas.width = width || 0
    Browser.canvas.height = height || 0
    Browser.context &&
      Browser.drawGrid(Browser.context, source, rect, left, top, right, bottom, width, height)

    return Browser.canvas.toDataURL(type || 'image/png')
  }

  static createElement(element: any) {
    return document.createElement(element)
  }

  static get dpi(): number {
    if (Browser._dpi == 0) {
      const arrDPI: number[] = []
      const { deviceXDPI, deviceYDPI } = window.screen as Record<string, any>
      if (deviceXDPI) {
        arrDPI[0] = deviceXDPI
        arrDPI[1] = deviceYDPI
      } else {
        const tmpNode = document.createElement('div')
        tmpNode.style.cssText =
          'width:1in;height:1in;position:absolute;left:0px;top:0px;z-index:99;visibility:hidden'
        document.body.appendChild(tmpNode)
        arrDPI[0] = tmpNode.offsetWidth
        arrDPI[1] = tmpNode.offsetHeight
        tmpNode.parentNode?.removeChild(tmpNode)
      }
      Browser._dpi = arrDPI[0]
    }

    return Browser._dpi
  }
}
