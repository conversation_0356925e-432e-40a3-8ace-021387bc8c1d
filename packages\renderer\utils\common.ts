import { createApp, h, ref } from 'vue'
import ParameterDialog from '@/renderer/components/ParameterDialog/index.vue'

// 转换类型
export const convertType = (
  data: {
    label: string
    value: string | number
  }[],
  type: string | number
) => {
  const option = data.find(item => item.value == type)
  return option ? option.label : '--'
}
// 深拷贝
export const deepCopy = (target: any) => {
  if (typeof target !== 'object' || target === null) return target
  if (target instanceof Date) return target
  const isArray = Array.isArray(target)
  if (isArray) {
    const result = [] as any
    target.forEach((value, index) => {
      result[index] = deepCopy(value)
    })
    return result
  }
  const result = {} as any
  for (const key in target) {
    if (target.hasOwnProperty(key)) {
      result[key] = deepCopy(target[key])
    }
  }
  return result
}
// 函数式弹窗组件
export const useParameterDialog = () => {
  return new Promise<string>(resolve => {
    const mountNode = document.createElement('div')
    document.body.appendChild(mountNode)
    const closeDialog = (selectedValue?: string) => {
      app.unmount()
      document.body.removeChild(mountNode)
      if (selectedValue !== undefined) {
        resolve(selectedValue)
      }
    }
    const app = createApp({
      setup() {
        const dialogVisible = ref(true)
        const handleSubmit = (value: string) => {
          closeDialog(value)
        }
        const onClose = () => {
          closeDialog()
        }
        return () =>
          h(ParameterDialog, {
            visible: dialogVisible.value,
            onClose,
            onSubmit: handleSubmit
          })
      }
    })
    app.mount(mountNode)
  })
}

export function transformToCoordinates(data: number[], dim: number) {
  const result = []
  const length = data.length

  if (dim === 2) {
    for (let i = 0; i < length; i += 2) {
      const coord1 = data[i] ?? null
      const coord2 = data[i + 1] ?? null
      result.push({ coord1, coord2 })
    }
  } else if (dim === 3) {
    for (let i = 0; i < length; i += 3) {
      const coord1 = data[i] ?? null
      const coord2 = data[i + 1] ?? null
      const coord3 = data[i + 2] ?? null
      result.push({ coord1, coord2, coord3 })
    }
  }
  return result
}

export function convertDspName(name: string) {
  return name
    .split('_')
    .map(part => part.charAt(0).toUpperCase() + part.slice(1))
    .join(' ')
}
