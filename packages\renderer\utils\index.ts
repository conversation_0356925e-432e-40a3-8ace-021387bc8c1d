import { PropType } from 'vue'

export const PI_2 = Math.PI * 2
export const RAD_TO_DEG = 180 / Math.PI
export const DEG_TO_RAD = Math.PI / 180
export const URL_FILE_EXTENSION = /\.(\w{3,4})(?:$|\?|#)/i
export const DATA_URI =
  /^\s*data:(?:([\w-]+)\/([\w+.-]+))?(?:;charset=([\w-]+))?(?:;(base64))?,(.*)/i
export const SVG_SIZE = // eslint-disable-line
  /<svg[^>]*(?:\s(width|height)=('|")(\d*(?:\.\d+)?)(?:px)?('|"))[^>]*(?:\s(width|height)=('|")(\d*(?:\.\d+)?)(?:px)?('|"))[^>]*>/i
export const RETINA_PREFIX = /@([0-9\.]+)x/

export function hex2rgb(hex: number, out: number[]) {
  out = out || []

  out[0] = ((hex >> 16) & 0xff) / 255
  out[1] = ((hex >> 8) & 0xff) / 255
  out[2] = (hex & 0xff) / 255

  return out
}

export function hex2string(hex: number): string {
  let result = hex.toString(16)
  result = '000000'.substr(0, 6 - result.length) + result

  return `#${result}`
}

export function rgb2hex(rgb: number[]): number {
  return ((rgb[0] * 255) << 16) + ((rgb[1] * 255) << 8) + ((rgb[2] * 255) | 0)
}

export function rgba2string(rgb: number[]): string {
  const [r = 0, g = 0, b = 0, a = 255] = rgb
  return `#${r.toString(16).padStart(2, '0').toUpperCase()}${g
    .toString(16)
    .padStart(2, '0')
    .toUpperCase()}${b.toString(16).padStart(2, '0').toUpperCase()}${a
    .toString(16)
    .padStart(2, '0')
    .toUpperCase()}`
}

export type DataUriType = {
  mediaType: string | null
  subType: string | null
  charset: string | null
  encoding: string | null
  data: string | null
}

export function decomposeDataUri(dataUri: string): DataUriType {
  const match = DATA_URI.exec(dataUri)
  const mediaType = (match && match.length > 1 && match[1] && match[1].toLowerCase()) || null
  const subType = (match && match.length > 2 && match[2] && match[2].toLowerCase()) || null
  const charset = (match && match.length > 3 && match[3] && match[3].toLowerCase()) || null
  const encoding = (match && match.length > 4 && match[4] && match[4].toLowerCase()) || null
  const data = (match && match.length > 5 && match[5]) || null
  return { mediaType, subType, charset, encoding, data }
}

export function isDataUri(dataUri: string) {
  return DATA_URI.test(dataUri)
}

export function getUrlFileExtension(url: string): string | null {
  const match = URL_FILE_EXTENSION.exec(url)
  return (match && match.length > 1 && match[1].toLowerCase()) || null
}

export function getSvgSize(svgString: string): { [key: string]: number } {
  const match = SVG_SIZE.exec(svgString)
  const size: { [key: string]: number } = {}
  if (match && match.length > 7) {
    size[match[1]] = Math.round(parseFloat(match[3]))
    size[match[5]] = Math.round(parseFloat(match[7]))
  }

  return size
}

export function getResolutionOfUrl(url: string, defaultValue?: number): number {
  const resolution = RETINA_PREFIX.exec(url)

  if (resolution) {
    return parseFloat(resolution[1])
  }

  return defaultValue !== undefined ? defaultValue : 1
}

export const TextureCache = Object.create(null)
export const BaseTextureCache = Object.create(null)

export function destroyTextureCache(): void {
  let key
  for (key in TextureCache) {
    TextureCache[key].destroy()
  }
  for (key in BaseTextureCache) {
    BaseTextureCache[key].destroy()
  }
}

export function clearTextureCache(): void {
  let key

  for (key in TextureCache) {
    delete TextureCache[key]
  }
  for (key in BaseTextureCache) {
    delete BaseTextureCache[key]
  }
}

let tempAnchor: any

export function determineCrossOrigin(url: string, loc?: Location) {
  // data: and javascript: urls are considered same-origin
  if (url.indexOf('data:') === 0) {
    return ''
  }

  // default is window.location
  loc = loc || window.location

  if (!tempAnchor) {
    tempAnchor = document.createElement('a')
  }

  // let the browser determine the full href for the url of this resource and then
  // parse with the node url lib, we can't use the properties of the anchor element
  // because they don't work in IE9 :(
  tempAnchor.href = url
  const urls = new URL(tempAnchor.href)

  const samePort = (!urls.port && loc.port === '') || urls.port === loc.port

  // if cross origin
  if (urls.hostname !== loc.hostname || !samePort || urls.protocol !== loc.protocol) {
    return 'anonymous'
  }

  return ''
}

export function isString(x: any): x is string {
  return typeof x === 'string'
}

export function oneOf(value: any, validList: any[]): boolean {
  for (let i = 0; i < validList.length; i++) {
    if (value === validList[i]) {
      return true
    }
  }
  return false
}

const SPECIAL_CHARS_REGEXP = /([\:\-\_]+(.))/g
const MOZ_HACK_REGEXP = /^moz([A-Z])/

function camelCase(name: string) {
  return name
    .replace(SPECIAL_CHARS_REGEXP, function (_, separator, letter, offset) {
      return offset ? letter.toUpperCase() : letter
    })
    .replace(MOZ_HACK_REGEXP, 'Moz$1')
}

export function getStyle(element?: HTMLElement, styleName?: any) {
  if (!element || !styleName) return null
  styleName = camelCase(styleName)
  if (styleName === 'float') {
    styleName = 'cssFloat'
  }
  try {
    const computed = document.defaultView?.getComputedStyle(element, '')
    return element.style[styleName] || (computed ? computed[styleName] : null)
  } catch (e) {
    return element.style[styleName]
  }
}

export class UDate extends Date {
  /*
   * eg:format="yyyy-MM-dd hh:mm:ss";
   */
  format(format: string) {
    const o: Record<string, any> = {
      'M+': this.getMonth() + 1, // month
      'd+': this.getDate(), // day
      'h+': this.getHours(), // hour
      'm+': this.getMinutes(), // minute
      's+': this.getSeconds(), // second
      'q+': Math.floor((this.getMonth() + 3) / 3), // quarter
      'S+': this.getMilliseconds()
    }

    if (/(y+)/.test(format)) {
      format = format.replace(RegExp.$1, (this.getFullYear() + '').substr(4 - RegExp.$1.length))
    }

    for (const k in o) {
      if (new RegExp('(' + k + ')').test(format)) {
        let formatStr = ''
        for (let i = 1; i <= RegExp.$1.length; i++) {
          formatStr += '0'
        }

        let replaceStr = ''
        if (RegExp.$1.length == 1) {
          replaceStr = o[k]
        } else {
          formatStr = formatStr + o[k]
          const index = ('' + o[k]).length
          formatStr = formatStr.substr(index)
          replaceStr = formatStr
        }
        format = format.replace(RegExp.$1, replaceStr)
      }
    }
    return format
  }
}

export interface ClientAgent {
  agent: string
  trident: boolean
  presto: boolean
  webKit: boolean
  gecko: boolean
  mobile: boolean
  ios: boolean
  android: boolean
  iPhone: boolean
  iPad: boolean
  iPod: boolean
  webApp: boolean
  pc: boolean
}

let __agent: ClientAgent
export const getAgent = () => {
  if (!__agent) {
    const agent = navigator.userAgent
    __agent = {
      agent,
      trident: agent.indexOf('Trident') > -1, // IE
      presto: agent.indexOf('Presto') > -1, // opera
      webKit: agent.indexOf('AppleWebKit') > -1, // apple&google kernel
      gecko: agent.indexOf('Gecko') > -1 && agent.indexOf('KHTML') === -1, // firfox
      mobile: !!agent.match(/AppleWebKit.*Mobile.*/), // is Mobile
      ios: !!agent.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/), // is ios
      android: agent.indexOf('Android') > -1 || agent.indexOf('Adr') > -1, // android
      iPhone: agent.indexOf('iPhone') > -1, // iPhone or QQHD
      iPad: agent.indexOf('iPad') > -1, // iPad
      iPod: agent.indexOf('iPod') > -1, // iPod
      webApp: agent.indexOf('Safari') === -1, // is webapp,no header and footer
      pc: !/Android|iPhone|SymbianOS|Windows Phone|iPad|iPod/i.test(agent)
    }
  }
  return __agent
}

let transferIndex = 0

function transferIncrease() {
  return transferIndex++
}

export const setWuiSwitchAttrs = ({
  onColor = '#53c41a',
  offColor = '#d4d3d1'
}: {
  onColor?: string
  offColor?: string
} = {}) => ({
  style: {
    '--wui-switch-on-color': onColor,
    '--wui-switch-off-color': offColor
  }
})

/**
 * @description 判断是否升序
 * @export
 * @param {((number)[])} arr
 * @return {*} {valid: 是否通过, line?: 不通过的item行, value?: 不通过的value值}
 */
export function isAscending(arr: number[]) {
  for (let i = 1; i < arr.length; i++) {
    if (arr[i] < arr[i - 1]) {
      return {
        valid: false,
        line: i + 1,
        value: arr[i]
      }
    }
  }
  return {
    valid: true
  }
}

/**
 * @description 判断是否每项都相等
 * @export
 * @param {((number)[])} arr
 * @return {*} {valid: 是否通过, line?: 不通过的item行, value?: 不通过的value值}
 */
export const isEqual = (arr: number[]) => {
  let value
  let line = -1
  const valid = arr.every((item, index) => {
    if (item === arr[0]) return true
    line = index + 1
    value = item
    return false
  })
  return valid
    ? {
        valid: true
      }
    : {
        valid,
        value,
        line
      }
}

export const getFormatToEnDate = (date = new Date(), locale = 'en-GB') => {
  const formatter = new Intl.DateTimeFormat(locale, {
    day: '2-digit',
    month: 'short',
    year: 'numeric'
  })
  return formatter.format(date)
}

export { transferIndex, transferIncrease }

export { default as Browser } from './Browser'
