export class LinkMap<K, V, D> {
  private _vals: Map<K, V>
  private _keys: Map<V, K>
  private _datas: Map<K, D>

  constructor() {
    this._vals = new Map()
    this._keys = new Map()
    this._datas = new Map()
  }

  public put(key: K, val: V, data?: D) {
    this._vals.set(key, val)
    this._keys.set(val, key)
    data && this._datas.set(key, data)
  }

  public remove(key: K, val: V) {
    this._vals.delete(key)
    this._datas.delete(key)
    this._keys.delete(val)
  }

  public forValue(key: K) {
    return this._vals.get(key)
  }

  public forKey(val: V) {
    return this._keys.get(val)
  }

  public forData(key: K) {
    return this._datas.get(key)
  }

  public list() {
    return Array.from(this._vals.values())
  }

  public clear() {
    this._vals.clear()
    this._keys.clear()
  }
}
