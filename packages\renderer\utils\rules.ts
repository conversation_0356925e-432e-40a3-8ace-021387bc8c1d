import { FormItemRule } from '@wuk/wui'
import { Ref } from 'vue'
import { isAscending, isEqual } from '.'
/**
 * @description 校验是否为stringnumber/number类型 - 整数
 */
export const validStrNumIntOrNumInt: FormItemRule['validator'] = (
  rule,
  value,
  callback,
  source,
  options
) => {
  value = Number(value)
  if (value && !Number.isInteger(value)) {
    callback(new Error('Please enter a positive integer'))
  } else {
    callback()
  }
}

/**
 * @description 范围
 */
export const validRange = (min: number, max = Infinity): FormItemRule['validator'] => {
  return (rule, value, callback, source, options) => {
    if (!value && value != 0) callback()
    if (value < min || value > max) {
      if (max === Infinity) {
        callback(new Error(`Please enter a number greater than or equal to ${min}`))
      } else {
        callback(new Error(`Please enter a number between ${min} and ${max}`))
      }
    } else {
      callback()
    }
  }
}

/**
 * @description 验证是否为表达式
 */
export const validExp = (str: string): boolean => {
  const allowedOperators = /^(?:[a-zA-Z0-9\s]+|(?:[a-zA-Z0-9\s]+[&|><=][a-zA-Z0-9\s]+)+)$/
  return allowedOperators.test(str.trim())
}

/**
 * @description 验证是否为正数
 */
export const validNum: FormItemRule['validator'] = (rule, value, callback, source, options) => {
  if (!value && value != 0) callback()
  if (!/^(\-|\+)?\d+(\.\d+)?$/.test(value)) {
    callback(new Error('Please enter a number'))
  } else {
    callback()
  }
}

/**
 * @description 接口校验
 */
export const validApi = (errors: Record<string, string>): FormItemRule['validator'] => {
  return (rule, value, callback, source, options) => {
    const msg = errors[rule.field || '']
    if (msg) {
      callback(new Error(msg))
    } else {
      callback()
    }
  }
}

export const setRuleErrorCallback = (
  errors: Ref<Record<string, string>>,
  key: string,
  value = ''
) => {
  if (!value) {
    delete errors.value[key]
    return
  }
  errors.value[key] = value
}

/**
 * @description
 * 1、校验每行维度是否正确
 * 2、校验每行数据点是否为数字
 * 3、x: 2d、3d --- 升序、可以相等
 * 4、3d：
 * 4.1、x轴相等时不校验y轴
 * 4.2、x轴不等时则校验y轴必须相等
 * @param str 代码
 * @param dim 维度
 * @returns
 */
export function validCorrDataCodeTxt(str: string, dim: number, isCheck = true) {
  const lines = str.trim().split('\n')
  const numberRegex = /^-?(\d+(\.\d*)?|\.\d+)$/
  const data: number[] = []
  const corrData: { coord1: number | null; coord2: number | null; coord3?: number | null }[] = []
  const xArr = []
  const yArr = []
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim()
    if (!line) continue // 跳过空行

    const dataItems = line.split(/\s+/).filter(item => item !== '')

    // 1. 检查每个数据项是否为有效数字
    for (const [index, item] of dataItems.entries()) {
      if (isCheck && !numberRegex.test(item)) {
        return {
          valid: false,
          message: `Incorrect number of values entered on row ${i + 1}`
        }
      }
      const val = Number(item)
      if (!corrData[i]) {
        corrData[i] = {
          coord1: null,
          coord2: null
        }
      }
      if (index === 0) {
        corrData[i].coord1 = val
      } else if (index === 1) {
        corrData[i].coord2 = val
      } else if (index === 2) {
        corrData[i].coord3 = val
      }
      data.push(val)
    }

    // 2. 检查数据项数量是否超过维度
    if (isCheck && dataItems.length > dim) {
      return {
        valid: false,
        message: `Incorrect dimension on row ${i + 1}, the correct dimension is ${dim}`
      }
    }

    // x轴集合
    xArr.push(Number(dataItems[dim - 1]))
    // y轴集合
    dim === 3 && yArr.push(Number(dataItems[1]))
  }
  if (isCheck) {
    const { valid, value, line } = isAscending(xArr)
    if (!valid) {
      return {
        valid,
        message: `X Input value of '${value}' on line ${line} is not in ascending order`
      }
    }
    if (dim === 3) {
      const { valid: XValid } = isEqual(xArr)
      if (XValid) {
        return {
          valid: true,
          message: 'success',
          data,
          corrData
        }
      }
      const { valid: yValid, line, value } = isEqual(yArr)
      if (!yValid) {
        return {
          valid: false,
          message: `Y Input value of '${value}' on line ${line} is unexpected should be ${yArr[0]}`
        }
      }
    }
  }
  return {
    valid: true,
    message: 'success',
    data,
    corrData
  }
}
