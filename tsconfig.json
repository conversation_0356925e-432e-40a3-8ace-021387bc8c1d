{"compilerOptions": {"module": "commonjs", "target": "es5", "lib": ["WebWorker", "es6", "dom"], "sourceMap": false, "allowJs": true, "jsx": "preserve", "jsxImportSource": "vue", "moduleResolution": "node", "noImplicitReturns": true, "noImplicitThis": true, "noImplicitAny": true, "strictNullChecks": true, "allowSyntheticDefaultImports": true, "noEmit": true, "noFallthroughCasesInSwitch": true, "noUnusedParameters": false, "esModuleInterop": true, "noUnusedLocals": false, "allowImportingTsExtensions": true, "strict": true, "rootDir": ".", "baseUrl": ".", "paths": {"@/*": ["packages/*"]}, "experimentalDecorators": true, "skipLibCheck": true, "suppressImplicitAnyIndexErrors": true, "downlevelIteration": true}, "include": ["packages/"], "exclude": ["node_modules/", "**node_modules*", "dist", "**/*.js"]}