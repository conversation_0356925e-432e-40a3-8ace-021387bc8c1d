import { defineConfig } from 'vite'
import path from 'path'
import electPlugin from 'vite-plugin-electron/simple'
import { visualizer } from 'rollup-plugin-visualizer'
import vue from "@vitejs/plugin-vue"
import vueJsx from "@vitejs/plugin-vue-jsx"
import { chunkSplitPlugin } from 'vite-plugin-chunk-split'
import viteCompression from 'vite-plugin-compression'

const config = require('./package.json')

const NODE_ENV = process.env.NODE_ENV
const __DEV__ = NODE_ENV === 'development'
const __PROP__ = NODE_ENV === 'production'
const __VERSION__ = '"' + String(config.version || '') + '"'

export default defineConfig({
  build: {
    chunkSizeWarningLimit: 1500,
    target: 'es2015',
    outDir: path.join(__dirname, './dist/'),
    emptyOutDir: true,
    cssCodeSplit: true,
    minify: (__PROP__ && 'terser') || false,
    rollupOptions: {
      input: {
        login: path.resolve(__dirname, 'login.html'),
        index: path.resolve(__dirname, 'index.html'),
        dialog: path.resolve(__dirname, 'dialog.html')
      },
      output: {
        experimentalMinChunkSize: 500_1000,
        chunkFileNames: 'renderer/js/[name]-[hash].js',
        entryFileNames: 'renderer/js/[name]-[hash].js',
        assetFileNames: 'renderer/assets/[name]-[hash].[ext]'
      },
      treeshake: {
        manualPureFunctions: ['styled', 'styled.div', 'Object.freeze']
      }
    },
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true
      },
      output: {
        comments: true // 去掉注释
      }
    }
  },
  css: {
    preprocessorOptions: {
      scss: {
        charset: false,
      }
    }
  },
  define: {
    __DEV__,
    __PROP__,
    __VERSION__
  },
  resolve: {
    extensions: ['.ts', '.tsx', '.js', '.jsx'],
    alias: {
      '@': path.join(__dirname, './packages/')
    }
  },
  plugins: [
    vue(),
    // vue 可以使用 jsx/tsx 语法
    vueJsx(),
    visualizer({ open: !__PROP__ && !__DEV__ }),
    viteCompression({
      //生成压缩包gz
      verbose: true,
      disable: false,
      threshold: 10240,
      algorithm: 'gzip',
      ext: '.gz'
    }),
    electPlugin({
      main: {
        entry: 'packages/main/index.ts',
        vite: {
          build: {
            minify: (__PROP__ && 'terser') || false,
            outDir: 'dist/main'
          },
          resolve: {
            extensions: ['.ts', '.js'],
            alias: {
              '@': path.join(__dirname, './packages/')
            }
          }
        }
      },
      preload: {
        input: 'packages/preload/index.ts',
        vite: {
          build: {
            minify: (__PROP__ && 'terser') || false,
            outDir: 'dist/preload'
          },
          resolve: {
            extensions: ['.ts', '.js'],
            alias: {
              '@': path.join(__dirname, './packages/')
            }
          }
        }
      }
    }),
    chunkSplitPlugin({
      strategy: 'default',
      customChunk: ({
        id,
        moduleId
      }: {
        id: string
        moduleId: string
        file: string
        root: string
      }) => {
        const cssLangs = `\\.(css|less|sass|scss|styl|stylus|pcss|postcss)($|\\?)`
        const cssLangRE = new RegExp(cssLangs)
        const isCSSRequest = (request: string): boolean =>
          cssLangRE.test(request)
        // 分vendor包
        if (!isCSSRequest(id)) {
          if (id.includes('node_modules')) {
            return 'vendor'
          } else if (id.includes('packages/renderer')) {
            if (id.includes('renderer/assets/')) {
              return 'assets'
            } else if (
              (id.includes('renderer/boots/') ||
              id.includes('renderer/components/') ||
              id.includes('renderer/configs/') ||
              id.includes('renderer/hooks/') ||
              id.includes('renderer/logic/') ||
              id.includes('renderer/store/') ||
              id.includes('renderer/utils/')) &&
              !id.includes('/excuter.')
            ) {
              return 'manifest'
            } else if (id.includes('/excuter')) {
              return 'excuter'
            } else if (id.includes('renderer/page/')) {
              return 'page'
            }
          }
        }
      }
    })
  ]
})
